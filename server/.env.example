# Server Configuration
PORT=4000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here

# Encryption Configuration
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Primary Database Configuration (Main Operations)
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_primary_database_password
DB_DATABASE=payments

# Secondary Database Configuration (Analytics/Reporting)
# Optional: If not provided, will use primary database settings
DB_SECONDARY_HOST=localhost
DB_SECONDARY_USER=root
DB_SECONDARY_PASSWORD=your_secondary_database_password
DB_SECONDARY_DATABASE=payments_secondary

# Alternative Secondary Database Configurations:

# Same server, different database:
# DB_SECONDARY_DATABASE=payments_analytics

# Different server (read replica):
# DB_SECONDARY_HOST=replica-db.example.com
# DB_SECONDARY_USER=readonly_user
# DB_SECONDARY_PASSWORD=readonly_password
# DB_SECONDARY_DATABASE=payments

# Analytics server:
# DB_SECONDARY_HOST=analytics-db.example.com
# DB_SECONDARY_USER=analytics_user
# DB_SECONDARY_PASSWORD=analytics_password
# DB_SECONDARY_DATABASE=payments_analytics

# Email Configuration
EMAIL_HOST=smtp.office365.com
EMAIL_SERVICE=Outlook365
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password

# SMS Configuration (if needed)
# TWILIO_ACCOUNT_SID=your_twilio_account_sid
# TWILIO_AUTH_TOKEN=your_twilio_auth_token
# TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Redis Configuration (if using Redis for caching)
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=your_redis_password

# Logging Configuration
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH=./uploads

# External API Keys
# PLAID_CLIENT_ID=your_plaid_client_id
# PLAID_SECRET=your_plaid_secret
# PLAID_ENV=sandbox

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here
STRIPE_ENV=test

# Plaid Processing Configuration
# MIGRATION COMPLETE: System has been migrated to webhook-only processing
# Processing mode: 'webhook-only' (default/recommended), 'hybrid', 'cron-only' (deprecated)
# webhook-only: Real-time webhook processing only (RECOMMENDED - eliminates 5-10min delays)
# hybrid: Webhooks with cron job fallback (transition mode only)
# cron-only: Legacy cron job processing (DEPRECATED - use only for debugging)
PLAID_PROCESSING_MODE=webhook-only

# Webhook Configuration (PRIMARY processing method)
# Replace with your actual production domain
WEBHOOK_BASE_URL=https://your-production-domain.com
PLAID_WEBHOOK_SECRET=your_webhook_secret
PLAID_WEBHOOK_PRIORITY=true

# Enhanced Webhook Security Configuration
# Enable enhanced security validation (recommended for production)
PLAID_ENHANCED_SECURITY=true

# IP address validation (disabled by default due to Plaid IP changes)
# Enable only if you have updated Plaid IP ranges
PLAID_VALIDATE_IP=false

# Strict user agent validation (disabled by default for compatibility)
PLAID_STRICT_USER_AGENT=false

# Timestamp validation settings (in seconds)
# Maximum age for webhook timestamps (default: 300 seconds = 5 minutes)
PLAID_MAX_TIMESTAMP_AGE=300
# Minimum timestamp age to allow for clock skew (default: -30 seconds)
PLAID_MIN_TIMESTAMP_AGE=-30

# Rate limiting configuration
# Enable rate limiting for webhook endpoints
PLAID_RATE_LIMIT_ENABLED=true
# Maximum requests per minute per IP
PLAID_RATE_LIMIT_PER_MINUTE=100
# Maximum requests per hour per IP
PLAID_RATE_LIMIT_PER_HOUR=1000

# Security event logging
# Enable comprehensive security event logging
PLAID_LOG_SECURITY_EVENTS=true

# Security alert thresholds
# Failed validations per hour before alerting
PLAID_ALERT_FAILED_VALIDATIONS=50
# Suspicious IPs per hour before alerting
PLAID_ALERT_SUSPICIOUS_IPS=10
# Rate limit violations per hour before alerting
PLAID_ALERT_RATE_LIMIT_VIOLATIONS=20

# Cron Job Configuration (DISABLED by default - webhook-only processing active)
# MIGRATION: Cron jobs are now DISABLED by default and serve only as emergency fallback
# Only enable cron jobs for debugging, testing, or emergency fallback scenarios
# Setting PLAID_CRON_ENABLED=true will activate legacy polling (not recommended)
PLAID_CRON_ENABLED=false

# Fallback Configuration (Safety net for webhook failures)
# PLAID_CRON_FALLBACK_MODE: Automatically enable cron jobs if webhooks fail consistently
# This provides reliability without manual intervention
PLAID_CRON_FALLBACK_MODE=true

# Cron Job Intervals (only used when cron jobs are enabled)
# These settings are preserved for fallback scenarios but not used in webhook-only mode
PLAID_SYNC_INTERVAL=5
PLAID_SMART_SYNC_INTERVAL=10
PLAID_STATS_INTERVAL=60

# Webhook Health Monitoring (for fallback activation)
# WEBHOOK_FAILURE_THRESHOLD: Number of consecutive failures before activating fallback
# WEBHOOK_TIMEOUT_MINUTES: Minutes without webhook events before considering timeout
WEBHOOK_FAILURE_THRESHOLD=5
WEBHOOK_TIMEOUT_MINUTES=15
