import { Router } from 'express';
import { 
  handleStripeWebhook, 
  testStripeWebhook, 
  getStripeWebhookStats 
} from '../controllers/stripeWebhookController';

const router = Router();

/**
 * Stripe webhook endpoint
 * This endpoint receives webhook events from Stripe
 * Note: This should NOT use authentication middleware as it's called by Stripe
 */
router.post('/webhook', handleStripeWebhook);

/**
 * Test endpoint for debugging Stripe webhooks
 * This can be used during development to test webhook processing
 */
router.post('/webhook/test', testStripeWebhook);

/**
 * Get Stripe webhook processing statistics
 * This endpoint can be used to monitor webhook processing
 */
router.get('/webhook/stats', getStripeWebhookStats);

export default router;
