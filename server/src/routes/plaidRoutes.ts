import { Router } from 'express';
import {
  createLinkToken,
  exchangePublicToken,
  getAccounts,
  setPrimaryAccount,
  disconnectAccount,
  createStripePaymentMethodFromPlaid,
  getPlaidStripePaymentMethodStatus,
  debugGetBankAccounts
} from '../controllers/plaidController';
import { authenticateTokenAndSession } from '../middlewares/sessionAuth';
import { asyncHandler } from '../utils/asyncHandler';

const router = Router();

// Apply authentication middleware to all routes
router.use(authenticateTokenAndSession);

// Plaid Link Token routes
router.post('/link-token', asyncHandler(createLinkToken as any)); // Create link token for Plaid Link

// Plaid account management routes
router.post('/exchange-public-token', asyncHandler(exchangePublicToken as any)); // Exchange public token for access token
router.get('/accounts', asyncHandler(getAccounts as any)); // Get connected bank accounts
router.put('/accounts/:accountId/primary', asyncHandler(setPrimaryAccount as any)); // Set primary account
router.delete('/accounts/:accountId', asyncHandler(disconnectAccount as any)); // Disconnect bank account

// Plaid-Stripe integration routes
router.post('/create-stripe-payment-method', asyncHandler(createStripePaymentMethodFromPlaid as any)); // Create Stripe payment method from Plaid
router.get('/stripe-payment-method-status/:plaidAccountId', asyncHandler(getPlaidStripePaymentMethodStatus as any)); // Get payment method status

// Debug routes (no authentication required)
router.get('/debug/bank-accounts', debugGetBankAccounts); // Debug: Get bank accounts without auth

export default router;
