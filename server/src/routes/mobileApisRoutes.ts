import { Router ,Request,Response,NextFunction} from 'express';
import { listBankAccounts, addUpdateBankAccount,  listWallet, setUpPin, changeWalletPin, resetWalletPin, sendOtp, verifyOTP, setPrimaryAccount, disconnectAccount, validatePin } from '../controllers/appControllers/bankController';
import { addMoney, createLinkToken, getAuthToken, getPlaidKeys, withdrawMoney } from '../controllers/appControllers/walletController';
import { authenticateTokenAndSession } from '../middlewares/sessionAuth';
import { sendPinResetOTP } from '../controllers/walletController';
import { getTransacrionReportIssues, listTransactionDetail, listTransactionsHistory } from '../controllers/appControllers/transactionController';


const router = Router();
router.get('/get-auth-token', getAuthToken);
// All wallet routes require full authentication (JWT + session)
router.use(authenticateTokenAndSession);
router.get('/send-otp', sendOtp); // Send OTP for PIN reset
router.post('/verify-otp', verifyOTP); // Verify OTP for PIN reset
router.post('/add-update-bank', addUpdateBankAccount);
router.get('/list-bank-accounts', listBankAccounts);


// transactions
router.get('/transaction-history', listTransactionsHistory);
router.post('/transaction-detail', listTransactionDetail);

router.get('/get-wallet', listWallet);
router.post('/add-pin',setUpPin)
router.post('/validate-pin', validatePin)
router.post('/change-pin', changeWalletPin);
router.post('/send-pin-reset-otp', sendPinResetOTP);
router.post('/reset-pin', resetWalletPin);


router.post('/add-money', addMoney); // Requires PIN and uses primary bank
router.post('/withdraw', withdrawMoney); // Requires PIN and transfers to primary bank

router.get('/get-keys', getPlaidKeys)
router.post('/link-token', (req: Request, res: Response, next: NextFunction) => {
  createLinkToken(req, res).catch(next);
});


router.get('/accounts/:accountId/set-primary',  (req: Request, res: Response, next: NextFunction) => {
  setPrimaryAccount(req, res).catch(next);
});

// Disconnect account endpoint
router.get('/accounts/:accountId',  (req: Request, res: Response, next: NextFunction) => {
  disconnectAccount(req, res).catch(next);
});

router.get('/report-issues-list', getTransacrionReportIssues)


export default router;
