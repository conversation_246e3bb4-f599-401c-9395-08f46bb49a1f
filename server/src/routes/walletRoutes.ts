import express from 'express';
import {
  getWalletInfo,
  createWallet,
  changeWalletPin,
  sendPinChangeOTP,
  sendPinResetOTP,
  resetWalletPin,
  verifyWalletPin,
  getBalance,
  checkPinStatus,
  addMoney,
  withdrawMoney,
  getTransactionHistory,
  getEnhancedWalletInfo,
  getDashboardData,
  transferToWallet,
  searchUsers,
  checkUserWallet,
  getWithdrawalStatus,
  transferBankToBank,
  validateBankAccountForTransfer,
  checkBankAccountReadiness,
  getTransferStatusController,
  getComprehensiveTransferHistory,
  getTransferDetails,
  getTransferStatistics,
  handleFailedTransferRecovery,
  compensateUser,
  getFailedTransactionsForRecovery,
  runTransferFlowTests,
  validateIntegrations,
  migrateUsersToStripe,
  getWithdrawalProgress,
  setupMissingTables,
  ensureStripeIntegration,
  checkStripeIntegrationStatus,
  migrateAllUsersToStripeIntegration
} from '../controllers/walletController';
import { authenticateTokenAndSession } from '../middlewares/sessionAuth';

const router = express.Router();

// All wallet routes require full authentication (JWT + session)
router.use(authenticateTokenAndSession);

// Wallet management routes
router.get('/info', getWalletInfo);
router.get('/enhanced-info', getEnhancedWalletInfo); // Enhanced info with bank details
router.get('/dashboard', getDashboardData); // Dashboard data with stats and recent transactions
router.post('/create', createWallet); // Now includes PIN setup
router.get('/balance', getBalance);

// Transaction management routes
router.get('/transactions', getTransactionHistory);

// PIN management routes
router.get('/pin-status', checkPinStatus);
router.post('/send-pin-change-otp', sendPinChangeOTP);
router.post('/change-pin', changeWalletPin);
router.post('/send-pin-reset-otp', sendPinResetOTP);
router.post('/reset-pin', resetWalletPin);
router.post('/verify-pin', verifyWalletPin);

// Money management routes (now with PIN verification)
router.post('/add-money', addMoney); // Requires PIN and uses primary bank
router.post('/withdraw', withdrawMoney); // Requires PIN and transfers to primary bank
router.get('/withdrawal-status/:transactionId', getWithdrawalStatus); // Get real-time withdrawal status

// Wallet-to-wallet transfer routes
router.post('/transfer', transferToWallet); // Transfer money to another wallet
router.get('/search-users', searchUsers); // Search for transfer recipients
router.get('/check-user-wallet', checkUserWallet); // Check if user has wallet (debug endpoint)

// Bank-to-bank transfer routes
router.post('/transfer-bank-to-bank', transferBankToBank); // Direct bank-to-bank transfer
router.get('/validate-bank-account/:bankAccountId', validateBankAccountForTransfer); // Validate bank account for transfers
router.get('/check-bank-account-readiness/:bankAccountId', checkBankAccountReadiness); // Check if bank account is ready for transfers
router.get('/transfer-status/:transferId', getTransferStatusController); // Get transfer status

// Enhanced transfer history routes
router.get('/transfer-history', getComprehensiveTransferHistory); // Get comprehensive transfer history with filters
router.get('/transfer-details/:transferId', getTransferDetails); // Get detailed transfer information
router.get('/transfer-statistics', getTransferStatistics); // Get transfer statistics for dashboard

// Error handling and recovery routes
router.post('/recover-failed-transfer/:transactionId', handleFailedTransferRecovery); // Handle failed transfer recovery
router.post('/compensate-user', compensateUser); // Compensate user for system errors (admin only)
router.get('/failed-transactions', getFailedTransactionsForRecovery); // Get failed transactions for recovery (admin only)

// Testing and validation routes (development/testing only)
router.get('/test-transfer-flows', runTransferFlowTests); // Run transfer flow tests
router.get('/validate-integrations', validateIntegrations); // Validate Stripe and Plaid integration

// Migration routes (admin only)
router.post('/migrate-users-to-stripe', migrateUsersToStripe); // Migrate existing users to Stripe customers

// Withdrawal progress routes
router.get('/withdrawal-progress/:transactionId', getWithdrawalProgress); // Get withdrawal progress for transaction

// Database setup routes (development/testing only)
router.post('/setup-missing-tables', setupMissingTables); // Setup missing database tables

// Bank-Stripe integration routes
router.post('/ensure-stripe-integration', ensureStripeIntegration); // Ensure Stripe integration for user
router.get('/stripe-integration-status', checkStripeIntegrationStatus); // Check Stripe integration status
router.post('/migrate-all-users-stripe-integration', migrateAllUsersToStripeIntegration); // Migrate all users (admin only)

export default router;
