import logger from '../utils/logger';

/**
 * Enhanced webhook configuration with security features
 * This centralizes all webhook-related settings.
 */
export const WEBHOOK_CONFIG = {
  // Base URL for webhooks - using ngrok URL from environment
  BASE_URL: process.env.WEBHOOK_BASE_URL || 'https://fd8e2e2a9770.ngrok-free.app',
  
  // Webhook endpoint path
  WEBHOOK_PATH: '/api/webhook',
  
  // Full webhook URL
  get FULL_URL(): string {
    return `${this.BASE_URL}${this.WEBHOOK_PATH}`;
  },
  
  // Webhook secret for signature verification
  SECRET: process.env.WEBHOOK_SECRET,

  // Processing mode ('webhook-only', 'hybrid', 'cron-only')
  PROCESSING_MODE: process.env.PLAID_PROCESSING_MODE || 'webhook-only',

  // Webhook only mode
  WEBHOOK_ONLY_MODE: process.env.PLAID_PROCESSING_MODE === 'webhook-only',

  // Webhook priority
  WEBHOOK_PRIORITY: process.env.PLAID_WEBHOOK_PRIORITY === 'true',
  
  // Environment check
  get IS_PRODUCTION(): boolean {
    return process.env.NODE_ENV === 'production';
  },

  // Enhanced security configuration
  SECURITY: {
    // Enable enhanced security validation
    ENHANCED_VALIDATION: process.env.ENHANCED_SECURITY !== 'false',
    
    // Enable IP validation
    VALIDATE_IP: process.env.VALIDATE_IP === 'true',
    
    // Enable strict user agent validation
    STRICT_USER_AGENT: process.env.STRICT_USER_AGENT === 'true',
    
    // Timestamp validation settings
    MAX_TIMESTAMP_AGE: parseInt(process.env.MAX_TIMESTAMP_AGE || '300'), // 5 minutes in seconds
    MIN_TIMESTAMP_AGE: parseInt(process.env.MIN_TIMESTAMP_AGE || '-30'), // 30 seconds in the future
    
    // Rate limiting settings
    RATE_LIMIT: {
      ENABLED: process.env.RATE_LIMIT_ENABLED !== 'false',
      MAX_PER_MINUTE: parseInt(process.env.RATE_LIMIT_PER_MINUTE || '100'),
      MAX_PER_HOUR: parseInt(process.env.RATE_LIMIT_PER_HOUR || '1000')
    },
    
    // Security headers validation
    REQUIRED_HEADERS: [
      'verification-header',
      'content-type',
      'user-agent'
    ],
    
    // Trusted IP ranges
    TRUSTED_IP_RANGES: [],
    
    // Security event logging
    LOG_SECURITY_EVENTS: process.env.LOG_SECURITY_EVENTS !== 'false',
    
    // Alert thresholds
    ALERT_THRESHOLDS: {
      FAILED_VALIDATIONS_PER_HOUR: parseInt(process.env.ALERT_FAILED_VALIDATIONS || '50'),
      SUSPICIOUS_IPS_PER_HOUR: parseInt(process.env.ALERT_SUSPICIOUS_IPS || '10'),
      RATE_LIMIT_VIOLATIONS_PER_HOUR: parseInt(process.env.ALERT_RATE_LIMIT_VIOLATIONS || '20')
    }
  },
  
  // Validate webhook configuration including security settings
  validateWebhookConfig(): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Basic configuration validation
    if (!this.BASE_URL || this.BASE_URL === 'https://your-app.com') {
      errors.push('WEBHOOK_BASE_URL is not configured. Please set it to your production domain.');
    }
    
    if (!this.SECRET && this.IS_PRODUCTION) {
      errors.push('WEBHOOK_SECRET is not configured for production.');
    }
    
    if (!this.BASE_URL.startsWith('https://')) {
      errors.push('WEBHOOK_BASE_URL must use HTTPS for production.');
    }
    
    // Security configuration validation
    if (this.IS_PRODUCTION) {
      if (!this.SECURITY.ENHANCED_VALIDATION) {
        warnings.push('Enhanced security validation is disabled in production');
      }
      
      if (!this.SECURITY.RATE_LIMIT.ENABLED) {
        warnings.push('Rate limiting is disabled in production');
      }
      
      if (this.SECURITY.MAX_TIMESTAMP_AGE > 600) { // 10 minutes
        warnings.push('MAX_TIMESTAMP_AGE is set too high (>10 minutes), may allow replay attacks');
      }
      
      if (this.SECURITY.RATE_LIMIT.MAX_PER_MINUTE > 200) {
        warnings.push('Rate limit per minute is set very high, may not protect against abuse');
      }
      
      if (!this.SECURITY.LOG_SECURITY_EVENTS) {
        warnings.push('Security event logging is disabled in production');
      }
    }
    
    // Validate timestamp age settings
    if (this.SECURITY.MAX_TIMESTAMP_AGE < 60) {
      warnings.push('MAX_TIMESTAMP_AGE is set very low (<1 minute), may cause legitimate webhooks to be rejected');
    }
    
    if (this.SECURITY.MIN_TIMESTAMP_AGE > 0) {
      warnings.push('MIN_TIMESTAMP_AGE should be negative to allow for clock skew');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  },
  
  // Log current webhook configuration including security settings
  logConfig(): void {
    const validation = this.validateWebhookConfig();
    
    logger.info('Enhanced webhook configuration', {
      baseUrl: this.BASE_URL,
      fullUrl: this.FULL_URL,
      hasSecret: !!this.SECRET,
      isProduction: this.IS_PRODUCTION,
      isValid: validation.isValid,
      security: {
        enhancedValidation: this.SECURITY.ENHANCED_VALIDATION,
        validateIP: this.SECURITY.VALIDATE_IP,
        strictUserAgent: this.SECURITY.STRICT_USER_AGENT,
        rateLimitEnabled: this.SECURITY.RATE_LIMIT.ENABLED,
        maxTimestampAge: this.SECURITY.MAX_TIMESTAMP_AGE,
        logSecurityEvents: this.SECURITY.LOG_SECURITY_EVENTS
      }
    });
    
    if (!validation.isValid) {
      logger.error('Webhook configuration errors', {
        errors: validation.errors
      });
    }
    
    if (validation.warnings.length > 0) {
      logger.warn('Webhook configuration warnings', {
        warnings: validation.warnings
      });
    }
  }
};
