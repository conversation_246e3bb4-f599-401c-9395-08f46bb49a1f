import Stripe from 'stripe';
import logger from '../utils/logger';

/**
 * Stripe configuration service for payment processing
 * Handles Stripe client initialization, environment management, and configuration validation
 */

// Environment variables with defaults for development
export const stripeSecretKey = process.env.STRIPE_SECRET_KEY || 'sk_test_your_stripe_secret_key_here';
export const stripePublishableKey = process.env.STRIPE_PUBLISHABLE_KEY || 'pk_test_your_stripe_publishable_key_here';
export const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET || 'whsec_your_stripe_webhook_secret_here';
export const stripeEnv = process.env.STRIPE_ENV || 'test'; // 'test' for development, 'live' for production

/**
 * Determine if we're using live Stripe keys
 */
export const isLiveMode = (): boolean => {
  return stripeEnv === 'live' || stripeSecretKey.startsWith('sk_live_');
};

/**
 * Stripe client configuration
 */
const stripeConfig: Stripe.StripeConfig = {
  apiVersion: '2025-07-30.basil', // Use latest stable API version
  typescript: true,
  telemetry: false, // Disable telemetry for privacy
};

// Initialize Stripe client
export const stripeClient = new Stripe(stripeSecretKey, stripeConfig);

/**
 * Stripe configuration object with validation and utilities
 */
export const STRIPE_CONFIG = {
  // API Keys
  SECRET_KEY: stripeSecretKey,
  PUBLISHABLE_KEY: stripePublishableKey,
  WEBHOOK_SECRET: stripeWebhookSecret,
  ENVIRONMENT: stripeEnv,
  
  // Environment checks
  get IS_LIVE_MODE(): boolean {
    return isLiveMode();
  },
  
  get IS_TEST_MODE(): boolean {
    return !this.IS_LIVE_MODE;
  },
  
  get IS_PRODUCTION(): boolean {
    return process.env.NODE_ENV === 'production';
  },
  
  // API Configuration
  API_VERSION: stripeConfig.apiVersion,
  
  // Webhook configuration
  WEBHOOK: {
    SECRET: stripeWebhookSecret,
    TOLERANCE: 300, // 5 minutes tolerance for webhook timestamps
    
    // Webhook endpoint path
    ENDPOINT_PATH: '/api/stripe/webhook',
    
    // Get full webhook URL
    getWebhookUrl(): string {
      const baseUrl = process.env.WEBHOOK_BASE_URL || 'https://your-production-domain.com';
      return `${baseUrl}${this.ENDPOINT_PATH}`;
    }
  },
  
  // Payment configuration
  PAYMENT: {
    // Default currency
    DEFAULT_CURRENCY: 'usd',
    
    // Minimum charge amount (in cents)
    MIN_CHARGE_AMOUNT: 50, // $0.50
    
    // Maximum charge amount (in cents) - can be adjusted based on business needs
    MAX_CHARGE_AMOUNT: *********, // $1,000,000
    
    // Payment method types to accept
    ACCEPTED_PAYMENT_METHODS: ['us_bank_account'],
    
    // ACH configuration
    ACH: {
      // ACH debit verification method
      VERIFICATION_METHOD: 'automatic' as const,
      
      // ACH setup future usage
      SETUP_FUTURE_USAGE: 'off_session' as const
    }
  },
  
  // Payout configuration
  PAYOUT: {
    // Default currency for payouts
    DEFAULT_CURRENCY: 'usd',
    
    // Minimum payout amount (in cents)
    MIN_PAYOUT_AMOUNT: 100, // $1.00
    
    // Maximum payout amount (in cents)
    MAX_PAYOUT_AMOUNT: *********, // $1,000,000
    
    // Payout method
    METHOD: 'standard' as const, // 'standard' or 'instant'
    
    // Payout description template
    DESCRIPTION_TEMPLATE: 'Wallet withdrawal - {userId}'
  },
  
  // Customer configuration
  CUSTOMER: {
    // Customer description template
    DESCRIPTION_TEMPLATE: 'Wallet user - {userId}',
    
    // Default customer metadata
    DEFAULT_METADATA: {
      source: 'wallet_integration',
      created_by: 'stripe_plaid_integration'
    }
  },
  
  // Error handling configuration
  ERROR_HANDLING: {
    // Maximum retry attempts for failed operations
    MAX_RETRY_ATTEMPTS: 3,
    
    // Retry delay in milliseconds
    RETRY_DELAY: 1000,
    
    // Exponential backoff multiplier
    BACKOFF_MULTIPLIER: 2
  },
  
  /**
   * Validate Stripe configuration
   */
  validate(): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Validate API keys
    if (!this.SECRET_KEY || this.SECRET_KEY === 'sk_test_your_stripe_secret_key_here') {
      errors.push('STRIPE_SECRET_KEY is not configured properly');
    }
    
    if (!this.PUBLISHABLE_KEY || this.PUBLISHABLE_KEY === 'pk_test_your_stripe_publishable_key_here') {
      errors.push('STRIPE_PUBLISHABLE_KEY is not configured properly');
    }
    
    if (!this.WEBHOOK_SECRET || this.WEBHOOK_SECRET === 'whsec_your_stripe_webhook_secret_here') {
      if (this.IS_PRODUCTION) {
        errors.push('STRIPE_WEBHOOK_SECRET is not configured for production');
      } else {
        warnings.push('STRIPE_WEBHOOK_SECRET is not configured (required for webhooks)');
      }
    }
    
    // Validate key consistency
    const secretKeyMode = this.SECRET_KEY.startsWith('sk_live_') ? 'live' : 'test';
    const publishableKeyMode = this.PUBLISHABLE_KEY.startsWith('pk_live_') ? 'live' : 'test';
    
    if (secretKeyMode !== publishableKeyMode) {
      errors.push('Stripe secret key and publishable key are from different environments');
    }
    
    // Production environment checks
    if (this.IS_PRODUCTION) {
      if (this.IS_TEST_MODE) {
        warnings.push('Using Stripe test keys in production environment');
      }
      
      if (!this.WEBHOOK_SECRET) {
        errors.push('Webhook secret is required in production');
      }
    }
    
    // Development environment checks
    if (!this.IS_PRODUCTION && this.IS_LIVE_MODE) {
      warnings.push('Using Stripe live keys in development environment');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  },
  
  /**
   * Log current Stripe configuration
   */
  logConfig(): void {
    const validation = this.validate();
    
    logger.info('Stripe configuration initialized', {
      environment: this.ENVIRONMENT,
      isLiveMode: this.IS_LIVE_MODE,
      isTestMode: this.IS_TEST_MODE,
      isProduction: this.IS_PRODUCTION,
      apiVersion: this.API_VERSION,
      hasSecretKey: !!this.SECRET_KEY && this.SECRET_KEY !== 'sk_test_your_stripe_secret_key_here',
      hasPublishableKey: !!this.PUBLISHABLE_KEY && this.PUBLISHABLE_KEY !== 'pk_test_your_stripe_publishable_key_here',
      hasWebhookSecret: !!this.WEBHOOK_SECRET && this.WEBHOOK_SECRET !== 'whsec_your_stripe_webhook_secret_here',
      webhookUrl: this.WEBHOOK.getWebhookUrl(),
      isValid: validation.isValid
    });
    
    if (!validation.isValid) {
      logger.error('Stripe configuration errors', {
        errors: validation.errors
      });
    }
    
    if (validation.warnings.length > 0) {
      logger.warn('Stripe configuration warnings', {
        warnings: validation.warnings
      });
    }
  }
};

/**
 * Validate Stripe configuration on startup
 */
export function validateStripeConfig(): void {
  STRIPE_CONFIG.logConfig();
  
  const validation = STRIPE_CONFIG.validate();
  if (!validation.isValid) {
    logger.error('Stripe configuration is invalid', {
      errors: validation.errors
    });
    
    if (STRIPE_CONFIG.IS_PRODUCTION) {
      throw new Error(`Stripe configuration errors: ${validation.errors.join(', ')}`);
    }
  }
}

/**
 * Get Stripe webhook URL
 */
export function getStripeWebhookUrl(): string {
  return STRIPE_CONFIG.WEBHOOK.getWebhookUrl();
}

/**
 * Check if amount is within valid range for charges
 */
export function isValidChargeAmount(amount: number): boolean {
  return amount >= STRIPE_CONFIG.PAYMENT.MIN_CHARGE_AMOUNT && 
         amount <= STRIPE_CONFIG.PAYMENT.MAX_CHARGE_AMOUNT;
}

/**
 * Check if amount is within valid range for payouts
 */
export function isValidPayoutAmount(amount: number): boolean {
  return amount >= STRIPE_CONFIG.PAYOUT.MIN_PAYOUT_AMOUNT && 
         amount <= STRIPE_CONFIG.PAYOUT.MAX_PAYOUT_AMOUNT;
}

/**
 * Format amount for Stripe (convert dollars to cents)
 */
export function formatAmountForStripe(dollarAmount: number): number {
  return Math.round(dollarAmount * 100);
}

/**
 * Format amount from Stripe (convert cents to dollars)
 */
export function formatAmountFromStripe(centAmount: number): number {
  return centAmount / 100;
}