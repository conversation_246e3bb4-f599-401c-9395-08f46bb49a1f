import { executeQuery, executeQ<PERSON>y<PERSON><PERSON><PERSON>, executeUpdate } from '../utils/database';
import logger from '../utils/logger';

export interface WithdrawalStep {
  id: number;
  step_key: string;
  title: string;
  description: string;
  step_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface WithdrawalProgress {
  id: number;
  transaction_id: number;
  step_key: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
  completed_at: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
}

export interface WithdrawalProgressWithStep {
  step_key: string;
  title: string;
  description: string;
  step_order: number;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
  completed_at: string | null;
  notes: string | null;
}

/**
 * Service for managing withdrawal progress tracking
 */
export class WithdrawalProgressService {

  /**
   * Initialize withdrawal progress for a new transaction
   */
  static async initializeWithdrawalProgress(transactionId: number): Promise<boolean> {
    try {
      logger.info('Initializing withdrawal progress', { transactionId });

      // Create initial step (initiated)
      await executeUpdate(
        `INSERT INTO tbl_withdrawal_progress (transaction_id, step_key, status, completed_at)
         VALUES (?, 'initiated', 'completed', NOW())
         ON DUPLICATE KEY UPDATE
         status = 'completed',
         completed_at = NOW(),
         updated_at = NOW()`,
        [transactionId]
      );

      logger.info('Withdrawal progress initialized successfully', { transactionId });
      return true;

    } catch (error) {
      logger.error('Error initializing withdrawal progress', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId
      });
      return false;
    }
  }

  /**
   * Update withdrawal progress step
   */
  static async updateWithdrawalStep(
    transactionId: number,
    stepKey: string,
    status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped',
    notes?: string
  ): Promise<boolean> {
    try {
      logger.info('Updating withdrawal step', { transactionId, stepKey, status });

      const completedAt = status === 'completed' ? 'NOW()' : 'NULL';

      await executeUpdate(
        `INSERT INTO tbl_withdrawal_progress (transaction_id, step_key, status, completed_at, notes)
         VALUES (?, ?, ?, ${completedAt}, ?)
         ON DUPLICATE KEY UPDATE
         status = VALUES(status),
         completed_at = VALUES(completed_at),
         notes = VALUES(notes),
         updated_at = NOW()`,
        [transactionId, stepKey, status, notes || null]
      );

      logger.info('Withdrawal step updated successfully', { transactionId, stepKey, status });
      return true;

    } catch (error) {
      logger.error('Error updating withdrawal step', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId,
        stepKey,
        status
      });
      return false;
    }
  }

  /**
   * Get withdrawal progress for a transaction
   */
  static async getWithdrawalProgress(transactionId: number): Promise<WithdrawalProgressWithStep[]> {
    try {
      const progress = await executeQuery(
        `SELECT
          wp.step_key,
          ws.title,
          ws.description,
          ws.step_order,
          wp.status,
          wp.completed_at,
          wp.notes
         FROM tbl_withdrawal_progress wp
         JOIN tbl_withdrawal_steps ws ON wp.step_key = ws.step_key
         WHERE wp.transaction_id = ? AND ws.is_active = 1
         ORDER BY wp.created_at ASC`,
        [transactionId]
      );

      const progressArray = Array.isArray(progress) ? progress : (progress ? [progress] : []);
      
      return progressArray.map(p => ({
        step_key: p.step_key,
        title: p.title,
        description: p.description,
        step_order: p.step_order,
        status: p.status,
        completed_at: p.completed_at,
        notes: p.notes
      }));

    } catch (error) {
      logger.error('Error getting withdrawal progress', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId
      });
      return [];
    }
  }

  /**
   * Get all withdrawal steps (template)
   */
  static async getWithdrawalSteps(): Promise<WithdrawalStep[]> {
    try {
      const steps = await executeQuery(
        'SELECT * FROM tbl_withdrawal_steps WHERE is_active = 1 ORDER BY step_order ASC'
      );

      const stepsArray = Array.isArray(steps) ? steps : (steps ? [steps] : []);
      return stepsArray as WithdrawalStep[];

    } catch (error) {
      logger.error('Error getting withdrawal steps', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return [];
    }
  }

  /**
   * Process successful withdrawal (complete all remaining steps)
   */
  static async processSuccessfulWithdrawal(transactionId: number): Promise<boolean> {
    try {
      logger.info('Processing successful withdrawal', { transactionId });

      // Update validation step
      await this.updateWithdrawalStep(transactionId, 'validation', 'completed', 'Account validated successfully');

      // Update processing step
      await this.updateWithdrawalStep(transactionId, 'processing', 'completed', 'Payment processed via Stripe');

      // Update bank transfer step
      await this.updateWithdrawalStep(transactionId, 'bank_transfer', 'completed', 'Funds transferred to bank account');

      // Update completed step
      await this.updateWithdrawalStep(transactionId, 'completed', 'completed', 'Withdrawal completed successfully');

      logger.info('Successful withdrawal processed', { transactionId });
      return true;

    } catch (error) {
      logger.error('Error processing successful withdrawal', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId
      });
      return false;
    }
  }

  /**
   * Process failed withdrawal
   */
  static async processFailedWithdrawal(
    transactionId: number,
    failedStep: string,
    reason: string
  ): Promise<boolean> {
    try {
      logger.info('Processing failed withdrawal', { transactionId, failedStep, reason });

      // Mark the failed step
      await this.updateWithdrawalStep(transactionId, failedStep, 'failed', reason);

      // Skip remaining steps
      const remainingSteps = ['validation', 'processing', 'bank_transfer', 'completed'];
      const failedStepIndex = remainingSteps.indexOf(failedStep);

      if (failedStepIndex >= 0) {
        for (let i = failedStepIndex + 1; i < remainingSteps.length; i++) {
          await this.updateWithdrawalStep(
            transactionId, 
            remainingSteps[i], 
            'skipped', 
            'Skipped due to previous step failure'
          );
        }
      }

      logger.info('Failed withdrawal processed', { transactionId, failedStep });
      return true;

    } catch (error) {
      logger.error('Error processing failed withdrawal', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId,
        failedStep
      });
      return false;
    }
  }

  /**
   * Get withdrawal status summary
   */
  static async getWithdrawalStatus(transactionId: number): Promise<{
    overall: 'pending' | 'completed' | 'failed';
    currentStep: string;
    currentStepTitle: string;
    currentStepDescription: string;
    lastUpdated: string;
    progress: {
      completed: number;
      total: number;
      percentage: number;
    };
  } | null> {
    try {
      const progress = await this.getWithdrawalProgress(transactionId);

      if (progress.length === 0) {
        return null;
      }

      // Calculate overall status
      const hasFailedStep = progress.some(p => p.status === 'failed');
      const allCompleted = progress.every(p => p.status === 'completed' || p.status === 'skipped');
      
      let overall: 'pending' | 'completed' | 'failed';
      if (hasFailedStep) {
        overall = 'failed';
      } else if (allCompleted) {
        overall = 'completed';
      } else {
        overall = 'pending';
      }

      // Find current step
      const currentStep = progress.find(p => 
        p.status === 'in_progress' || 
        (p.status === 'pending' && !progress.some(p2 => p2.step_order < p.step_order && p2.status === 'pending'))
      ) || progress[progress.length - 1];

      // Calculate progress
      const completedSteps = progress.filter(p => p.status === 'completed').length;
      const totalSteps = progress.length;
      const percentage = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

      // Get last updated time
      const lastUpdated = progress
        .map(p => p.completed_at)
        .filter(Boolean)
        .sort()
        .pop() || new Date().toISOString();

      return {
        overall,
        currentStep: currentStep.step_key,
        currentStepTitle: currentStep.title,
        currentStepDescription: currentStep.description,
        lastUpdated,
        progress: {
          completed: completedSteps,
          total: totalSteps,
          percentage
        }
      };

    } catch (error) {
      logger.error('Error getting withdrawal status', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId
      });
      return null;
    }
  }

  /**
   * Clean up old withdrawal progress records (older than 90 days)
   */
  static async cleanupOldProgress(): Promise<number> {
    try {
      const result = await executeUpdate(
        `DELETE FROM tbl_withdrawal_progress 
         WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)`,
        []
      );

      const deletedCount = result.affectedRows || 0;
      logger.info('Cleaned up old withdrawal progress records', { deletedCount });
      
      return deletedCount;

    } catch (error) {
      logger.error('Error cleaning up old withdrawal progress', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return 0;
    }
  }
}
