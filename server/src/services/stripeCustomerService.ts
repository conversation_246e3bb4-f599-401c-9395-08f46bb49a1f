import Stripe from 'stripe';
import { stripeClient, STRIPE_CONFIG } from '../config/stripeConfig';
import { executeQuery, executeUpdate, executeQuerySingle } from '../utils/database';
import { getUserById, UserData } from './userService';
import logger from '../utils/logger';

/**
 * Interface for Stripe customer mapping in database
 */
export interface StripeCustomerMapping {
  id: number;
  user_id: number;
  stripe_customer_id: string;
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface for creating a Stripe customer
 */
export interface CreateStripeCustomerData {
  userId: number;
  email: string;
  name?: string;
  phone?: string;
  metadata?: Record<string, string>;
}

/**
 * Interface for updating a Stripe customer
 */
export interface UpdateStripeCustomerData {
  email?: string;
  name?: string;
  phone?: string;
  metadata?: Record<string, string>;
}

/**
 * Result interface for customer operations
 */
export interface CustomerOperationResult {
  success: boolean;
  customer?: Stripe.Customer;
  mapping?: StripeCustomerMapping;
  error?: string;
}

/**
 * Service for managing Stripe customers and their mappings to users
 */
export class StripeCustomerService {
  
  /**
   * Create a new Stripe customer and store the mapping
   * @param customerData - Customer data for creation
   * @returns Promise<CustomerOperationResult>
   */
  static async createStripeCustomer(customerData: CreateStripeCustomerData): Promise<CustomerOperationResult> {
    try {
      // Validate user exists
      const user = await getUserById(customerData.userId);
      if (!user) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      // Check if customer mapping already exists
      const existingMapping = await this.getStripeCustomerMapping(customerData.userId);
      if (existingMapping) {
        // Return existing customer
        const existingCustomer = await stripeClient.customers.retrieve(existingMapping.stripe_customer_id);
        return {
          success: true,
          customer: existingCustomer as Stripe.Customer,
          mapping: existingMapping
        };
      }

      // Prepare customer data for Stripe
      const stripeCustomerData: Stripe.CustomerCreateParams = {
        email: customerData.email,
        name: customerData.name || `${user.firstname || ''} ${user.lastname || ''}`.trim() || undefined,
        phone: customerData.phone,
        description: STRIPE_CONFIG.CUSTOMER.DESCRIPTION_TEMPLATE.replace('{userId}', customerData.userId.toString()),
        metadata: {
          ...STRIPE_CONFIG.CUSTOMER.DEFAULT_METADATA,
          user_id: customerData.userId.toString(),
          ...customerData.metadata
        }
      };

      // Create customer in Stripe
      const stripeCustomer = await stripeClient.customers.create(stripeCustomerData);

      // Store mapping in database
      const mappingResult = await executeUpdate(
        `INSERT INTO stripe_customer_mappings (user_id, stripe_customer_id, created_at, updated_at) 
         VALUES (?, ?, NOW(), NOW())`,
        [customerData.userId, stripeCustomer.id]
      );

      // Get the created mapping
      const mapping = await this.getStripeCustomerMapping(customerData.userId);

      logger.info('Stripe customer created successfully', {
        userId: customerData.userId,
        stripeCustomerId: stripeCustomer.id,
        mappingId: mappingResult.insertId
      });

      return {
        success: true,
        customer: stripeCustomer,
        mapping: mapping || undefined
      };

    } catch (error) {
      logger.error('Error creating Stripe customer', {
        customerData: { ...customerData, metadata: '[REDACTED]' },
        error: error instanceof Error ? error.message : error
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Get Stripe customer by user ID
   * @param userId - User ID
   * @returns Promise<Stripe.Customer | null>
   */
  static async getStripeCustomerByUserId(userId: number): Promise<Stripe.Customer | null> {
    try {
      const mapping = await this.getStripeCustomerMapping(userId);
      if (!mapping) {
        return null;
      }

      const customer = await stripeClient.customers.retrieve(mapping.stripe_customer_id);
      return customer as Stripe.Customer;

    } catch (error) {
      logger.error('Error getting Stripe customer by user ID', {
        userId,
        error: error instanceof Error ? error.message : error
      });
      return null;
    }
  }

  /**
   * Get Stripe customer by Stripe customer ID
   * @param stripeCustomerId - Stripe customer ID
   * @returns Promise<Stripe.Customer | null>
   */
  static async getStripeCustomerById(stripeCustomerId: string): Promise<Stripe.Customer | null> {
    try {
      const customer = await stripeClient.customers.retrieve(stripeCustomerId);
      return customer as Stripe.Customer;

    } catch (error) {
      logger.error('Error getting Stripe customer by ID', {
        stripeCustomerId,
        error: error instanceof Error ? error.message : error
      });
      return null;
    }
  }

  /**
   * Update Stripe customer information
   * @param userId - User ID
   * @param updateData - Data to update
   * @returns Promise<CustomerOperationResult>
   */
  static async updateStripeCustomer(userId: number, updateData: UpdateStripeCustomerData): Promise<CustomerOperationResult> {
    try {
      const mapping = await this.getStripeCustomerMapping(userId);
      if (!mapping) {
        return {
          success: false,
          error: 'Stripe customer not found for user'
        };
      }

      // Prepare update data for Stripe
      const stripeUpdateData: Stripe.CustomerUpdateParams = {};
      
      if (updateData.email !== undefined) {
        stripeUpdateData.email = updateData.email;
      }
      
      if (updateData.name !== undefined) {
        stripeUpdateData.name = updateData.name;
      }
      
      if (updateData.phone !== undefined) {
        stripeUpdateData.phone = updateData.phone;
      }
      
      if (updateData.metadata !== undefined) {
        stripeUpdateData.metadata = updateData.metadata;
      }

      // Update customer in Stripe
      const updatedCustomer = await stripeClient.customers.update(mapping.stripe_customer_id, stripeUpdateData);

      // Update mapping timestamp
      await executeUpdate(
        'UPDATE stripe_customer_mappings SET updated_at = NOW() WHERE user_id = ?',
        [userId]
      );

      logger.info('Stripe customer updated successfully', {
        userId,
        stripeCustomerId: mapping.stripe_customer_id,
        updateData: { ...updateData, metadata: '[REDACTED]' }
      });

      return {
        success: true,
        customer: updatedCustomer,
        mapping
      };

    } catch (error) {
      logger.error('Error updating Stripe customer', {
        userId,
        updateData: { ...updateData, metadata: '[REDACTED]' },
        error: error instanceof Error ? error.message : error
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Delete Stripe customer and remove mapping
   * @param userId - User ID
   * @returns Promise<CustomerOperationResult>
   */
  static async deleteStripeCustomer(userId: number): Promise<CustomerOperationResult> {
    try {
      const mapping = await this.getStripeCustomerMapping(userId);
      if (!mapping) {
        return {
          success: false,
          error: 'Stripe customer not found for user'
        };
      }

      // Delete customer in Stripe
      await stripeClient.customers.del(mapping.stripe_customer_id);

      // Remove mapping from database
      await executeUpdate(
        'DELETE FROM stripe_customer_mappings WHERE user_id = ?',
        [userId]
      );

      logger.info('Stripe customer deleted successfully', {
        userId,
        stripeCustomerId: mapping.stripe_customer_id
      });

      return {
        success: true,
        mapping
      };

    } catch (error) {
      logger.error('Error deleting Stripe customer', {
        userId,
        error: error instanceof Error ? error.message : error
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Get or create Stripe customer for a user
   * @param userId - User ID
   * @returns Promise<CustomerOperationResult>
   */
  static async getOrCreateStripeCustomer(userId: number): Promise<CustomerOperationResult> {
    try {
      // Try to get existing customer
      const existingCustomer = await this.getStripeCustomerByUserId(userId);
      if (existingCustomer) {
        const mapping = await this.getStripeCustomerMapping(userId);
        return {
          success: true,
          customer: existingCustomer,
          mapping: mapping || undefined
        };
      }

      // Get user data for customer creation
      const user = await getUserById(userId);
      if (!user) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      // Create new customer
      return await this.createStripeCustomer({
        userId,
        email: user.email,
        name: `${user.firstname || ''} ${user.lastname || ''}`.trim() || undefined
      });

    } catch (error) {
      logger.error('Error getting or creating Stripe customer', {
        userId,
        error: error instanceof Error ? error.message : error
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Get Stripe customer mapping from database
   * @param userId - User ID
   * @returns Promise<StripeCustomerMapping | null>
   */
  static async getStripeCustomerMapping(userId: number): Promise<StripeCustomerMapping | null> {
    try {
      const mapping = await executeQuerySingle<StripeCustomerMapping>(
        'SELECT id, user_id, stripe_customer_id, created_at, updated_at FROM stripe_customer_mappings WHERE user_id = ?',
        [userId]
      );
      return mapping;

    } catch (error) {
      logger.error('Error getting Stripe customer mapping', {
        userId,
        error: error instanceof Error ? error.message : error
      });
      return null;
    }
  }

  /**
   * Get user ID from Stripe customer ID
   * @param stripeCustomerId - Stripe customer ID
   * @returns Promise<number | null>
   */
  static async getUserIdFromStripeCustomer(stripeCustomerId: string): Promise<number | null> {
    try {
      const mapping = await executeQuerySingle<StripeCustomerMapping>(
        'SELECT user_id FROM stripe_customer_mappings WHERE stripe_customer_id = ?',
        [stripeCustomerId]
      );
      return mapping?.user_id || null;

    } catch (error) {
      logger.error('Error getting user ID from Stripe customer', {
        stripeCustomerId,
        error: error instanceof Error ? error.message : error
      });
      return null;
    }
  }

  /**
   * List all Stripe customer mappings with pagination
   * @param page - Page number (1-based)
   * @param limit - Number of records per page
   * @returns Promise<{ mappings: StripeCustomerMapping[], total: number }>
   */
  static async listStripeCustomerMappings(
    page: number = 1,
    limit: number = 10
  ): Promise<{ mappings: StripeCustomerMapping[], total: number }> {
    try {
      const offset = (page - 1) * limit;

      // Get total count
      const countResult = await executeQuerySingle<{ total: number }>(
        'SELECT COUNT(*) as total FROM stripe_customer_mappings'
      );
      const total = countResult?.total || 0;

      // Get paginated mappings
      const mappings = await executeQuery<StripeCustomerMapping>(
        `SELECT id, user_id, stripe_customer_id, created_at, updated_at 
         FROM stripe_customer_mappings 
         ORDER BY created_at DESC 
         LIMIT ? OFFSET ?`,
        [limit, offset]
      );

      return { mappings, total };

    } catch (error) {
      logger.error('Error listing Stripe customer mappings', {
        page,
        limit,
        error: error instanceof Error ? error.message : error
      });
      return { mappings: [], total: 0 };
    }
  }

  /**
   * Sync user data with Stripe customer
   * @param userId - User ID
   * @returns Promise<CustomerOperationResult>
   */
  static async syncUserWithStripeCustomer(userId: number): Promise<CustomerOperationResult> {
    try {
      const user = await getUserById(userId);
      if (!user) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      const mapping = await this.getStripeCustomerMapping(userId);
      if (!mapping) {
        return {
          success: false,
          error: 'Stripe customer mapping not found'
        };
      }

      // Update Stripe customer with current user data
      return await this.updateStripeCustomer(userId, {
        email: user.email,
        name: `${user.firstname || ''} ${user.lastname || ''}`.trim() || undefined
      });

    } catch (error) {
      logger.error('Error syncing user with Stripe customer', {
        userId,
        error: error instanceof Error ? error.message : error
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}

export default StripeCustomerService;