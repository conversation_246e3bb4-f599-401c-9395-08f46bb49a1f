import { executeUpdate, executeQuery, executeQuerySingle } from '../utils/database';
import logger from '../utils/logger';

/**
 * Interface for webhook events in the queue
 */
export interface QueuedWebhookEvent {
  id: string;
  eventId: string;
  webhookEventId?: number;
  event: WebhookEventData;
  attempts: number;
  maxAttempts: number;
  nextRetryAt: Date;
  lastError?: string;
  priority: number;
  createdAt: Date;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'dead_letter';
}

/**
 * Interface for webhook event data
 */
export interface WebhookEventData {
  webhook_type: string;
  webhook_code: string;
  event_id: string;
  transfer_id?: string;
  item_id?: string;
  account_id?: string;
  status?: string;
  timestamp: string;
  environment: string;
  [key: string]: any;
}

/**
 * Interface for webhook processing result
 */
export interface WebhookProcessingResult {
  success: boolean;
  eventId: string;
  processingTime: number;
  action: string;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Interface for queue statistics
 */
export interface QueueStats {
  totalEvents: number;
  pendingEvents: number;
  processingEvents: number;
  completedEvents: number;
  failedEvents: number;
  deadLetterEvents: number;
  averageProcessingTime: number;
  errorRate: number;
}

/**
 * Configuration for retry logic
 */
export interface RetryConfig {
  maxAttempts: number;
  initialDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableErrors: string[];
}

/**
 * Default retry configuration
 */
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 5,
  initialDelay: 1000, // 1 second
  maxDelay: 30000,    // 30 seconds
  backoffMultiplier: 2,
  retryableErrors: [
    'NETWORK_ERROR',
    'TEMPORARY_UNAVAILABLE',
    'RATE_LIMIT_EXCEEDED',
    'DATABASE_CONNECTION_ERROR',
    'TIMEOUT_ERROR'
  ]
};

/**
 * WebhookEventQueue service for reliable webhook processing
 * Provides in-memory and persistent queuing with retry mechanisms
 */
export class WebhookEventQueue {
  private inMemoryQueue: Map<string, QueuedWebhookEvent> = new Map();
  private processingEvents: Set<string> = new Set();
  private retryConfig: RetryConfig;
  private isProcessing: boolean = false;
  private processingInterval?: NodeJS.Timeout;
  private batchSize: number = 10;
  private concurrentLimit: number = 5;

  constructor(retryConfig?: Partial<RetryConfig>) {
    this.retryConfig = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };
    this.startQueueProcessor();
  }

  /**
   * Enqueue a webhook event for processing with enhanced deduplication
   */
  async enqueue(event: WebhookEventData, priority: number = 0): Promise<void> {
    try {
      const eventId = event.event_id || `webhook_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      
      // Enhanced deduplication check - check both enhanced table and queue
      const isDuplicate = await this.checkForDuplicateEvent(eventId);
      if (isDuplicate) {
        logger.info('Duplicate webhook event detected in queue, skipping enqueue', {
          eventId,
          webhookType: event.webhook_type,
          webhookCode: event.webhook_code,
          transferId: event.transfer_id
        });
        return; // Skip enqueuing duplicate events
      }
      
      // Store in enhanced webhook events table first with deduplication
      const webhookEventId = await this.storeWebhookEventWithDeduplication(event);
      if (!webhookEventId) {
        logger.info('Event already exists in database, skipping queue', {
          eventId,
          webhookType: event.webhook_type,
          webhookCode: event.webhook_code
        });
        return;
      }
      
      const queuedEvent: QueuedWebhookEvent = {
        id: `queue_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        eventId,
        webhookEventId,
        event: {
          ...event,
          event_id: eventId, // Ensure event_id is set
          enqueued_at: new Date().toISOString(),
          correlation_id: event.correlation_id || `corr_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
        },
        attempts: 0,
        maxAttempts: this.retryConfig.maxAttempts,
        nextRetryAt: new Date(),
        priority,
        createdAt: new Date(),
        status: 'pending'
      };

      // Add to in-memory queue for immediate processing
      this.inMemoryQueue.set(queuedEvent.id, queuedEvent);

      // Store in persistent queue
      await this.storePersistentQueueEvent(queuedEvent);

      logger.info('Webhook event enqueued with enhanced tracking', {
        eventId,
        queueId: queuedEvent.id,
        webhookType: event.webhook_type,
        webhookCode: event.webhook_code,
        transferId: event.transfer_id,
        correlationId: event.correlation_id,
        priority,
        queueSize: this.inMemoryQueue.size,
        verified: event.verified || false
      });

    } catch (error) {
      logger.error('Error enqueuing webhook event', {
        eventId: event.event_id,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }

  /**
   * Process the webhook event queue
   */
  async processQueue(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      // Load pending events from persistent storage if in-memory queue is empty
      if (this.inMemoryQueue.size === 0) {
        await this.loadPendingEvents();
      }

      // Get events ready for processing
      const readyEvents = this.getReadyEvents();
      
      if (readyEvents.length === 0) {
        return;
      }

      logger.info('Processing webhook queue', {
        readyEvents: readyEvents.length,
        totalInMemory: this.inMemoryQueue.size,
        processing: this.processingEvents.size
      });

      // Process events concurrently with limit
      const batches = this.createBatches(readyEvents, this.concurrentLimit);
      
      for (const batch of batches) {
        await Promise.all(batch.map(event => this.processEvent(event)));
      }

    } catch (error) {
      logger.error('Error processing webhook queue', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Retry failed events that are ready for retry
   */
  async retryFailedEvents(): Promise<void> {
    try {
      // Load failed events from persistent storage
      const failedEvents = await this.loadFailedEvents();
      
      for (const event of failedEvents) {
        if (event.nextRetryAt <= new Date() && event.attempts < event.maxAttempts) {
          // Add back to in-memory queue for retry
          event.status = 'pending';
          this.inMemoryQueue.set(event.id, event);
          
          logger.info('Retrying failed webhook event', {
            eventId: event.eventId,
            attempt: event.attempts + 1,
            maxAttempts: event.maxAttempts
          });
        }
      }

    } catch (error) {
      logger.error('Error retrying failed events', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats(): Promise<QueueStats> {
    try {
      const stats = await executeQuerySingle(`
        SELECT 
          COUNT(*) as total_events,
          SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_events,
          SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing_events,
          SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_events,
          SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_events,
          SUM(CASE WHEN status = 'dead_letter' THEN 1 ELSE 0 END) as dead_letter_events
        FROM tbl_webhook_processing_queue
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      `);

      const processingStats = await executeQuerySingle(`
        SELECT 
          AVG(wee.processing_time_ms) as avg_processing_time,
          COUNT(CASE WHEN wee.processed_result IS NULL THEN 1 END) / COUNT(*) as error_rate
        FROM tbl_webhook_events_enhanced wee
        WHERE wee.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      `);

      return {
        totalEvents: stats?.total_events || 0,
        pendingEvents: stats?.pending_events || 0,
        processingEvents: stats?.processing_events || 0,
        completedEvents: stats?.completed_events || 0,
        failedEvents: stats?.failed_events || 0,
        deadLetterEvents: stats?.dead_letter_events || 0,
        averageProcessingTime: processingStats?.avg_processing_time || 0,
        errorRate: processingStats?.error_rate || 0
      };

    } catch (error) {
      logger.error('Error getting queue stats', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      return {
        totalEvents: 0,
        pendingEvents: 0,
        processingEvents: 0,
        completedEvents: 0,
        failedEvents: 0,
        deadLetterEvents: 0,
        averageProcessingTime: 0,
        errorRate: 0
      };
    }
  }

  /**
   * Start the queue processor
   */
  private startQueueProcessor(): void {
    // Process queue every 5 seconds
    this.processingInterval = setInterval(async () => {
      await this.processQueue();
      await this.retryFailedEvents();
    }, 5000);

    logger.info('Webhook queue processor started', {
      interval: 5000,
      batchSize: this.batchSize,
      concurrentLimit: this.concurrentLimit
    });
  }

  /**
   * Stop the queue processor
   */
  stopQueueProcessor(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = undefined;
    }
    logger.info('Webhook queue processor stopped');
  }

  /**
   * Store webhook event in enhanced events table
   */
  private async storeWebhookEvent(event: WebhookEventData): Promise<number> {
    try {
      const result = await executeUpdate(`
        INSERT INTO tbl_webhook_events_enhanced
        (event_id, webhook_type, webhook_code, transfer_id, item_id, account_id, 
         status, raw_data, verified, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        event.event_id,
        event.webhook_type,
        event.webhook_code,
        event.transfer_id || null,
        event.item_id || null,
        event.account_id || null,
        event.status || null,
        JSON.stringify(event),
        false // Will be verified during processing
      ]);

      return result.insertId;

    } catch (error) {
      logger.error('Error storing webhook event', {
        eventId: event.event_id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Store queued event in persistent queue
   */
  private async storePersistentQueueEvent(queuedEvent: QueuedWebhookEvent): Promise<void> {
    try {
      await executeUpdate(`
        INSERT INTO tbl_webhook_processing_queue
        (event_id, webhook_event_id, status, attempts, max_attempts, 
         next_retry_at, priority, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        queuedEvent.eventId,
        queuedEvent.webhookEventId,
        queuedEvent.status,
        queuedEvent.attempts,
        queuedEvent.maxAttempts,
        queuedEvent.nextRetryAt,
        queuedEvent.priority,
        queuedEvent.createdAt
      ]);

    } catch (error) {
      logger.error('Error storing persistent queue event', {
        eventId: queuedEvent.eventId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Load pending events from persistent storage
   */
  private async loadPendingEvents(): Promise<void> {
    try {
      const limit = this.batchSize * 2;
      const events = await executeQuery(`
        SELECT wpq.*, wee.raw_data
        FROM tbl_webhook_processing_queue wpq
        JOIN tbl_webhook_events_enhanced wee ON wpq.webhook_event_id = wee.id
        WHERE wpq.status IN ('pending', 'failed') 
        AND wpq.next_retry_at <= NOW()
        AND wpq.attempts < wpq.max_attempts
        ORDER BY wpq.priority DESC, wpq.created_at ASC
        LIMIT ${limit}
      `);

      const eventArray = Array.isArray(events) ? events : events ? [events] : [];

      for (const dbEvent of eventArray) {
        const queuedEvent: QueuedWebhookEvent = {
          id: `queue_${dbEvent.id}`,
          eventId: dbEvent.event_id,
          webhookEventId: dbEvent.webhook_event_id,
          event: typeof dbEvent.raw_data === 'string' ? JSON.parse(dbEvent.raw_data) : dbEvent.raw_data,
          attempts: dbEvent.attempts,
          maxAttempts: dbEvent.max_attempts,
          nextRetryAt: new Date(dbEvent.next_retry_at),
          lastError: dbEvent.last_error,
          priority: dbEvent.priority,
          createdAt: new Date(dbEvent.created_at),
          status: 'pending'
        };

        this.inMemoryQueue.set(queuedEvent.id, queuedEvent);
      }

      if (eventArray.length > 0) {
        logger.info('Loaded pending events from persistent storage', {
          count: eventArray.length
        });
      }

    } catch (error) {
      logger.error('Error loading pending events', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Load failed events ready for retry
   */
  private async loadFailedEvents(): Promise<QueuedWebhookEvent[]> {
    try {
      const events = await executeQuery(`
        SELECT wpq.*, wee.raw_data
        FROM tbl_webhook_processing_queue wpq
        JOIN tbl_webhook_events_enhanced wee ON wpq.webhook_event_id = wee.id
        WHERE wpq.status = 'failed' 
        AND wpq.next_retry_at <= NOW()
        AND wpq.attempts < wpq.max_attempts
        ORDER BY wpq.priority DESC, wpq.created_at ASC
        LIMIT ${this.batchSize}
      `);

      const eventArray = Array.isArray(events) ? events : events ? [events] : [];

      return eventArray.map(dbEvent => ({
        id: `queue_${dbEvent.id}`,
        eventId: dbEvent.event_id,
        webhookEventId: dbEvent.webhook_event_id,
        event: typeof dbEvent.raw_data === 'string' ? JSON.parse(dbEvent.raw_data) : dbEvent.raw_data,
        attempts: dbEvent.attempts,
        maxAttempts: dbEvent.max_attempts,
        nextRetryAt: new Date(dbEvent.next_retry_at),
        lastError: dbEvent.last_error,
        priority: dbEvent.priority,
        createdAt: new Date(dbEvent.created_at),
        status: 'failed'
      }));

    } catch (error) {
      logger.error('Error loading failed events', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return [];
    }
  }

  /**
   * Get events ready for processing
   */
  private getReadyEvents(): QueuedWebhookEvent[] {
    const now = new Date();
    const readyEvents: QueuedWebhookEvent[] = [];

    for (const [id, event] of this.inMemoryQueue.entries()) {
      if (event.status === 'pending' && 
          event.nextRetryAt <= now && 
          !this.processingEvents.has(id) &&
          event.attempts < event.maxAttempts) {
        readyEvents.push(event);
      }
    }

    // Sort by priority (desc) and creation time (asc)
    return readyEvents.sort((a, b) => {
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }
      return a.createdAt.getTime() - b.createdAt.getTime();
    }).slice(0, this.batchSize);
  }

  /**
   * Create batches for concurrent processing
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Process a single webhook event
   */
  private async processEvent(queuedEvent: QueuedWebhookEvent): Promise<void> {
    const startTime = Date.now();
    
    // Mark as processing
    this.processingEvents.add(queuedEvent.id);
    queuedEvent.status = 'processing';
    queuedEvent.attempts++;

    try {
      // Update persistent queue status
      await this.updateQueueEventStatus(queuedEvent, 'processing');

      logger.info('Processing webhook event', {
        eventId: queuedEvent.eventId,
        attempt: queuedEvent.attempts,
        webhookType: queuedEvent.event.webhook_type,
        webhookCode: queuedEvent.event.webhook_code
      });

      // Process the webhook event using existing controller logic
      const result = await this.processWebhookEvent(queuedEvent.event);
      
      const processingTime = Date.now() - startTime;

      if (result.success) {
        // Mark as completed
        queuedEvent.status = 'completed';
        await this.updateQueueEventStatus(queuedEvent, 'completed');
        await this.updateWebhookEventResult(queuedEvent, result, processingTime);
        
        // Remove from in-memory queue
        this.inMemoryQueue.delete(queuedEvent.id);

        logger.info('Webhook event processed successfully', {
          eventId: queuedEvent.eventId,
          processingTime,
          action: result.action
        });

      } else {
        // Handle failure
        await this.handleEventFailure(queuedEvent, result.error || 'Processing failed', processingTime);
      }

    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      logger.error('Error processing webhook event', {
        eventId: queuedEvent.eventId,
        attempt: queuedEvent.attempts,
        error: errorMessage,
        processingTime
      });

      await this.handleEventFailure(queuedEvent, errorMessage, processingTime);

    } finally {
      this.processingEvents.delete(queuedEvent.id);
    }
  }

  /**
   * Handle event processing failure
   */
  private async handleEventFailure(queuedEvent: QueuedWebhookEvent, error: string, processingTime: number): Promise<void> {
    queuedEvent.lastError = error;

    // Check if error is retryable
    const isRetryable = this.isRetryableError(error);
    
    if (queuedEvent.attempts >= queuedEvent.maxAttempts || !isRetryable) {
      // Move to dead letter queue
      queuedEvent.status = 'dead_letter';
      await this.updateQueueEventStatus(queuedEvent, 'dead_letter');
      await this.updateWebhookEventResult(queuedEvent, { 
        success: false, 
        error, 
        attempts: queuedEvent.attempts,
        deadLetter: true 
      }, processingTime);
      
      // Remove from in-memory queue
      this.inMemoryQueue.delete(queuedEvent.id);

      logger.error('Webhook event moved to dead letter queue', {
        eventId: queuedEvent.eventId,
        attempts: queuedEvent.attempts,
        error,
        isRetryable
      });

    } else {
      // Schedule for retry with exponential backoff
      const delay = this.calculateRetryDelay(queuedEvent.attempts);
      queuedEvent.nextRetryAt = new Date(Date.now() + delay);
      queuedEvent.status = 'failed';
      
      await this.updateQueueEventStatus(queuedEvent, 'failed');
      await this.updateWebhookEventResult(queuedEvent, { 
        success: false, 
        error, 
        attempts: queuedEvent.attempts,
        nextRetryAt: queuedEvent.nextRetryAt 
      }, processingTime);

      logger.warn('Webhook event scheduled for retry', {
        eventId: queuedEvent.eventId,
        attempt: queuedEvent.attempts,
        nextRetryAt: queuedEvent.nextRetryAt,
        delay,
        error
      });
    }
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(attempts: number): number {
    const delay = this.retryConfig.initialDelay * Math.pow(this.retryConfig.backoffMultiplier, attempts - 1);
    return Math.min(delay, this.retryConfig.maxDelay);
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: string): boolean {
    return this.retryConfig.retryableErrors.some(retryableError => 
      error.toUpperCase().includes(retryableError)
    );
  }

  /**
   * Update queue event status in persistent storage
   */
  private async updateQueueEventStatus(queuedEvent: QueuedWebhookEvent, status: string): Promise<void> {
    try {
      await executeUpdate(`
        UPDATE tbl_webhook_processing_queue 
        SET status = ?, attempts = ?, next_retry_at = ?, last_error = ?, updated_at = NOW()
        WHERE event_id = ?
      `, [
        status,
        queuedEvent.attempts,
        queuedEvent.nextRetryAt,
        queuedEvent.lastError || null,
        queuedEvent.eventId
      ]);

    } catch (error) {
      logger.error('Error updating queue event status', {
        eventId: queuedEvent.eventId,
        status,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Update webhook event with processing result
   */
  private async updateWebhookEventResult(queuedEvent: QueuedWebhookEvent, result: any, processingTime: number): Promise<void> {
    try {
      await executeUpdate(`
        UPDATE tbl_webhook_events_enhanced 
        SET processed_result = ?, processing_time_ms = ?, processed_at = NOW(), retry_count = ?
        WHERE event_id = ?
      `, [
        JSON.stringify(result),
        processingTime,
        queuedEvent.attempts,
        queuedEvent.eventId
      ]);

    } catch (error) {
      logger.error('Error updating webhook event result', {
        eventId: queuedEvent.eventId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Process webhook event using existing controller logic
   */
  private async processWebhookEvent(event: WebhookEventData): Promise<WebhookProcessingResult> {
    try {
      // Import the existing webhook handler
      const { handleTransferWebhook } = await import('./webhookEventProcessor');
      
      const result = await handleTransferWebhook(event);
      
      return {
        success: true,
        eventId: event.event_id,
        processingTime: 0, // Will be set by caller
        action: result.action || 'processed',
        metadata: result.metadata
      };

    } catch (error) {
      return {
        success: false,
        eventId: event.event_id,
        processingTime: 0, // Will be set by caller
        action: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Check for duplicate events in both enhanced table and current queue
   */
  private async checkForDuplicateEvent(eventId: string): Promise<boolean> {
    try {
      // Check enhanced webhook events table
      const existingEvent = await executeQuerySingle(
        'SELECT id FROM tbl_webhook_events_enhanced WHERE event_id = ?',
        [eventId]
      );
      
      if (existingEvent) {
        return true;
      }
      
      // Check current in-memory queue
      for (const [, queuedEvent] of this.inMemoryQueue.entries()) {
        if (queuedEvent.eventId === eventId) {
          return true;
        }
      }
      
      // Check persistent queue
      const queuedEvent = await executeQuerySingle(
        'SELECT id FROM tbl_webhook_processing_queue WHERE event_id = ?',
        [eventId]
      );
      
      return !!queuedEvent;
      
    } catch (error) {
      logger.error('Error checking for duplicate event', {
        eventId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      // In case of error, assume not duplicate to avoid losing events
      return false;
    }
  }

  /**
   * Store webhook event with deduplication check
   * Returns the webhook event ID if stored, null if duplicate
   */
  private async storeWebhookEventWithDeduplication(event: WebhookEventData): Promise<number | null> {
    try {
      // Try to insert with ON DUPLICATE KEY UPDATE to handle race conditions
      const result = await executeUpdate(`
        INSERT INTO tbl_webhook_events_enhanced
        (event_id, webhook_type, webhook_code, transfer_id, item_id, account_id, 
         status, raw_data, verified, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        id = LAST_INSERT_ID(id),
        raw_data = IF(processed_result IS NULL, VALUES(raw_data), raw_data),
        verified = IF(processed_result IS NULL, VALUES(verified), verified)
      `, [
        event.event_id,
        event.webhook_type,
        event.webhook_code,
        event.transfer_id || null,
        event.item_id || null,
        event.account_id || null,
        event.status || null,
        JSON.stringify(event),
        event.verified || false
      ]);

      // If insertId is 0, it means the event already existed and was not updated
      if (result.insertId === 0) {
        logger.info('Webhook event already exists, skipping duplicate', {
          eventId: event.event_id,
          webhookType: event.webhook_type,
          webhookCode: event.webhook_code
        });
        return null;
      }

      return result.insertId;

    } catch (error) {
      // Check if it's a duplicate key error
      if (error instanceof Error && error.message.includes('Duplicate entry')) {
        logger.info('Duplicate webhook event detected during storage', {
          eventId: event.event_id,
          webhookType: event.webhook_type,
          webhookCode: event.webhook_code
        });
        return null;
      }
      
      logger.error('Error storing webhook event with deduplication', {
        eventId: event.event_id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}

// Export singleton instance
export const webhookEventQueue = new WebhookEventQueue();