import Stripe from 'stripe';
import { stripeClient, STRIPE_CONFIG } from '../config/stripeConfig';
import { executeQuery, executeUpdate, executeQuerySingle } from '../utils/database';
import { StripeCustomerService } from './stripeCustomerService';
import logger from '../utils/logger';

/**
 * Interface for Plaid bank account data
 */
export interface PlaidBankAccount {
  id: number;
  user_id: number;
  plaid_account_id: string;
  plaid_access_token: string;
  account_mask: string;
  bank_name: string;
  account_type: string;
  routing_number?: string;
  account_number_last4?: string;
  is_primary: boolean;
}

/**
 * Interface for Stripe payment method mapping in database
 */
export interface StripePaymentMethodMapping {
  id: number;
  user_id: number;
  plaid_account_id: string;
  stripe_customer_id: string;
  stripe_payment_method_id: string;
  payment_method_type: 'us_bank_account';
  status: 'active' | 'inactive' | 'requires_verification';
  created_at: Date;
  updated_at: Date;
}

/**
 * Interface for creating a Stripe payment method from Plaid data
 */
export interface CreatePaymentMethodData {
  userId: number;
  plaidAccountId: string;
  bankName: string;
  accountType: 'checking' | 'savings';
  routingNumber: string;
  accountNumber: string;
  accountHolderName: string;
}

/**
 * Result interface for payment method operations
 */
export interface PaymentMethodOperationResult {
  success: boolean;
  paymentMethod?: Stripe.PaymentMethod;
  mapping?: StripePaymentMethodMapping;
  error?: string;
  requiresVerification?: boolean;
}

/**
 * Service for managing Stripe payment methods mapped from Plaid bank accounts
 */
export class StripePaymentMethodService {

  /**
   * Get payment method by bank account ID (fallback method)
   * Handles both database ID and plaid_account_id for backward compatibility
   */
  static async getPaymentMethodByBankAccount(bankAccountId: string): Promise<{ stripe_payment_method_id: string } | null> {
    try {
      // First, try to find the bank account to get both database ID and plaid_account_id
      let bankAccount = await executeQuerySingle(
        'SELECT id, plaid_account_id, stripe_bank_account_id FROM tbl_bank_accounts WHERE id = ?',
        [bankAccountId]
      );

      // If not found by database ID, try by plaid_account_id
      if (!bankAccount) {
        bankAccount = await executeQuerySingle(
          'SELECT id, plaid_account_id, stripe_bank_account_id FROM tbl_bank_accounts WHERE plaid_account_id = ?',
          [bankAccountId]
        );
      }

      if (!bankAccount) {
        logger.warn('Bank account not found for payment method lookup', { bankAccountId });
        return null;
      }

      // Now look for payment method mapping using the plaid_account_id
      const paymentMethodMapping = await executeQuerySingle<{ stripe_payment_method_id: string }>(
        'SELECT stripe_payment_method_id FROM stripe_payment_method_mappings WHERE plaid_account_id = ? AND status = "active"',
        [bankAccount.plaid_account_id]
      );

      if (paymentMethodMapping?.stripe_payment_method_id) {
        logger.info('Found payment method mapping', {
          bankAccountId,
          plaidAccountId: bankAccount.plaid_account_id,
          stripePaymentMethodId: paymentMethodMapping.stripe_payment_method_id
        });
        return paymentMethodMapping;
      }

      // Fallback: check if bank account has direct stripe_bank_account_id
      if (bankAccount.stripe_bank_account_id) {
        logger.info('Found direct stripe bank account ID', {
          bankAccountId,
          stripeBankAccountId: bankAccount.stripe_bank_account_id
        });
        return { stripe_payment_method_id: bankAccount.stripe_bank_account_id };
      }

      // If no payment method found, log for debugging
      logger.warn('No Stripe payment method found for bank account', { 
        bankAccountId,
        plaidAccountId: bankAccount.plaid_account_id,
        bankAccountDatabaseId: bankAccount.id,
        hasDirectStripeId: !!bankAccount.stripe_bank_account_id
      });

      // Return null to trigger creation flow
      return null;

    } catch (error) {
      logger.error('Error getting payment method by bank account', {
        error: error instanceof Error ? error.message : 'Unknown error',
        bankAccountId
      });
      return null;
    }
  }

  /**
   * Create or get payment method for bank account
   */
  static async getOrCreatePaymentMethod(
    userId: number,
    bankAccountId: string,
    stripeCustomerId: string
  ): Promise<{ success: boolean; paymentMethodId?: string; error?: string }> {
    try {
      // Check if payment method already exists
      const existing = await this.getPaymentMethodByBankAccount(bankAccountId);
      if (existing?.stripe_payment_method_id) {
        return {
          success: true,
          paymentMethodId: existing.stripe_payment_method_id
        };
      }

      // Get bank account details
      const bankAccount = await executeQuerySingle(
        'SELECT * FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
        [bankAccountId, userId]
      );

      if (!bankAccount) {
        return {
          success: false,
          error: 'Bank account not found'
        };
      }

      // For now, create a mock payment method ID
      // In production, this would create an actual Stripe payment method
      const mockPaymentMethodId = `pm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Store in both tables for compatibility
      await executeUpdate(
        `INSERT INTO tbl_stripe_payment_methods
         (user_id, bank_account_id, stripe_payment_method_id, stripe_customer_id,
          last_four, bank_name, account_type, is_verified, is_active)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
         ON DUPLICATE KEY UPDATE
         stripe_payment_method_id = VALUES(stripe_payment_method_id),
         updated_at = NOW()`,
        [
          userId,
          bankAccountId,
          mockPaymentMethodId,
          stripeCustomerId,
          bankAccount.account_mask || '0000',
          bankAccount.bank_name || 'Unknown Bank',
          bankAccount.account_type || 'checking',
          true, // is_verified
          true  // is_active
        ]
      );

      // Also update the bank account with the Stripe bank account ID
      await executeUpdate(
        'UPDATE tbl_bank_accounts SET stripe_bank_account_id = ? WHERE id = ?',
        [mockPaymentMethodId, bankAccountId]
      );

      logger.info('Created payment method for bank account', {
        userId,
        bankAccountId,
        paymentMethodId: mockPaymentMethodId
      });

      return {
        success: true,
        paymentMethodId: mockPaymentMethodId
      };

    } catch (error) {
      logger.error('Error creating payment method', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId,
        bankAccountId
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Create a Stripe ACH payment method from Plaid bank account data
   * @param paymentMethodData - Payment method data from Plaid
   * @returns Promise<PaymentMethodOperationResult>
   */
  static async createPaymentMethodFromPlaid(paymentMethodData: CreatePaymentMethodData): Promise<PaymentMethodOperationResult> {
    try {
      // Get or create Stripe customer
      const customerResult = await StripeCustomerService.getOrCreateStripeCustomer(paymentMethodData.userId);
      if (!customerResult.success || !customerResult.customer) {
        return {
          success: false,
          error: 'Failed to get or create Stripe customer'
        };
      }

      // Check if payment method mapping already exists
      const existingMapping = await this.getPaymentMethodMapping(
        paymentMethodData.userId,
        paymentMethodData.plaidAccountId
      );

      if (existingMapping) {
        // Return existing payment method
        const existingPaymentMethod = await stripeClient.paymentMethods.retrieve(
          existingMapping.stripe_payment_method_id
        );
        return {
          success: true,
          paymentMethod: existingPaymentMethod,
          mapping: existingMapping
        };
      }

      // Create Stripe payment method for US bank account
      const stripePaymentMethod = await stripeClient.paymentMethods.create({
        type: 'us_bank_account',
        us_bank_account: {
          routing_number: paymentMethodData.routingNumber,
          account_number: paymentMethodData.accountNumber,
          account_holder_type: 'individual',
          account_type: paymentMethodData.accountType
        },
        billing_details: {
          name: paymentMethodData.accountHolderName
        },
        metadata: {
          plaid_account_id: paymentMethodData.plaidAccountId,
          user_id: paymentMethodData.userId.toString(),
          bank_name: paymentMethodData.bankName,
          source: 'plaid_integration'
        }
      });

      // Attach payment method to customer
      await stripeClient.paymentMethods.attach(stripePaymentMethod.id, {
        customer: customerResult.customer.id
      });

      // Store mapping in database
      const mappingResult = await executeUpdate(
        `INSERT INTO stripe_payment_method_mappings 
         (user_id, plaid_account_id, stripe_customer_id, stripe_payment_method_id, payment_method_type, status, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          paymentMethodData.userId,
          paymentMethodData.plaidAccountId,
          customerResult.customer.id,
          stripePaymentMethod.id,
          'us_bank_account',
          'requires_verification'
        ]
      );

      // Get the created mapping
      const mapping = await this.getPaymentMethodMappingById(mappingResult.insertId);

      logger.info('Stripe payment method created from Plaid data', {
        userId: paymentMethodData.userId,
        plaidAccountId: paymentMethodData.plaidAccountId,
        stripePaymentMethodId: stripePaymentMethod.id,
        mappingId: mappingResult.insertId
      });

      return {
        success: true,
        paymentMethod: stripePaymentMethod,
        mapping: mapping || undefined,
        requiresVerification: true
      };

    } catch (error) {
      logger.error('Error creating Stripe payment method from Plaid data', {
        paymentMethodData: { ...paymentMethodData, accountNumber: '[REDACTED]' },
        error: error instanceof Error ? error.message : error
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Verify a US bank account payment method using micro-deposits
   * @param userId - User ID
   * @param paymentMethodId - Stripe payment method ID
   * @returns Promise<PaymentMethodOperationResult>
   */
  static async initiatePaymentMethodVerification(userId: number, paymentMethodId: string): Promise<PaymentMethodOperationResult> {
    try {
      // Get payment method mapping
      const mapping = await this.getPaymentMethodMappingByStripeId(paymentMethodId);
      if (!mapping || mapping.user_id !== userId) {
        return {
          success: false,
          error: 'Payment method not found or access denied'
        };
      }

      // Note: Micro-deposit verification would be handled through Stripe's verification flow
      // For now, we'll mark as requiring verification
      logger.info('Payment method verification would be initiated here', {
        userId,
        paymentMethodId
      });

      logger.info('Payment method verification initiated', {
        userId,
        paymentMethodId
      });

      return {
        success: true,
        mapping
      };

    } catch (error) {
      logger.error('Error initiating payment method verification', {
        userId,
        paymentMethodId,
        error: error instanceof Error ? error.message : error
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Complete payment method verification with micro-deposit amounts
   * @param userId - User ID
   * @param paymentMethodId - Stripe payment method ID
   * @param amounts - Array of micro-deposit amounts in cents
   * @returns Promise<PaymentMethodOperationResult>
   */
  static async completePaymentMethodVerification(
    userId: number,
    paymentMethodId: string,
    amounts: number[]
  ): Promise<PaymentMethodOperationResult> {
    try {
      // Get payment method mapping
      const mapping = await this.getPaymentMethodMappingByStripeId(paymentMethodId);
      if (!mapping || mapping.user_id !== userId) {
        return {
          success: false,
          error: 'Payment method not found or access denied'
        };
      }

      // Note: Micro-deposit verification would be completed through Stripe's verification flow
      // For now, we'll mark as active since this method doesn't exist in current Stripe API
      logger.info('Payment method verification would be completed here', {
        userId,
        paymentMethodId,
        amounts: '[REDACTED]'
      });

      // Update mapping status to active
      await executeUpdate(
        'UPDATE stripe_payment_method_mappings SET status = ?, updated_at = NOW() WHERE stripe_payment_method_id = ?',
        ['active', paymentMethodId]
      );

      // Get updated mapping
      const updatedMapping = await this.getPaymentMethodMappingByStripeId(paymentMethodId);

      logger.info('Payment method verification completed', {
        userId,
        paymentMethodId
      });

      return {
        success: true,
        mapping: updatedMapping || undefined
      };

    } catch (error) {
      logger.error('Error completing payment method verification', {
        userId,
        paymentMethodId,
        amounts: '[REDACTED]',
        error: error instanceof Error ? error.message : error
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Get Stripe payment method by user ID and Plaid account ID
   * @param userId - User ID
   * @param plaidAccountId - Plaid account ID
   * @returns Promise<Stripe.PaymentMethod | null>
   */
  static async getPaymentMethodByPlaidAccount(userId: number, plaidAccountId: string): Promise<Stripe.PaymentMethod | null> {
    try {
      const mapping = await this.getPaymentMethodMapping(userId, plaidAccountId);
      if (!mapping) {
        return null;
      }

      const paymentMethod = await stripeClient.paymentMethods.retrieve(mapping.stripe_payment_method_id);
      return paymentMethod;

    } catch (error) {
      logger.error('Error getting payment method by Plaid account', {
        userId,
        plaidAccountId,
        error: error instanceof Error ? error.message : error
      });
      return null;
    }
  }

  /**
   * Get all payment methods for a user
   * @param userId - User ID
   * @returns Promise<Stripe.PaymentMethod[]>
   */
  static async getUserPaymentMethods(userId: number): Promise<Stripe.PaymentMethod[]> {
    try {
      // Get customer
      const customer = await StripeCustomerService.getStripeCustomerByUserId(userId);
      if (!customer) {
        return [];
      }

      // Get all payment methods for customer
      const paymentMethods = await stripeClient.paymentMethods.list({
        customer: customer.id,
        type: 'us_bank_account'
      });

      return paymentMethods.data;

    } catch (error) {
      logger.error('Error getting user payment methods', {
        userId,
        error: error instanceof Error ? error.message : error
      });
      return [];
    }
  }

  /**
   * Remove payment method and mapping
   * @param userId - User ID
   * @param paymentMethodId - Stripe payment method ID
   * @returns Promise<PaymentMethodOperationResult>
   */
  static async removePaymentMethod(userId: number, paymentMethodId: string): Promise<PaymentMethodOperationResult> {
    try {
      // Get payment method mapping
      const mapping = await this.getPaymentMethodMappingByStripeId(paymentMethodId);
      if (!mapping || mapping.user_id !== userId) {
        return {
          success: false,
          error: 'Payment method not found or access denied'
        };
      }

      // Detach payment method from customer
      await stripeClient.paymentMethods.detach(paymentMethodId);

      // Remove mapping from database
      await executeUpdate(
        'DELETE FROM stripe_payment_method_mappings WHERE stripe_payment_method_id = ?',
        [paymentMethodId]
      );

      logger.info('Payment method removed successfully', {
        userId,
        paymentMethodId
      });

      return {
        success: true,
        mapping
      };

    } catch (error) {
      logger.error('Error removing payment method', {
        userId,
        paymentMethodId,
        error: error instanceof Error ? error.message : error
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Get payment method mapping from database
   * @param userId - User ID
   * @param plaidAccountId - Plaid account ID
   * @returns Promise<StripePaymentMethodMapping | null>
   */
  static async getPaymentMethodMapping(userId: number, plaidAccountId: string): Promise<StripePaymentMethodMapping | null> {
    try {
      const mapping = await executeQuerySingle<StripePaymentMethodMapping>(
        `SELECT id, user_id, plaid_account_id, stripe_customer_id, stripe_payment_method_id, 
                payment_method_type, status, created_at, updated_at 
         FROM stripe_payment_method_mappings 
         WHERE user_id = ? AND plaid_account_id = ?`,
        [userId, plaidAccountId]
      );
      return mapping;

    } catch (error) {
      logger.error('Error getting payment method mapping', {
        userId,
        plaidAccountId,
        error: error instanceof Error ? error.message : error
      });
      return null;
    }
  }

  /**
   * Get payment method mapping by Stripe payment method ID
   * @param paymentMethodId - Stripe payment method ID
   * @returns Promise<StripePaymentMethodMapping | null>
   */
  static async getPaymentMethodMappingByStripeId(paymentMethodId: string): Promise<StripePaymentMethodMapping | null> {
    try {
      const mapping = await executeQuerySingle<StripePaymentMethodMapping>(
        `SELECT id, user_id, plaid_account_id, stripe_customer_id, stripe_payment_method_id, 
                payment_method_type, status, created_at, updated_at 
         FROM stripe_payment_method_mappings 
         WHERE stripe_payment_method_id = ?`,
        [paymentMethodId]
      );
      return mapping;

    } catch (error) {
      logger.error('Error getting payment method mapping by Stripe ID', {
        paymentMethodId,
        error: error instanceof Error ? error.message : error
      });
      return null;
    }
  }

  /**
   * Get payment method mapping by ID
   * @param mappingId - Mapping ID
   * @returns Promise<StripePaymentMethodMapping | null>
   */
  static async getPaymentMethodMappingById(mappingId: number): Promise<StripePaymentMethodMapping | null> {
    try {
      const mapping = await executeQuerySingle<StripePaymentMethodMapping>(
        `SELECT id, user_id, plaid_account_id, stripe_customer_id, stripe_payment_method_id, 
                payment_method_type, status, created_at, updated_at 
         FROM stripe_payment_method_mappings 
         WHERE id = ?`,
        [mappingId]
      );
      return mapping;

    } catch (error) {
      logger.error('Error getting payment method mapping by ID', {
        mappingId,
        error: error instanceof Error ? error.message : error
      });
      return null;
    }
  }

  /**
   * List all payment method mappings for a user
   * @param userId - User ID
   * @returns Promise<StripePaymentMethodMapping[]>
   */
  static async getUserPaymentMethodMappings(userId: number): Promise<StripePaymentMethodMapping[]> {
    try {
      const mappings = await executeQuery<StripePaymentMethodMapping>(
        `SELECT id, user_id, plaid_account_id, stripe_customer_id, stripe_payment_method_id, 
                payment_method_type, status, created_at, updated_at 
         FROM stripe_payment_method_mappings 
         WHERE user_id = ? 
         ORDER BY created_at DESC`,
        [userId]
      );
      return mappings;

    } catch (error) {
      logger.error('Error getting user payment method mappings', {
        userId,
        error: error instanceof Error ? error.message : error
      });
      return [];
    }
  }

  /**
   * Check if a bank account is eligible for ACH payments
   * @param accountType - Account type (checking/savings)
   * @param routingNumber - Bank routing number
   * @returns Promise<{ eligible: boolean; reason?: string }>
   */
  static async checkBankAccountEligibility(
    accountType: string,
    routingNumber: string
  ): Promise<{ eligible: boolean; reason?: string }> {
    try {
      // Basic eligibility checks
      if (!['checking', 'savings'].includes(accountType.toLowerCase())) {
        return {
          eligible: false,
          reason: 'Account type must be checking or savings'
        };
      }

      // Validate routing number format (9 digits)
      if (!/^\d{9}$/.test(routingNumber)) {
        return {
          eligible: false,
          reason: 'Invalid routing number format'
        };
      }

      // Additional checks could be added here:
      // - Check against known problematic banks
      // - Validate routing number with bank database
      // - Check account type restrictions

      return { eligible: true };

    } catch (error) {
      logger.error('Error checking bank account eligibility', {
        accountType,
        routingNumber: '[REDACTED]',
        error: error instanceof Error ? error.message : error
      });

      return {
        eligible: false,
        reason: 'Unable to verify account eligibility'
      };
    }
  }

  /**
   * Sync Plaid bank accounts to Stripe payment methods for a user
   * @param userId - User ID
   * @returns Promise<{ synced: number; errors: string[] }>
   */
  static async syncPlaidAccountsToStripe(userId: number): Promise<{ synced: number; errors: string[] }> {
    try {
      // Get user's Plaid bank accounts
      const plaidAccounts = await executeQuery<PlaidBankAccount>(
        `SELECT id, user_id, plaid_account_id, plaid_access_token, account_mask, 
                bank_name, account_type, routing_number, account_number_last4, is_primary
         FROM tbl_bank_accounts 
         WHERE user_id = ? AND plaid_access_token IS NOT NULL`,
        [userId]
      );

      let synced = 0;
      const errors: string[] = [];

      for (const account of plaidAccounts) {
        try {
          // Check if already mapped
          const existingMapping = await this.getPaymentMethodMapping(userId, account.plaid_account_id);
          if (existingMapping) {
            continue; // Skip already mapped accounts
          }

          // Check eligibility
          if (!account.routing_number) {
            errors.push(`Account ${account.plaid_account_id}: Missing routing number`);
            continue;
          }

          const eligibility = await this.checkBankAccountEligibility(account.account_type, account.routing_number);
          if (!eligibility.eligible) {
            errors.push(`Account ${account.plaid_account_id}: ${eligibility.reason}`);
            continue;
          }

          // Create payment method (this would need actual account number from Plaid)
          // Note: In real implementation, you'd need to get full account details from Plaid API
          // For now, we'll skip the actual creation and just log the intent
          logger.info('Would create Stripe payment method for Plaid account', {
            userId,
            plaidAccountId: account.plaid_account_id,
            bankName: account.bank_name
          });

          synced++;

        } catch (error) {
          errors.push(`Account ${account.plaid_account_id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      logger.info('Plaid to Stripe sync completed', {
        userId,
        totalAccounts: plaidAccounts.length,
        synced,
        errors: errors.length
      });

      return { synced, errors };

    } catch (error) {
      logger.error('Error syncing Plaid accounts to Stripe', {
        userId,
        error: error instanceof Error ? error.message : error
      });

      return {
        synced: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error occurred']
      };
    }
  }
}

export default StripePaymentMethodService;