import { executeQuery<PERSON>ingle, executeUpdate, executeTransaction } from '../utils/database';
import { updateWalletBalance, getWalletBalance, createWalletTransaction } from './walletService';
import { StripeWalletService } from './stripeWalletService';
import logger from '../utils/logger';

export interface TransferRecoveryResult {
  success: boolean;
  action: 'reversed' | 'refunded' | 'compensated' | 'manual_review_required';
  newBalance?: number;
  reversalTransactionId?: number;
  message: string;
  error?: string;
}

export interface FailedTransferInfo {
  id: number;
  userId: number;
  amount: number;
  type: string;
  referenceId: string;
  paymentProvider: string;
  metadata: any;
  failureReason?: string;
  createdAt: Date;
}

/**
 * Service for handling transfer failures and implementing recovery mechanisms
 */
export class TransferRecoveryService {

  /**
   * Handle a failed transfer and attempt recovery
   */
  static async handleFailedTransfer(
    transactionId: number,
    failureReason: string,
    automaticRecovery: boolean = true
  ): Promise<TransferRecoveryResult> {
    try {
      logger.info('Handling failed transfer', {
        transactionId,
        failureReason,
        automaticRecovery
      });

      // Get transaction details
      const transaction = await this.getTransactionDetails(transactionId);
      if (!transaction) {
        return {
          success: false,
          action: 'manual_review_required',
          message: 'Transaction not found',
          error: 'Transaction not found'
        };
      }

      // Update transaction status to failed
      await this.markTransactionAsFailed(transactionId, failureReason);

      // Determine recovery strategy based on transaction type
      const recoveryStrategy = this.determineRecoveryStrategy(transaction, failureReason);

      if (!automaticRecovery || recoveryStrategy === 'manual_review_required') {
        return {
          success: true,
          action: 'manual_review_required',
          message: 'Transaction marked for manual review'
        };
      }

      // Execute recovery strategy
      return await this.executeRecoveryStrategy(transaction, recoveryStrategy, failureReason);

    } catch (error) {
      logger.error('Error handling failed transfer', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId,
        failureReason
      });

      return {
        success: false,
        action: 'manual_review_required',
        message: 'Recovery failed - manual intervention required',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Reverse a wallet deduction for failed transfers
   */
  static async reverseWalletDeduction(
    userId: number,
    amount: number,
    originalTransactionId: number,
    reason: string
  ): Promise<TransferRecoveryResult> {
    try {
      logger.info('Reversing wallet deduction', {
        userId,
        amount,
        originalTransactionId,
        reason
      });

      // Get current wallet balance
      const currentBalance = await getWalletBalance(userId);
      if (currentBalance === null) {
        throw new Error('Unable to retrieve wallet balance');
      }

      // Calculate new balance
      const newBalance = currentBalance + Math.abs(amount);

      // Create reversal transaction and update balance in a transaction
      const queries = [
        {
          query: 'UPDATE tbl_masterwallet SET balance = ? WHERE user_id = ?',
          params: [newBalance, userId]
        }
      ];

      await executeTransaction(queries);

      // Create reversal transaction record
      const reversalTransactionId = await createWalletTransaction(
        userId,
        Math.abs(amount), // Positive amount for reversal
        `Reversal: ${reason}`,
        'reversal',
        `REV_${originalTransactionId}_${Date.now()}`,
        {
          originalTransactionId,
          reversalReason: reason,
          reversalType: 'wallet_deduction',
          paymentProvider: 'system'
        }
      );

      logger.info('Wallet deduction reversed successfully', {
        userId,
        originalBalance: currentBalance,
        newBalance,
        reversalAmount: Math.abs(amount),
        reversalTransactionId
      });

      return {
        success: true,
        action: 'reversed',
        newBalance,
        reversalTransactionId,
        message: `Wallet balance restored: $${Math.abs(amount).toFixed(2)} added back`
      };

    } catch (error) {
      logger.error('Error reversing wallet deduction', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId,
        amount,
        originalTransactionId
      });

      return {
        success: false,
        action: 'manual_review_required',
        message: 'Failed to reverse wallet deduction - manual intervention required',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Handle Stripe payment failures and reversals
   */
  static async handleStripeFailure(
    transactionId: number,
    stripeId: string,
    failureReason: string
  ): Promise<TransferRecoveryResult> {
    try {
      logger.info('Handling Stripe failure', {
        transactionId,
        stripeId,
        failureReason
      });

      const transaction = await this.getTransactionDetails(transactionId);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      // Check if this was a debit transaction (withdrawal/transfer out)
      if (transaction.amount < 0) {
        // Reverse the wallet deduction
        return await this.reverseWalletDeduction(
          transaction.userId,
          transaction.amount,
          transactionId,
          `Stripe failure: ${failureReason}`
        );
      } else {
        // For credit transactions (deposits), just mark as failed
        // No reversal needed since money wasn't added to wallet yet
        return {
          success: true,
          action: 'refunded',
          message: 'Deposit failed - no funds were charged'
        };
      }

    } catch (error) {
      logger.error('Error handling Stripe failure', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId,
        stripeId
      });

      return {
        success: false,
        action: 'manual_review_required',
        message: 'Failed to handle Stripe failure - manual intervention required',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Compensate user for system errors
   */
  static async compensateUser(
    userId: number,
    amount: number,
    reason: string,
    originalTransactionId?: number
  ): Promise<TransferRecoveryResult> {
    try {
      logger.info('Compensating user', {
        userId,
        amount,
        reason,
        originalTransactionId
      });

      // Get current wallet balance
      const currentBalance = await getWalletBalance(userId);
      if (currentBalance === null) {
        throw new Error('Unable to retrieve wallet balance');
      }

      // Add compensation to wallet
      const newBalance = currentBalance + Math.abs(amount);
      await updateWalletBalance(userId, newBalance);

      // Create compensation transaction
      const compensationTransactionId = await createWalletTransaction(
        userId,
        Math.abs(amount),
        `Compensation: ${reason}`,
        'compensation',
        `COMP_${originalTransactionId || Date.now()}_${Date.now()}`,
        {
          originalTransactionId,
          compensationReason: reason,
          compensationType: 'system_error',
          paymentProvider: 'system'
        }
      );

      logger.info('User compensated successfully', {
        userId,
        compensationAmount: Math.abs(amount),
        newBalance,
        compensationTransactionId
      });

      return {
        success: true,
        action: 'compensated',
        newBalance,
        reversalTransactionId: compensationTransactionId,
        message: `Compensation added: $${Math.abs(amount).toFixed(2)}`
      };

    } catch (error) {
      logger.error('Error compensating user', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId,
        amount,
        reason
      });

      return {
        success: false,
        action: 'manual_review_required',
        message: 'Failed to compensate user - manual intervention required',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get transaction details for recovery
   */
  private static async getTransactionDetails(transactionId: number): Promise<FailedTransferInfo | null> {
    try {
      const transaction = await executeQuerySingle(
        `SELECT id, user_id, amount, type, reference_id, payment_provider, 
         meta_data, created_at FROM tbl_wallet_transactions WHERE id = ?`,
        [transactionId]
      );

      if (!transaction) {
        return null;
      }

      return {
        id: transaction.id,
        userId: transaction.user_id,
        amount: transaction.amount,
        type: transaction.type,
        referenceId: transaction.reference_id,
        paymentProvider: transaction.payment_provider,
        metadata: transaction.meta_data ? JSON.parse(transaction.meta_data) : {},
        createdAt: new Date(transaction.created_at)
      };

    } catch (error) {
      logger.error('Error getting transaction details', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId
      });
      return null;
    }
  }

  /**
   * Mark transaction as failed
   */
  private static async markTransactionAsFailed(transactionId: number, reason: string): Promise<void> {
    await executeUpdate(
      `UPDATE tbl_wallet_transactions 
       SET status_id = 3, 
           meta_data = JSON_SET(COALESCE(meta_data, '{}'), '$.failureReason', ?, '$.failedAt', ?)
       WHERE id = ?`,
      [reason, new Date().toISOString(), transactionId]
    );
  }

  /**
   * Determine recovery strategy based on transaction type and failure reason
   */
  private static determineRecoveryStrategy(
    transaction: FailedTransferInfo,
    failureReason: string
  ): 'reversed' | 'refunded' | 'compensated' | 'manual_review_required' {
    // High-value transactions require manual review
    if (Math.abs(transaction.amount) > 10000) {
      return 'manual_review_required';
    }

    // Fraud-related failures require manual review
    if (failureReason.toLowerCase().includes('fraud') || 
        failureReason.toLowerCase().includes('suspicious')) {
      return 'manual_review_required';
    }

    // Bank-related failures for withdrawals should be reversed
    if (transaction.amount < 0 && 
        (failureReason.includes('bank') || failureReason.includes('account'))) {
      return 'reversed';
    }

    // System errors should be compensated
    if (failureReason.toLowerCase().includes('system') || 
        failureReason.toLowerCase().includes('timeout')) {
      return 'compensated';
    }

    // Default to reversal for debit transactions, refund for credit
    return transaction.amount < 0 ? 'reversed' : 'refunded';
  }

  /**
   * Execute the determined recovery strategy
   */
  private static async executeRecoveryStrategy(
    transaction: FailedTransferInfo,
    strategy: 'reversed' | 'refunded' | 'compensated' | 'manual_review_required',
    failureReason: string
  ): Promise<TransferRecoveryResult> {
    switch (strategy) {
      case 'reversed':
        return await this.reverseWalletDeduction(
          transaction.userId,
          transaction.amount,
          transaction.id,
          failureReason
        );

      case 'compensated':
        return await this.compensateUser(
          transaction.userId,
          Math.abs(transaction.amount),
          failureReason,
          transaction.id
        );

      case 'refunded':
        return {
          success: true,
          action: 'refunded',
          message: 'Transaction failed - no funds were processed'
        };

      default:
        return {
          success: true,
          action: 'manual_review_required',
          message: 'Transaction requires manual review'
        };
    }
  }

  /**
   * Get failed transactions that need recovery
   */
  static async getFailedTransactionsForRecovery(limit: number = 50): Promise<FailedTransferInfo[]> {
    try {
      const transactions = await executeQuerySingle(
        `SELECT id, user_id, amount, type, reference_id, payment_provider, 
         meta_data, created_at 
         FROM tbl_wallet_transactions 
         WHERE status_id = 3 
         AND JSON_EXTRACT(meta_data, '$.recoveryAttempted') IS NULL
         ORDER BY created_at DESC 
         LIMIT ?`,
        [limit]
      );

      if (!transactions) {
        return [];
      }

      const transactionArray = Array.isArray(transactions) ? transactions : [transactions];
      
      return transactionArray.map(tx => ({
        id: tx.id,
        userId: tx.user_id,
        amount: tx.amount,
        type: tx.type,
        referenceId: tx.reference_id,
        paymentProvider: tx.payment_provider,
        metadata: tx.meta_data ? JSON.parse(tx.meta_data) : {},
        failureReason: tx.meta_data ? JSON.parse(tx.meta_data).failureReason : undefined,
        createdAt: new Date(tx.created_at)
      }));

    } catch (error) {
      logger.error('Error getting failed transactions for recovery', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return [];
    }
  }
}
