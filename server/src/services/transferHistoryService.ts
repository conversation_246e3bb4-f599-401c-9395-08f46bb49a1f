import { executeQuery, executeQuerySingle } from '../utils/database';
import logger from '../utils/logger';

export interface TransferHistoryFilter {
  userId: number;
  transferType?: 'wallet_to_wallet' | 'bank_to_wallet' | 'wallet_to_bank' | 'bank_to_bank' | 'all';
  status?: 'completed' | 'pending' | 'failed' | 'all';
  dateFrom?: string;
  dateTo?: string;
  limit?: number;
  offset?: number;
  searchTerm?: string;
}

export interface TransferHistoryItem {
  id: number;
  type: string;
  transferType: 'wallet_to_wallet' | 'bank_to_wallet' | 'wallet_to_bank' | 'bank_to_bank';
  direction: 'incoming' | 'outgoing';
  amount: number;
  description: string;
  status: 'completed' | 'pending' | 'failed' | 'requires_action';
  paymentProvider: string;
  referenceId: string;
  createdAt: Date;
  updatedAt: Date;
  metadata: any;
  counterparty?: {
    userId?: number;
    name?: string;
    email?: string;
    bankName?: string;
    accountMask?: string;
  };
  fees?: {
    amount: number;
    description: string;
  };
}

export interface TransferHistorySummary {
  totalTransfers: number;
  completedTransfers: number;
  pendingTransfers: number;
  failedTransfers: number;
  totalAmountTransferred: number;
  totalFeesCharged: number;
  transfersByType: {
    walletToWallet: number;
    bankToWallet: number;
    walletToBank: number;
    bankToBank: number;
  };
}

/**
 * Comprehensive transfer history management service
 */
export class TransferHistoryService {

  /**
   * Get comprehensive transfer history for a user
   */
  static async getTransferHistory(filter: TransferHistoryFilter): Promise<{
    transfers: TransferHistoryItem[];
    summary: TransferHistorySummary;
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  }> {
    try {
      const { userId, transferType = 'all', status = 'all', limit = 50, offset = 0 } = filter;

      // Build WHERE clause
      let whereConditions = ['wt.user_id = ?'];
      let queryParams: any[] = [userId];

      // Filter by transfer type
      if (transferType !== 'all') {
        whereConditions.push("JSON_EXTRACT(wt.meta_data, '$.transferType') LIKE ?");
        queryParams.push(`%${transferType}%`);
      }

      // Filter by status
      if (status !== 'all') {
        const statusMap = {
          'completed': 1,
          'pending': 2,
          'failed': 3,
          'requires_action': 4
        };
        whereConditions.push('wt.status_id = ?');
        queryParams.push(statusMap[status]);
      }

      // Date range filter
      if (filter.dateFrom) {
        whereConditions.push('wt.created_at >= ?');
        queryParams.push(filter.dateFrom);
      }

      if (filter.dateTo) {
        whereConditions.push('wt.created_at <= ?');
        queryParams.push(filter.dateTo);
      }

      // Search term filter
      if (filter.searchTerm) {
        whereConditions.push('(wt.description LIKE ? OR wt.reference_id LIKE ?)');
        queryParams.push(`%${filter.searchTerm}%`, `%${filter.searchTerm}%`);
      }

      const whereClause = whereConditions.join(' AND ');

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM tbl_wallet_transactions wt
        WHERE ${whereClause}
      `;

      const countResult = await executeQuerySingle(countQuery, queryParams);
      const total = countResult?.total || 0;

      // Get transactions with details
      const transactionsQuery = `
        SELECT 
          wt.id,
          wt.user_id,
          wt.type,
          wt.amount,
          wt.description,
          wt.reference_id,
          wt.payment_provider,
          wt.status_id,
          wt.created_at,
          wt.updated_at,
          wt.meta_data,
          u.full_name as counterparty_name,
          u.email as counterparty_email
        FROM tbl_wallet_transactions wt
        LEFT JOIN tbl_users u ON (
          JSON_EXTRACT(wt.meta_data, '$.recipientUserId') = u.id OR
          JSON_EXTRACT(wt.meta_data, '$.senderUserId') = u.id
        )
        WHERE ${whereClause}
        ORDER BY wt.created_at DESC
        LIMIT ? OFFSET ?
      `;

      const transactions = await executeQuery(
        transactionsQuery, 
        [...queryParams, limit, offset]
      );

      // Transform transactions to TransferHistoryItem format
      const transfers = await Promise.all(
        (Array.isArray(transactions) ? transactions : [transactions]).map(async (tx) => {
          return await this.transformTransactionToHistoryItem(tx, userId);
        })
      );

      // Get summary statistics
      const summary = await this.getTransferSummary(userId, filter);

      return {
        transfers: transfers.filter(Boolean), // Remove any null results
        summary,
        pagination: {
          total,
          limit,
          offset,
          hasMore: (offset + limit) < total
        }
      };

    } catch (error) {
      logger.error('Error getting transfer history', {
        error: error instanceof Error ? error.message : 'Unknown error',
        filter
      });
      throw error;
    }
  }

  /**
   * Transform database transaction to TransferHistoryItem
   */
  private static async transformTransactionToHistoryItem(
    tx: any, 
    currentUserId: number
  ): Promise<TransferHistoryItem | null> {
    try {
      const metadata = tx.meta_data ? JSON.parse(tx.meta_data) : {};
      
      // Determine transfer type and direction
      const transferType = this.determineTransferType(tx.type, metadata);
      const direction = this.determineDirection(tx.amount, metadata, currentUserId);

      // Get status
      const statusMap = {
        1: 'completed' as const,
        2: 'pending' as const,
        3: 'failed' as const,
        4: 'requires_action' as const
      };
      const status = statusMap[tx.status_id] || 'pending';

      // Build counterparty information
      const counterparty = await this.buildCounterpartyInfo(metadata, tx);

      // Calculate fees if any
      const fees = metadata.fees ? {
        amount: metadata.fees.amount || 0,
        description: metadata.fees.description || 'Transaction fee'
      } : undefined;

      return {
        id: tx.id,
        type: tx.type,
        transferType,
        direction,
        amount: Math.abs(tx.amount),
        description: tx.description,
        status,
        paymentProvider: tx.payment_provider || 'unknown',
        referenceId: tx.reference_id,
        createdAt: new Date(tx.created_at),
        updatedAt: new Date(tx.updated_at || tx.created_at),
        metadata,
        counterparty,
        fees
      };

    } catch (error) {
      logger.error('Error transforming transaction to history item', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId: tx.id
      });
      return null;
    }
  }

  /**
   * Determine transfer type from transaction data
   */
  private static determineTransferType(
    type: string, 
    metadata: any
  ): 'wallet_to_wallet' | 'bank_to_wallet' | 'wallet_to_bank' | 'bank_to_bank' {
    // Check metadata first
    if (metadata.transferType) {
      if (metadata.transferType.includes('bank_to_bank')) return 'bank_to_bank';
      if (metadata.transferType.includes('wallet_to_wallet')) return 'wallet_to_wallet';
    }

    // Fallback to type analysis
    switch (type) {
      case 'deposit':
      case 'bank_deposit':
        return 'bank_to_wallet';
      case 'withdraw':
      case 'bank_withdraw':
        return 'wallet_to_bank';
      case 'transfer':
      case 'wallet_transfer':
        return 'wallet_to_wallet';
      case 'bank_transfer_in':
      case 'bank_transfer_out':
        return 'bank_to_bank';
      default:
        // Default based on payment provider
        if (metadata.paymentProvider === 'stripe' && metadata.bankAccountId) {
          return type.includes('debit') ? 'wallet_to_bank' : 'bank_to_wallet';
        }
        return 'wallet_to_wallet';
    }
  }

  /**
   * Determine transaction direction
   */
  private static determineDirection(
    amount: number, 
    metadata: any, 
    currentUserId: number
  ): 'incoming' | 'outgoing' {
    // Check metadata for explicit direction
    if (metadata.transferType) {
      if (metadata.transferType.includes('_in')) return 'incoming';
      if (metadata.transferType.includes('_out')) return 'outgoing';
    }

    // Check if current user is sender or recipient
    if (metadata.senderUserId === currentUserId) return 'outgoing';
    if (metadata.recipientUserId === currentUserId) return 'incoming';

    // Fallback to amount sign
    return amount > 0 ? 'incoming' : 'outgoing';
  }

  /**
   * Build counterparty information
   */
  private static async buildCounterpartyInfo(metadata: any, tx: any): Promise<any> {
    const counterparty: any = {};

    // Add user information if available
    if (tx.counterparty_name) {
      counterparty.name = tx.counterparty_name;
    }
    if (tx.counterparty_email) {
      counterparty.email = tx.counterparty_email;
    }

    // Add user IDs
    if (metadata.recipientUserId) {
      counterparty.userId = metadata.recipientUserId;
    }
    if (metadata.senderUserId) {
      counterparty.userId = metadata.senderUserId;
    }

    // Add bank information if available
    if (metadata.bankAccountId) {
      try {
        const bankAccount = await executeQuerySingle(
          'SELECT bank_name, account_mask FROM tbl_bank_accounts WHERE id = ?',
          [metadata.bankAccountId]
        );
        if (bankAccount) {
          counterparty.bankName = bankAccount.bank_name;
          counterparty.accountMask = bankAccount.account_mask;
        }
      } catch (error) {
        logger.warn('Failed to get bank account info for counterparty', {
          bankAccountId: metadata.bankAccountId
        });
      }
    }

    return Object.keys(counterparty).length > 0 ? counterparty : undefined;
  }

  /**
   * Get transfer summary statistics
   */
  private static async getTransferSummary(
    userId: number, 
    filter: TransferHistoryFilter
  ): Promise<TransferHistorySummary> {
    try {
      // Build base WHERE clause
      let whereConditions = ['user_id = ?'];
      let queryParams: any[] = [userId];

      // Add date filters if specified
      if (filter.dateFrom) {
        whereConditions.push('created_at >= ?');
        queryParams.push(filter.dateFrom);
      }

      if (filter.dateTo) {
        whereConditions.push('created_at <= ?');
        queryParams.push(filter.dateTo);
      }

      const whereClause = whereConditions.join(' AND ');

      // Get overall statistics
      const summaryQuery = `
        SELECT 
          COUNT(*) as total_transfers,
          SUM(CASE WHEN status_id = 1 THEN 1 ELSE 0 END) as completed_transfers,
          SUM(CASE WHEN status_id = 2 THEN 1 ELSE 0 END) as pending_transfers,
          SUM(CASE WHEN status_id = 3 THEN 1 ELSE 0 END) as failed_transfers,
          SUM(ABS(amount)) as total_amount_transferred
        FROM tbl_wallet_transactions
        WHERE ${whereClause}
      `;

      const summaryResult = await executeQuerySingle(summaryQuery, queryParams);

      // Get transfers by type (this is a simplified version)
      const typeQuery = `
        SELECT 
          type,
          COUNT(*) as count
        FROM tbl_wallet_transactions
        WHERE ${whereClause}
        GROUP BY type
      `;

      const typeResults = await executeQuery(typeQuery, queryParams);
      const typeArray = Array.isArray(typeResults) ? typeResults : [typeResults];

      // Map types to categories
      const transfersByType = {
        walletToWallet: 0,
        bankToWallet: 0,
        walletToBank: 0,
        bankToBank: 0
      };

      typeArray.forEach((result: any) => {
        if (result) {
          const type = result.type;
          const count = result.count;

          if (type.includes('transfer') || type.includes('wallet')) {
            transfersByType.walletToWallet += count;
          } else if (type.includes('deposit') || type.includes('bank_transfer_in')) {
            transfersByType.bankToWallet += count;
          } else if (type.includes('withdraw') || type.includes('bank_transfer_out')) {
            transfersByType.walletToBank += count;
          } else if (type.includes('bank_to_bank')) {
            transfersByType.bankToBank += count;
          }
        }
      });

      return {
        totalTransfers: summaryResult?.total_transfers || 0,
        completedTransfers: summaryResult?.completed_transfers || 0,
        pendingTransfers: summaryResult?.pending_transfers || 0,
        failedTransfers: summaryResult?.failed_transfers || 0,
        totalAmountTransferred: summaryResult?.total_amount_transferred || 0,
        totalFeesCharged: 0, // TODO: Calculate from metadata
        transfersByType
      };

    } catch (error) {
      logger.error('Error getting transfer summary', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId,
        filter
      });

      // Return empty summary on error
      return {
        totalTransfers: 0,
        completedTransfers: 0,
        pendingTransfers: 0,
        failedTransfers: 0,
        totalAmountTransferred: 0,
        totalFeesCharged: 0,
        transfersByType: {
          walletToWallet: 0,
          bankToWallet: 0,
          walletToBank: 0,
          bankToBank: 0
        }
      };
    }
  }

  /**
   * Get transfer details by ID
   */
  static async getTransferDetails(transferId: number, userId: number): Promise<TransferHistoryItem | null> {
    try {
      const transaction = await executeQuerySingle(
        `SELECT 
          wt.*,
          u.full_name as counterparty_name,
          u.email as counterparty_email
        FROM tbl_wallet_transactions wt
        LEFT JOIN tbl_users u ON (
          JSON_EXTRACT(wt.meta_data, '$.recipientUserId') = u.id OR
          JSON_EXTRACT(wt.meta_data, '$.senderUserId') = u.id
        )
        WHERE wt.id = ? AND wt.user_id = ?`,
        [transferId, userId]
      );

      if (!transaction) {
        return null;
      }

      return await this.transformTransactionToHistoryItem(transaction, userId);

    } catch (error) {
      logger.error('Error getting transfer details', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transferId,
        userId
      });
      return null;
    }
  }
}
