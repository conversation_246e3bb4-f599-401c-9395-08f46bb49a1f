import { 
  createPendingDeposit, 
  createWithdrawalHold, 
  confirmPendingDeposit, 
  confirmWithdrawalHold, 
  releaseFailedHold,
  getEnhancedWalletBalance 
} from './enhancedPendingBalanceService';
import { updateTransactionStatus } from './walletService';
import { executeUpdate, executeQuerySingle } from '../utils/database';
import logger from '../utils/logger';

/**
 * Comprehensive Wallet Balance Manager
 * Handles all wallet balance operations for Plaid transfers
 */

export interface WalletOperation {
  userId: number;
  amount: number;
  plaidTransferId: string;
  type: 'deposit' | 'withdrawal';
  description: string;
  metadata?: any;
}

export interface WalletBalanceResult {
  success: boolean;
  message: string;
  userId?: number;
  amount?: number;
  newBalance?: number;
  operation?: string;
  webhookEventId?: string;
  correlationId?: string;
  processingTime?: number;
  realTimeUpdate?: boolean;
}

/**
 * 1. ADD MONEY TO WALLET (Deposit)
 * When user adds money, create pending deposit
 * When funds are settled, add to main balance
 */
export async function handleDepositOperation(operation: WalletOperation): Promise<WalletBalanceResult> {
  try {
    logger.info('Handling deposit operation', { 
      userId: operation.userId, 
      amount: operation.amount, 
      plaidTransferId: operation.plaidTransferId 
    });

    // Create pending deposit hold
    const holdResult = await createPendingDeposit(
      operation.userId,
      operation.amount,
      operation.plaidTransferId,
      operation.description,
      operation.metadata
    );

    if (!holdResult.success) {
      return {
        success: false,
        message: holdResult.message || 'Failed to create pending deposit'
      };
    }

    // Get updated balance
    const balance = await getEnhancedWalletBalance(operation.userId);

    logger.info('Deposit operation initiated', {
      userId: operation.userId,
      amount: operation.amount,
      plaidTransferId: operation.plaidTransferId,
      holdId: holdResult.holdId,
      newBalance: balance
    });

    return {
      success: true,
      message: 'Deposit initiated successfully',
      userId: operation.userId,
      amount: operation.amount,
      newBalance: balance?.available_balance,
      operation: 'deposit_initiated'
    };

  } catch (error) {
    logger.error('Error handling deposit operation', { operation, error });
    return {
      success: false,
      message: 'Failed to handle deposit operation'
    };
  }
}

/**
 * 2. CONFIRM DEPOSIT (When funds are settled)
 * Move from pending to main balance
 */
export async function confirmDepositSettlement(plaidTransferId: string): Promise<WalletBalanceResult> {
  try {
    logger.info('Confirming deposit settlement', { plaidTransferId });

    // Confirm the pending deposit
    const result = await confirmPendingDeposit(plaidTransferId, 'settled');

    if (!result.success) {
      return {
        success: false,
        message: result.message || 'Failed to confirm deposit'
      };
    }

    // Get updated balance
    const balance = await getEnhancedWalletBalance(result.userId!);

    logger.info('Deposit settlement confirmed', {
      userId: result.userId,
      amount: result.amount,
      plaidTransferId,
      newBalance: balance
    });

    return {
      success: true,
      message: 'Deposit settled successfully',
      userId: result.userId,
      amount: result.amount,
      newBalance: balance?.available_balance,
      operation: 'deposit_settled'
    };

  } catch (error) {
    logger.error('Error confirming deposit settlement', { plaidTransferId, error });
    return {
      success: false,
      message: 'Failed to confirm deposit settlement'
    };
  }
}

/**
 * 3. WITHDRAWAL OPERATION
 * When user withdraws, block the amount
 * When transfer succeeds, deduct from main balance
 */
export async function handleWithdrawalOperation(operation: WalletOperation): Promise<WalletBalanceResult> {
  try {
    logger.info('Handling withdrawal operation', { 
      userId: operation.userId, 
      amount: operation.amount, 
      plaidTransferId: operation.plaidTransferId 
    });

    // Create withdrawal hold (blocks the amount)
    const holdResult = await createWithdrawalHold(
      operation.userId,
      operation.amount,
      operation.plaidTransferId,
      operation.description,
      operation.metadata
    );

    if (!holdResult.success) {
      return {
        success: false,
        message: holdResult.message || 'Failed to create withdrawal hold'
      };
    }

    // Get updated balance
    const balance = await getEnhancedWalletBalance(operation.userId);

    logger.info('Withdrawal operation initiated', {
      userId: operation.userId,
      amount: operation.amount,
      plaidTransferId: operation.plaidTransferId,
      holdId: holdResult.holdId,
      newBalance: balance
    });

    return {
      success: true,
      message: 'Withdrawal initiated successfully',
      userId: operation.userId,
      amount: operation.amount,
      newBalance: balance?.available_balance,
      operation: 'withdrawal_initiated'
    };

  } catch (error) {
    logger.error('Error handling withdrawal operation', { operation, error });
    return {
      success: false,
      message: 'Failed to handle withdrawal operation'
    };
  }
}

/**
 * 4. CONFIRM WITHDRAWAL (When transfer succeeds)
 * Deduct from main balance and remove from blocked
 */
export async function confirmWithdrawalSettlement(plaidTransferId: string): Promise<WalletBalanceResult> {
  try {
    logger.info('Confirming withdrawal settlement', { plaidTransferId });

    // Confirm the withdrawal hold
    const result = await confirmWithdrawalHold(plaidTransferId, 'settled');

    if (!result.success) {
      return {
        success: false,
        message: result.message || 'Failed to confirm withdrawal'
      };
    }

    // Get updated balance
    const balance = await getEnhancedWalletBalance(result.userId!);

    logger.info('Withdrawal settlement confirmed', {
      userId: result.userId,
      amount: result.amount,
      plaidTransferId,
      newBalance: balance
    });

    return {
      success: true,
      message: 'Withdrawal settled successfully',
      userId: result.userId,
      amount: result.amount,
      newBalance: balance?.available_balance,
      operation: 'withdrawal_settled'
    };

  } catch (error) {
    logger.error('Error confirming withdrawal settlement', { plaidTransferId, error });
    return {
      success: false,
      message: 'Failed to confirm withdrawal settlement'
    };
  }
}

/**
 * 5. HANDLE TRANSFER FAILURE
 * Restore the amount back to wallet (for both deposits and withdrawals)
 */
export async function handleTransferFailure(plaidTransferId: string, failureReason: string): Promise<WalletBalanceResult> {
  try {
    logger.info('Handling transfer failure', { plaidTransferId, failureReason });

    // Release the failed hold (restores balance)
    const result = await releaseFailedHold(plaidTransferId, failureReason);

    if (!result.success) {
      return {
        success: false,
        message: result.message || 'Failed to release failed hold'
      };
    }

    // Get updated balance
    const balance = await getEnhancedWalletBalance(result.userId!);

    logger.info('Transfer failure handled', {
      userId: result.userId,
      amount: result.amount,
      plaidTransferId,
      failureReason,
      newBalance: balance
    });

    return {
      success: true,
      message: 'Transfer failure handled - balance restored',
      userId: result.userId,
      amount: result.amount,
      newBalance: balance?.available_balance,
      operation: 'transfer_failed_balance_restored'
    };

  } catch (error) {
    logger.error('Error handling transfer failure', { plaidTransferId, failureReason, error });
    return {
      success: false,
      message: 'Failed to handle transfer failure'
    };
  }
}

/**
 * Enhanced webhook event correlation interface
 */
export interface WebhookEventContext {
  eventId?: string;
  correlationId?: string;
  webhookCode?: string;
  timestamp?: string;
  verified?: boolean;
  processingStartTime?: number;
}

/**
 * 6. ENHANCED WEBHOOK-AWARE TRANSFER STATUS HANDLER
 * Automatically handles all transfer status changes with webhook event correlation
 */
export async function handleTransferStatusChangeWithWebhook(
  plaidTransferId: string, 
  status: string, 
  failureReason?: string,
  webhookContext?: WebhookEventContext
): Promise<WalletBalanceResult> {
  const processingStartTime = webhookContext?.processingStartTime || Date.now();
  
  try {
    logger.info('Handling webhook-aware transfer status change', { 
      plaidTransferId, 
      status, 
      failureReason,
      webhookEventId: webhookContext?.eventId,
      correlationId: webhookContext?.correlationId,
      webhookCode: webhookContext?.webhookCode,
      verified: webhookContext?.verified
    });

    // Correlate webhook event with wallet transaction before processing
    if (webhookContext?.eventId) {
      await correlateWebhookEventWithWalletTransaction(plaidTransferId, webhookContext);
    }

    // Update transfer status in database with webhook correlation
    await updateTransferStatusWithWebhookCorrelation(plaidTransferId, status, failureReason, webhookContext);

    let result: WalletBalanceResult;

    // Handle based on status with enhanced webhook context
    switch (status?.toLowerCase()) {
      case 'posted':
      case 'settled':
      case 'funds_available':
        result = await handleSuccessfulTransferWithWebhook(plaidTransferId, status, webhookContext);
        break;

      case 'failed':
      case 'cancelled':
      case 'returned':
      case 'rejected':
        result = await handleFailedTransferWithWebhook(plaidTransferId, failureReason || 'Transfer failed', webhookContext);
        break;

      case 'pending':
      case 'pending_approval':
      case 'approved':
        result = await handlePendingTransferWithWebhook(plaidTransferId, status, webhookContext);
        break;

      default:
        result = {
          success: false,
          message: `Unknown transfer status: ${status}`,
          webhookEventId: webhookContext?.eventId,
          correlationId: webhookContext?.correlationId
        };
    }

    // Add webhook context to result
    const processingTime = Date.now() - processingStartTime;
    result.webhookEventId = webhookContext?.eventId;
    result.correlationId = webhookContext?.correlationId;
    result.processingTime = processingTime;
    result.realTimeUpdate = true;

    // Update webhook event correlation with final result
    if (webhookContext?.eventId && result.success) {
      await updateWebhookEventCorrelationResult(webhookContext.eventId, result);
    }

    logger.info('Webhook-aware transfer status change completed', {
      plaidTransferId,
      status,
      success: result.success,
      operation: result.operation,
      userId: result.userId,
      amount: result.amount,
      newBalance: result.newBalance,
      processingTime,
      webhookEventId: webhookContext?.eventId,
      correlationId: webhookContext?.correlationId
    });

    return result;

  } catch (error) {
    const processingTime = Date.now() - processingStartTime;
    logger.error('Error handling webhook-aware transfer status change', { 
      plaidTransferId, 
      status, 
      error,
      webhookEventId: webhookContext?.eventId,
      correlationId: webhookContext?.correlationId,
      processingTime
    });
    
    return {
      success: false,
      message: 'Failed to handle transfer status change',
      webhookEventId: webhookContext?.eventId,
      correlationId: webhookContext?.correlationId,
      processingTime
    };
  }
} /**
 * 7.
 LEGACY TRANSFER STATUS HANDLER (for backward compatibility)
 * Automatically handles all transfer status changes without webhook context
 */
export async function handleTransferStatusChange(
  plaidTransferId: string, 
  status: string, 
  failureReason?: string
): Promise<WalletBalanceResult> {
  // Call the enhanced version without webhook context for backward compatibility
  return await handleTransferStatusChangeWithWebhook(plaidTransferId, status, failureReason);
}

/**
 * Correlate webhook event with wallet transaction for tracking and debugging
 */
async function correlateWebhookEventWithWalletTransaction(
  plaidTransferId: string, 
  webhookContext: WebhookEventContext
): Promise<void> {
  try {
    // Find the wallet transaction associated with this transfer
    const walletTransaction = await executeQuerySingle(`
      SELECT id, user_id, amount, status_id, created_at 
      FROM tbl_wallet_transactions 
      WHERE reference_id = ? 
      ORDER BY created_at DESC 
      LIMIT 1
    `, [plaidTransferId]);

    if (walletTransaction) {
      // Update the webhook event with wallet transaction correlation
      await executeUpdate(`
        UPDATE tbl_webhook_events_enhanced 
        SET processed_result = JSON_SET(
          COALESCE(processed_result, '{}'),
          '$.wallet_correlation',
          JSON_OBJECT(
            'wallet_transaction_id', ?,
            'user_id', ?,
            'amount', ?,
            'wallet_status_id', ?,
            'wallet_created_at', ?,
            'correlation_timestamp', ?,
            'correlation_id', ?
          )
        )
        WHERE event_id = ?
      `, [
        walletTransaction.id,
        walletTransaction.user_id,
        walletTransaction.amount,
        walletTransaction.status_id,
        walletTransaction.created_at,
        new Date().toISOString(),
        webhookContext.correlationId,
        webhookContext.eventId
      ]);

      logger.info('Webhook event correlated with wallet transaction', {
        webhookEventId: webhookContext.eventId,
        correlationId: webhookContext.correlationId,
        plaidTransferId,
        walletTransactionId: walletTransaction.id,
        userId: walletTransaction.user_id,
        amount: walletTransaction.amount
      });
    } else {
      logger.warn('No wallet transaction found for transfer correlation', {
        webhookEventId: webhookContext.eventId,
        correlationId: webhookContext.correlationId,
        plaidTransferId
      });
    }

  } catch (error) {
    logger.error('Error correlating webhook event with wallet transaction', {
      webhookEventId: webhookContext.eventId,
      correlationId: webhookContext.correlationId,
      plaidTransferId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    // Don't throw error as this is non-critical correlation tracking
  }
}

/**
 * Update transfer status with webhook correlation information
 */
async function updateTransferStatusWithWebhookCorrelation(
  plaidTransferId: string, 
  status: string, 
  failureReason?: string,
  webhookContext?: WebhookEventContext
): Promise<void> {
  try {
    // Update the transaction status with enhanced webhook correlation (using wallet service)
    await updateTransactionStatus(plaidTransferId, status, failureReason ? { failureReason } : undefined);

    // If we have webhook context, also update the wallet transaction with webhook correlation
    if (webhookContext?.eventId) {
      await executeUpdate(`
        UPDATE tbl_wallet_transactions 
        SET meta_data = JSON_SET(
          COALESCE(meta_data, '{}'),
          '$.webhook_correlation',
          JSON_OBJECT(
            'webhook_event_id', ?,
            'correlation_id', ?,
            'webhook_code', ?,
            'webhook_timestamp', ?,
            'verified', ?,
            'real_time_update', true,
            'status_update_timestamp', ?
          )
        )
        WHERE reference_id = ?
      `, [
        webhookContext.eventId,
        webhookContext.correlationId,
        webhookContext.webhookCode,
        webhookContext.timestamp,
        webhookContext.verified || false,
        new Date().toISOString(),
        plaidTransferId
      ]);

      logger.info('Transfer status updated with webhook correlation', {
        plaidTransferId,
        status,
        webhookEventId: webhookContext.eventId,
        correlationId: webhookContext.correlationId,
        verified: webhookContext.verified
      });
    }

  } catch (error) {
    logger.error('Error updating transfer status with webhook correlation', {
      plaidTransferId,
      status,
      webhookEventId: webhookContext?.eventId,
      correlationId: webhookContext?.correlationId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error; // This is critical, so throw the error
  }
}

/**
 * Handle successful transfer with webhook context
 */
async function handleSuccessfulTransferWithWebhook(
  plaidTransferId: string, 
  status: string,
  webhookContext?: WebhookEventContext
): Promise<WalletBalanceResult> {
  try {
    logger.info('Handling successful transfer with webhook context', {
      plaidTransferId,
      status,
      webhookEventId: webhookContext?.eventId,
      correlationId: webhookContext?.correlationId
    });

    // Try to confirm deposit first
    let result = await confirmPendingDeposit(plaidTransferId, status);
    if (result.success) {
      // Get updated balance for real-time response
      const balance = await getEnhancedWalletBalance(result.userId!);
      
      return {
        success: true,
        message: 'Deposit confirmed successfully via webhook',
        userId: result.userId,
        amount: result.amount,
        newBalance: balance?.available_balance,
        operation: 'deposit_confirmed_webhook',
        realTimeUpdate: true
      };
    }

    // Try to confirm withdrawal
    result = await confirmWithdrawalHold(plaidTransferId, status);
    if (result.success) {
      // Get updated balance for real-time response
      const balance = await getEnhancedWalletBalance(result.userId!);
      
      return {
        success: true,
        message: 'Withdrawal confirmed successfully via webhook',
        userId: result.userId,
        amount: result.amount,
        newBalance: balance?.available_balance,
        operation: 'withdrawal_confirmed_webhook',
        realTimeUpdate: true
      };
    }

    return {
      success: false,
      message: 'No pending holds found for this transfer',
      realTimeUpdate: true
    };

  } catch (error) {
    logger.error('Error handling successful transfer with webhook', {
      plaidTransferId,
      status,
      webhookEventId: webhookContext?.eventId,
      correlationId: webhookContext?.correlationId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Handle failed transfer with webhook context
 */
async function handleFailedTransferWithWebhook(
  plaidTransferId: string, 
  failureReason: string,
  webhookContext?: WebhookEventContext
): Promise<WalletBalanceResult> {
  try {
    logger.info('Handling failed transfer with webhook context', {
      plaidTransferId,
      failureReason,
      webhookEventId: webhookContext?.eventId,
      correlationId: webhookContext?.correlationId
    });

    // Release the failed hold (restores balance)
    const result = await releaseFailedHold(plaidTransferId, failureReason);

    if (!result.success) {
      return {
        success: false,
        message: result.message || 'Failed to release failed hold',
        realTimeUpdate: true
      };
    }

    // Get updated balance for real-time response
    const balance = await getEnhancedWalletBalance(result.userId!);

    return {
      success: true,
      message: 'Transfer failure handled via webhook - balance restored',
      userId: result.userId,
      amount: result.amount,
      newBalance: balance?.available_balance,
      operation: 'transfer_failed_balance_restored_webhook',
      realTimeUpdate: true
    };

  } catch (error) {
    logger.error('Error handling failed transfer with webhook', {
      plaidTransferId,
      failureReason,
      webhookEventId: webhookContext?.eventId,
      correlationId: webhookContext?.correlationId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Handle pending transfer with webhook context
 */
async function handlePendingTransferWithWebhook(
  plaidTransferId: string, 
  status: string,
  webhookContext?: WebhookEventContext
): Promise<WalletBalanceResult> {
  try {
    logger.info('Handling pending transfer with webhook context', {
      plaidTransferId,
      status,
      webhookEventId: webhookContext?.eventId,
      correlationId: webhookContext?.correlationId
    });

    // For pending statuses, just update status without balance changes
    // but provide real-time feedback
    return {
      success: true,
      message: `Transfer status updated to ${status} via webhook`,
      operation: 'status_updated_webhook',
      realTimeUpdate: true
    };

  } catch (error) {
    logger.error('Error handling pending transfer with webhook', {
      plaidTransferId,
      status,
      webhookEventId: webhookContext?.eventId,
      correlationId: webhookContext?.correlationId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Update webhook event correlation with final processing result
 */
async function updateWebhookEventCorrelationResult(
  webhookEventId: string, 
  result: WalletBalanceResult
): Promise<void> {
  try {
    await executeUpdate(`
      UPDATE tbl_webhook_events_enhanced 
      SET processed_result = JSON_SET(
        COALESCE(processed_result, '{}'),
        '$.wallet_processing_result',
        JSON_OBJECT(
          'success', ?,
          'operation', ?,
          'user_id', ?,
          'amount', ?,
          'new_balance', ?,
          'processing_time', ?,
          'real_time_update', ?,
          'result_timestamp', ?
        )
      )
      WHERE event_id = ?
    `, [
      result.success,
      result.operation,
      result.userId,
      result.amount,
      result.newBalance,
      result.processingTime,
      result.realTimeUpdate,
      new Date().toISOString(),
      webhookEventId
    ]);

    logger.info('Webhook event correlation updated with processing result', {
      webhookEventId,
      success: result.success,
      operation: result.operation,
      userId: result.userId,
      processingTime: result.processingTime
    });

  } catch (error) {
    logger.error('Error updating webhook event correlation result', {
      webhookEventId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    // Don't throw error as this is non-critical correlation tracking
  }
}

/**
 * Get real-time balance update for webhook response
 * This provides immediate balance information for webhook processing
 */
export async function getRealTimeBalanceUpdate(
  userId: number,
  webhookContext?: WebhookEventContext
): Promise<{
  availableBalance: number;
  pendingBalance: number;
  totalBalance: number;
  lastUpdated: string;
  webhookTriggered: boolean;
}> {
  try {
    const balance = await getEnhancedWalletBalance(userId);
    
    return {
      availableBalance: balance?.available_balance || 0,
      pendingBalance: balance?.pending_balance || 0,
      totalBalance: (balance?.balance || 0) + (balance?.pending_balance || 0),
      lastUpdated: new Date().toISOString(),
      webhookTriggered: !!webhookContext?.eventId
    };

  } catch (error) {
    logger.error('Error getting real-time balance update', {
      userId,
      webhookEventId: webhookContext?.eventId,
      correlationId: webhookContext?.correlationId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    return {
      availableBalance: 0,
      pendingBalance: 0,
      totalBalance: 0,
      lastUpdated: new Date().toISOString(),
      webhookTriggered: false
    };
  }
}

/**
 * Trigger real-time balance notification (for future WebSocket integration)
 * This function can be extended to send real-time notifications to connected clients
 */
export async function triggerRealTimeBalanceNotification(
  userId: number,
  balanceUpdate: WalletBalanceResult,
  webhookContext?: WebhookEventContext
): Promise<void> {
  try {
    logger.info('Triggering real-time balance notification', {
      userId,
      operation: balanceUpdate.operation,
      amount: balanceUpdate.amount,
      newBalance: balanceUpdate.newBalance,
      webhookEventId: webhookContext?.eventId,
      correlationId: webhookContext?.correlationId,
      realTimeUpdate: balanceUpdate.realTimeUpdate
    });

    // TODO: Implement WebSocket notification to connected clients
    // This is where you would send real-time updates to the frontend
    // Example: websocketService.sendBalanceUpdate(userId, balanceUpdate);

    // For now, we'll just log the notification
    // In a future implementation, this could integrate with:
    // - WebSocket connections
    // - Push notifications
    // - Real-time dashboard updates

  } catch (error) {
    logger.error('Error triggering real-time balance notification', {
      userId,
      webhookEventId: webhookContext?.eventId,
      correlationId: webhookContext?.correlationId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    // Don't throw error as this is non-critical notification
  }
}