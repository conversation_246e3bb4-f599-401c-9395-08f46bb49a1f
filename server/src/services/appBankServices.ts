
import { executeQuery, executeQuery<PERSON>ingle, executeUpdate } from '../utils/database';
import logger from '../utils/logger';
import { getPaymentMethodById } from './paymentMethodService';
import { StripeWalletService } from './stripeWalletService';
import { StripePaymentMethodService } from './stripePaymentMethodService';
import { getPrimaryBankAccount, getUserMasterWallet, verifyWalletMasterPin } from './walletService';

/**
 * Add or update a bank account for the user
 * @param params - Bank account details (id for update, rest for insert)
 * @returns Promise<{ updated: boolean, inserted: boolean }>
 */
export async function addOrUpdateBankAccount(params: {
  id?: string;
  bank_name: string;
  account_number_last4: string;
  routing_number: string;
  account_type: string;
  userId: string;
  plaid_account_id?: string;
  account_mask?: string;
  plaid_item_id?: string;
  plaid_access_token?: string;
  stripe_bank_account_id?: string;
}): Promise<{ updated?: boolean; inserted?: boolean }> {
  try {
    const { id, bank_name, plaid_account_id, account_mask, account_number_last4, routing_number, account_type, userId, plaid_access_token, plaid_item_id, stripe_bank_account_id } = params;
    if (id) {
      // Update existing bank account
      const account = await executeQuerySingle('SELECT * FROM tbl_bank_accounts WHERE id = ? AND user_id = ?', [id, userId]);
      if (!account) {
        return { updated: false };
      }
      await executeUpdate(
        `UPDATE tbl_bank_accounts SET bank_name = ?, account_number_last4 = ?, routing_number = ?, account_type = ?, stripe_bank_account_id = ? WHERE id = ? AND user_id = ?`,
        [bank_name, account_number_last4, routing_number, account_type, stripe_bank_account_id || null, id, userId]
      );
      return { updated: true };
    } else {
      // Insert new bank account
      const result = await executeUpdate(
            `INSERT INTO tbl_bank_accounts
           (user_id, plaid_account_id, account_mask, bank_name, account_type, routing_number, account_number_last4, is_primary, plaid_access_token, plaid_item_id, stripe_bank_account_id)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,

        [userId, plaid_account_id || null, account_mask || null, bank_name, account_type, routing_number, account_number_last4, 1, plaid_access_token || null, plaid_item_id || null, stripe_bank_account_id || null]
      );

      // Create Stripe customer and payment method for the new bank account (only if not already provided)
      const newBankAccountId = result.insertId;
      if (newBankAccountId && !stripe_bank_account_id) {
        try {
          logger.info('Creating Stripe payment method for new bank account', {
            userId,
            bankAccountId: newBankAccountId,
            bankName: bank_name
          });

          // Get or create Stripe customer
          const stripeCustomerId = await StripeWalletService.getOrCreateStripeCustomer(parseInt(userId));

          // Create payment method for the bank account
          const paymentMethodResult = await StripePaymentMethodService.getOrCreatePaymentMethod(
            parseInt(userId),
            newBankAccountId.toString(),
            stripeCustomerId
          );

          if (paymentMethodResult.success && paymentMethodResult.paymentMethodId) {
            logger.info('Successfully created Stripe payment method for new bank account', {
              userId,
              bankAccountId: newBankAccountId,
              paymentMethodId: paymentMethodResult.paymentMethodId
            });
             await executeUpdate(
              'UPDATE tbl_bank_accounts SET stripe_bank_account_id = ? WHERE id = ?',
              [paymentMethodResult.paymentMethodId, newBankAccountId]
            );
          } else {
            logger.warn('Failed to create Stripe payment method for new bank account', {
              userId,
              bankAccountId: newBankAccountId,
              error: paymentMethodResult.error
            });
          }
        } catch (error) {
          // Don't fail the bank account creation if Stripe integration fails
          logger.error('Error creating Stripe payment method for new bank account', {
            userId,
            bankAccountId: newBankAccountId,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return { inserted: true };
    }
  } catch (error) {
    logger.error('Error in addOrUpdateBankAccount', { error });
    throw error;
  }
}

/**
 * List all bank accounts for the user
 * @param userId - User ID
 * @returns Promise<any[]>
 */
export async function getBankAccounts(userId: string): Promise<any[]> {
  try {
    const accounts = await executeQuery('SELECT * FROM tbl_bank_accounts WHERE user_id = ? ORDER BY id DESC, created_at ASC', [userId]);
    return accounts;
  } catch (error) {
    logger.error('Error in getBankAccounts', { error });
    throw error;
  }
}

/**
 * List all wallet transactions for the user
 * @param userId - User ID
 * @returns Promise<any[]>
 */
export async function getTransactionsHistory(userId: string, page: number = 1, pageSize: number = 10): Promise<{ data: any[], totalRecords: number }> {
  try {
    const offset = (page - 1) * pageSize;

    // Get total count
    const countResult = await executeQuery(
      `SELECT COUNT(*) AS total FROM tbl_wallet_transactions WHERE user_id = ?`,
      [userId]
    );
    const totalRecords = countResult[0]?.total || 0;

    console.log(userId, pageSize, offset,'jhjkdfhgkdfjg')
console.log(countResult,'countResult')
    // Fetch paginated transactions
    const transactions =await executeQuery(
  `SELECT * FROM tbl_wallet_transactions WHERE user_id = ? ORDER BY id DESC, created_at ASC LIMIT ${pageSize} OFFSET ${offset};`, [userId]);
console.log(transactions,'trrrrrrrrr')

    return { data: transactions, totalRecords };
  } catch (error) {
    logger.error('Error in getTransactionsHistory', { error });
    throw error;
  }
}
export async function getReportIssues(){
  try {
    const result = await executeQuery('SELECT * FROM tbl_transaction_report_issues',);
    return result;
}catch (error) {
    logger.error('Error in getTransactionsHistory', { error });
    throw error;
  }
}

export async function getTransactionsDetail(userId: string, transaction_id:string): Promise<{ data: any[] }> {
  try {


    // Fetch paginated transactions
    const transactions =await executeQuery(
  `SELECT
  t.id,
  t.user_id,
  t_pair.user_id AS other_user,
  t.type,
  t.amount,
   t.plaid_status,
  t.reference_id,
  t.description,
  t.created_at,
  t.meta_data,
  CASE
    WHEN t.type = 'debit' THEN u_current.full_name
    ELSE u_pair.full_name
  END AS sender_name,
  CASE
    WHEN t.type = 'debit' THEN u_pair.full_name
    ELSE u_current.full_name
  END AS receiver_name
FROM
  tbl_wallet_transactions t
  JOIN tbl_wallet_transactions t_pair
    ON t.reference_id = t_pair.reference_id AND t.id != t_pair.id
  LEFT JOIN tbl_users u_current ON u_current.id = t.user_id
  LEFT JOIN tbl_users u_pair ON u_pair.id = t_pair.user_id
WHERE
  t.reference_id = ?
  AND t.user_id = ?;`, [ transaction_id, userId]);
console.log(transactions,'trrrrrrrrr')

    return { data: transactions };
  } catch (error) {
    logger.error('Error in getTransactionsHistory', { error });
    throw error;
  }
}

/**
 * List wallet info for the user
 * @param userId - User ID
 * @returns Promise<any[]>
 */
export async function getWallets(userId: string): Promise<any[]> {
  try {
    const wallets = await executeQuery(
      `SELECT id , user_id , wallet_unique_id , name , username , balance FROM tbl_masterwallet WHERE user_id = ? ORDER BY id DESC, created_at ASC`,
      [userId]
    );
    return wallets;
  } catch (error) {
    logger.error('Error in getWallets', { error });
    throw error;
  }
}

export async function setPin(userId: string, pin: string): Promise<void> {
  try {
    await executeUpdate(
      `UPDATE tbl_masterwallet SET wallet_master_pin = ? WHERE user_id = ?`,
      [pin, userId]
    );
  } catch (error) {
    logger.error('Error in setUpPin', { error });
    throw error;
  }
}
export async function createWalletTransaction(
  userId: number,
  amount: number,
  description: string,
  type: string = 'wallet_transfer',
  referenceId?: string,
  metaData?: any,
  statusId: number = 1, // Default to completed (1), can be set to pending (2) or failed (3)
  plaidEventId?: string
): Promise<number | null> {
  try {
    const result = await executeUpdate(
      `INSERT INTO tbl_wallet_transactions
       (user_id, type, amount, reference_id, payment_provider, description, status_id, created_at, meta_data, plaid_event_id)
       VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?)`,
      [
        userId,
        type,
        amount,
        referenceId || `TXN_${Date.now()}_${userId}`,
        type,
        description,
        statusId,
        metaData ? JSON.stringify(metaData) : null,
        plaidEventId || null
      ]
    );

    return result && result.insertId ? result.insertId : null;
  } catch (error) {
    logger.error('Error creating wallet transaction', { userId, amount, type, error });
    return null;
  }
}



export async function addMoneyFromBank(
  userId: number,
  amount: number,
  pin: string,
  bankAccountId?: string
): Promise<{ success: boolean, newBalance?: number, message?: string, transactionId?: number }> {
  try {
    // Get user's wallet
    const wallet = await getUserMasterWallet(userId);
    if (!wallet) {
      return { success: false, message: 'Wallet not found' };
    }

    // Verify PIN
    const isPinValid = await verifyWalletMasterPin(userId, pin);
    if (!isPinValid) {
      return { success: false, message: 'Invalid PIN' };
    }

    // Get bank account
    let bankAccount = await (async () => {
      if (bankAccountId) {
        return await executeQuerySingle(
          'SELECT * FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
          [bankAccountId, userId]
        );
      }
      return await getPrimaryBankAccount(userId);
    })();
    // bankAccount = {
    //   "id":"22",
    //   "bank_name": "Bank of Tests",
    //   "bank_account_number": "",
    //   "account_mask":"1111"
    // };

    if (!bankAccount) {
      return { success: false, message: 'Bank account not found' };
    }

    // Use Stripe for payment processing
    try {
      const { StripeWalletService } = await import('./stripeWalletService');

      // Create the payment using Stripe
      const stripeResult = await StripeWalletService.addMoneyToWallet(
        userId,
        amount,
        bankAccount.plaid_account_id,
        `Add money to wallet from ${bankAccount.bank_name}`
      );

      if (!stripeResult.success) {
        return { success: false, message: stripeResult.error || 'Failed to add money from bank' };
      }

      // Create transaction record with appropriate status
      const transactionId = await createWalletTransaction(
        userId,
        amount,
        `Add money from ${bankAccount.bank_name}`,
        'add_money',
        stripeResult.paymentIntent?.id || `ADD_MONEY_${Date.now()}`,
        {
          bankAccountId: bankAccount.id,
          bankName: bankAccount.bank_name,
          accountMask: bankAccount.account_mask,
          stripePaymentIntentId: stripeResult.paymentIntent?.id,
          stripeStatus: stripeResult.paymentIntent?.status,
          paymentProvider: 'stripe'
        },
        stripeResult.paymentIntent?.status === 'succeeded' ? 1 : 2 // 1 = completed, 2 = pending
      ) || 0;

      logger.info('Stripe payment initiated for add money', {
        userId,
        amount,
        stripePaymentIntentId: stripeResult.paymentIntent?.id,
        transactionId,
        status: stripeResult.paymentIntent?.status
      });

      const statusMessage = stripeResult.paymentIntent?.status === 'succeeded'
        ? `${amount.toFixed(2)} has been added to your wallet.`
        : `Payment initiated. ${amount.toFixed(2)} will be added to your wallet once confirmed by your bank.`;

      return {
        success: true,
        newBalance: stripeResult.newBalance,
        transactionId,
        message: statusMessage
      };
    } catch (error) {
      logger.error('Error adding money from bank', { userId, amount, error });
      return { success: false, message: 'Failed to add money from bank' };
    }
  } catch (error) {
    logger.error('Error adding money from bank', { userId, amount, error });
    return { success: false, message: 'Failed to add money from bank' };
  }
}

export async function withdrawMoneyToBank(
  userId: number,
  amount: number,
  pin: string,
  bankAccountId?: string,
  paymentMethod: string = 'ach_standard'
): Promise<{ success: boolean, newBalance?: number, message?: string, transactionId?: number }> {
  try {
    logger.info('Starting withdrawal process', { userId, amount, paymentMethod, bankAccountId });

    // Validate amount format - must be a positive number with exactly 2 decimal places
    const amountStr = amount.toString();
    const decimalParts = amountStr.split('.');
    if (amount <= 0 || decimalParts.length > 2 || (decimalParts.length === 2 && decimalParts[1].length !== 2)) {
      logger.warn('Invalid amount format for withdrawal', { userId, amount });
      return { success: false, message: 'Withdrawal failed: amount must be a decimal with 2 places greater than 0, such as 0.10' };
    }

    // Format amount to ensure exactly 2 decimal places
    const formattedAmount = parseFloat(amount.toFixed(2));

    // Get user's wallet
    const wallet = await getUserMasterWallet(userId);
    if (!wallet) {
      logger.error('Wallet not found for user', { userId });
      return { success: false, message: 'Wallet not found' };
    }
    logger.info('Wallet retrieved successfully', { userId, walletId: wallet.id });

    // Verify PIN
    const isPinValid = await verifyWalletMasterPin(userId, pin);
    if (!isPinValid) {
      logger.warn('Invalid PIN attempt for withdrawal', { userId });
      return { success: false, message: 'Invalid PIN' };
    }
    logger.info('PIN verification successful', { userId });

    // Get selected bank account or primary bank account
    let  bankAccount;
    if (bankAccountId) {
      logger.info('Retrieving specific bank account', { userId, bankAccountId });
      // Get specific bank account by ID
      bankAccount = await executeQuerySingle(
        'SELECT * FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
        [bankAccountId, userId]
      );
      if (!bankAccount) {
        logger.error('Selected bank account not found', { userId, bankAccountId });
        return { success: false, message: 'Selected bank account not found or does not belong to user.' };
      }
    } else {
      logger.info('Retrieving primary bank account', { userId });
      // Fall back to primary bank account
      bankAccount = await getPrimaryBankAccount(userId);
      if (!bankAccount) {
        logger.error('No primary bank account found', { userId });
        return { success: false, message: 'No primary bank account found. Please connect a bank account first.' };
      }
    }
    
    logger.info('Bank account retrieved successfully', { userId, bankAccountId: bankAccount.id, bankName: bankAccount.bank_name });

    // Get payment method details from database
    logger.info('Retrieving payment method', { paymentMethod });
    const selectedMethod = await getPaymentMethodById(paymentMethod);
    if (!selectedMethod) {
      logger.error('Invalid payment method selected', { paymentMethod });
      return { success: false, message: 'Invalid payment method selected' };
    }

    // Use Stripe for withdrawal processing
    logger.info('Initiating Stripe withdrawal', { userId, amount, bankAccountId: bankAccount.id });

    try {
      // Import the Stripe wallet service
      const { StripeWalletService } = await import('./stripeWalletService');

      // Create the withdrawal using Stripe
      const stripeResult = await StripeWalletService.withdrawFromWallet(
        userId,
        formattedAmount, // Include processing fee in withdrawal amount
        (bankAccount as any).stripe_bank_account_id,
        `Withdrawal to ${bankAccount.bank_name}`
      );

      if (!stripeResult.success) {
        logger.error('Stripe withdrawal failed', { userId, error: stripeResult.error });
        return { success: false, message: stripeResult.error || 'Failed to initiate withdrawal' };
      }

      logger.info('Stripe withdrawal initiated successfully', {
        userId,
        transferId: stripeResult.transferId,
        newBalance: stripeResult.newBalance
      });

      // Create transaction record with pending status
      const transactionId = await createWalletTransaction(
        userId,
        -formattedAmount, // Negative total amount including fees for withdrawal
        `Withdrawal to ${bankAccount.bank_name}`,
        'bank_transfer',
        stripeResult.transferId || `WITHDRAW_${Date.now()}`,
        {
          bankAccountId: bankAccount.id,
          bankName: bankAccount.bank_name,
          accountMask: bankAccount.account_mask,
          paymentMethod: paymentMethod,
          paymentMethodName: selectedMethod.name,
          withdrawalAmount: amount,
          totalAmount: formattedAmount,
          estimatedArrival: selectedMethod.processing_time,
          stripeTransferId: stripeResult.transferId,
        },
        2 // status_id = 2 for pending
      );

      if (!transactionId) {
        logger.error('Failed to create transaction record', { userId });
        return { success: false, message: 'Failed to create transaction record' };
      }
      logger.info('Transaction record created successfully', { userId, transactionId });

      return {
        success: true,
        newBalance: stripeResult.newBalance, // Return available balance, not main balance
        transactionId,
        message: `Withdrawal initiated. ${amount.toFixed(2)} will be sent to ${bankAccount.bank_name} once confirmed. Expected arrival: ${selectedMethod.processing_time}.`
      };
    } catch (error) {
      logger.error('Error with Stripe transfer', { userId, amount, error });
      return { success: false, message: 'Failed to initiate withdrawal with Stripe' };
    }
  } catch (error) {
    logger.error('Error withdrawing money to bank', {
      userId,
      amount,
      error: error instanceof Error ? error.message : 'Unknown error',
      errorStack: error instanceof Error ? error.stack : undefined,
      errorName: error instanceof Error ? error.name : undefined,
      sqlState: (error as any)?.sqlState,
      sqlMessage: (error as any)?.sqlMessage,
      errno: (error as any)?.errno,
      code: (error as any)?.code
    });
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to withdraw money. Please try again.'
    };
  }
}
