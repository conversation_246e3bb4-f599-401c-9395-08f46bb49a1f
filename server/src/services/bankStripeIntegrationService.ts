import { executeQuerySingle, executeQuery } from '../utils/database';
import { StripeWalletService } from './stripeWalletService';
import { StripePaymentMethodService } from './stripePaymentMethodService';
import logger from '../utils/logger';

export interface BankStripeIntegrationResult {
  success: boolean;
  stripeCustomerId?: string;
  paymentMethodsCreated: number;
  errors: string[];
  details: {
    userId: number;
    bankAccountsProcessed: number;
    paymentMethodIds: string[];
  };
}

/**
 * Service to handle integration between bank accounts and Stripe
 */
export class BankStripeIntegrationService {

  /**
   * Ensure user has Stripe customer and payment methods for all bank accounts
   */
  static async ensureStripeIntegrationForUser(userId: number): Promise<BankStripeIntegrationResult> {
    try {
      logger.info('Ensuring Stripe integration for user', { userId });

      const result: BankStripeIntegrationResult = {
        success: false,
        paymentMethodsCreated: 0,
        errors: [],
        details: {
          userId,
          bankAccountsProcessed: 0,
          paymentMethodIds: []
        }
      };

      // Step 1: Get or create Stripe customer
      try {
        const stripeCustomerId = await StripeWalletService.getOrCreateStripeCustomer(userId);
        result.stripeCustomerId = stripeCustomerId;
        logger.info('Stripe customer ensured', { userId, stripeCustomerId });
      } catch (error) {
        const errorMsg = `Failed to create Stripe customer: ${error instanceof Error ? error.message : 'Unknown error'}`;
        result.errors.push(errorMsg);
        logger.error('Failed to ensure Stripe customer', { userId, error: errorMsg });
        return result;
      }

      // Step 2: Get all bank accounts for the user
      const bankAccounts = await executeQuery(
        'SELECT id, bank_name, account_mask, plaid_account_id FROM tbl_bank_accounts WHERE user_id = ?',
        [userId]
      );

      const accountsArray = Array.isArray(bankAccounts) ? bankAccounts : (bankAccounts ? [bankAccounts] : []);
      result.details.bankAccountsProcessed = accountsArray.length;

      if (accountsArray.length === 0) {
        logger.info('No bank accounts found for user', { userId });
        result.success = true;
        return result;
      }

      // Step 3: Create payment methods for each bank account
      for (const account of accountsArray) {
        try {
          const paymentMethodResult = await StripePaymentMethodService.getOrCreatePaymentMethod(
            userId,
            account.id,
            result.stripeCustomerId!
          );

          if (paymentMethodResult.success && paymentMethodResult.paymentMethodId) {
            result.paymentMethodsCreated++;
            result.details.paymentMethodIds.push(paymentMethodResult.paymentMethodId);
            
            logger.info('Payment method ensured for bank account', {
              userId,
              bankAccountId: account.id,
              paymentMethodId: paymentMethodResult.paymentMethodId
            });
          } else {
            const errorMsg = `Failed to create payment method for account ${account.id}: ${paymentMethodResult.error}`;
            result.errors.push(errorMsg);
            logger.warn('Failed to create payment method', {
              userId,
              bankAccountId: account.id,
              error: paymentMethodResult.error
            });
          }
        } catch (error) {
          const errorMsg = `Error processing account ${account.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          result.errors.push(errorMsg);
          logger.error('Error processing bank account for Stripe integration', {
            userId,
            bankAccountId: account.id,
            error: errorMsg
          });
        }
      }

      result.success = result.errors.length === 0;

      logger.info('Stripe integration completed for user', {
        userId,
        success: result.success,
        paymentMethodsCreated: result.paymentMethodsCreated,
        errors: result.errors.length
      });

      return result;

    } catch (error) {
      logger.error('Error ensuring Stripe integration for user', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        success: false,
        paymentMethodsCreated: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        details: {
          userId,
          bankAccountsProcessed: 0,
          paymentMethodIds: []
        }
      };
    }
  }

  /**
   * Ensure Stripe integration for a specific bank account
   */
  static async ensureStripeIntegrationForBankAccount(
    userId: number,
    bankAccountId: string
  ): Promise<{ success: boolean; paymentMethodId?: string; error?: string }> {
    try {
      logger.info('Ensuring Stripe integration for specific bank account', { userId, bankAccountId });

      // Get or create Stripe customer
      const stripeCustomerId = await StripeWalletService.getOrCreateStripeCustomer(userId);

      // Create payment method for the bank account
      const paymentMethodResult = await StripePaymentMethodService.getOrCreatePaymentMethod(
        userId,
        bankAccountId,
        stripeCustomerId
      );

      if (paymentMethodResult.success) {
        logger.info('Stripe integration ensured for bank account', {
          userId,
          bankAccountId,
          paymentMethodId: paymentMethodResult.paymentMethodId
        });
      }

      return paymentMethodResult;

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      logger.error('Error ensuring Stripe integration for bank account', {
        userId,
        bankAccountId,
        error: errorMsg
      });

      return {
        success: false,
        error: errorMsg
      };
    }
  }

  /**
   * Check if user has complete Stripe integration
   */
  static async checkStripeIntegrationStatus(userId: number): Promise<{
    hasStripeCustomer: boolean;
    stripeCustomerId?: string;
    bankAccountsCount: number;
    paymentMethodsCount: number;
    missingPaymentMethods: string[];
    isComplete: boolean;
  }> {
    try {
      // Check if user has Stripe customer
      const user = await executeQuerySingle(
        'SELECT stripe_customer_id FROM tbl_users WHERE id = ?',
        [userId]
      );

      const hasStripeCustomer = !!(user?.stripe_customer_id);
      const stripeCustomerId = user?.stripe_customer_id;

      // Get bank accounts count
      const bankAccountsResult = await executeQuerySingle(
        'SELECT COUNT(*) as count FROM tbl_bank_accounts WHERE user_id = ?',
        [userId]
      );
      const bankAccountsCount = bankAccountsResult?.count || 0;

      // Get payment methods count
      const paymentMethodsResult = await executeQuerySingle(
        'SELECT COUNT(*) as count FROM tbl_stripe_payment_methods WHERE user_id = ? AND is_active = 1',
        [userId]
      );
      const paymentMethodsCount = paymentMethodsResult?.count || 0;

      // Find bank accounts without payment methods
      const missingPaymentMethodsResult = await executeQuery(
        `SELECT ba.id FROM tbl_bank_accounts ba
         LEFT JOIN tbl_stripe_payment_methods spm ON ba.id = spm.bank_account_id AND spm.is_active = 1
         WHERE ba.user_id = ? AND spm.id IS NULL`,
        [userId]
      );

      const missingArray = Array.isArray(missingPaymentMethodsResult) 
        ? missingPaymentMethodsResult 
        : (missingPaymentMethodsResult ? [missingPaymentMethodsResult] : []);

      const missingPaymentMethods = missingArray.map(item => item.id);

      const isComplete = hasStripeCustomer && 
                        bankAccountsCount > 0 && 
                        paymentMethodsCount >= bankAccountsCount &&
                        missingPaymentMethods.length === 0;

      return {
        hasStripeCustomer,
        stripeCustomerId,
        bankAccountsCount,
        paymentMethodsCount,
        missingPaymentMethods,
        isComplete
      };

    } catch (error) {
      logger.error('Error checking Stripe integration status', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        hasStripeCustomer: false,
        bankAccountsCount: 0,
        paymentMethodsCount: 0,
        missingPaymentMethods: [],
        isComplete: false
      };
    }
  }

  /**
   * Migrate all users to have complete Stripe integration
   */
  static async migrateAllUsersToStripeIntegration(limit: number = 50): Promise<{
    success: boolean;
    usersProcessed: number;
    usersSuccessful: number;
    usersFailed: number;
    errors: string[];
  }> {
    try {
      logger.info('Starting bulk migration of users to Stripe integration', { limit });

      // Get users who have bank accounts but may not have complete Stripe integration
      const usersWithBankAccounts = await executeQuery(
        `SELECT DISTINCT u.id, u.email, u.full_name
         FROM tbl_users u
         INNER JOIN tbl_bank_accounts ba ON u.id = ba.user_id
         LIMIT ?`,
        [limit]
      );

      const usersArray = Array.isArray(usersWithBankAccounts) 
        ? usersWithBankAccounts 
        : (usersWithBankAccounts ? [usersWithBankAccounts] : []);

      let usersSuccessful = 0;
      let usersFailed = 0;
      const errors: string[] = [];

      for (const user of usersArray) {
        try {
          const result = await this.ensureStripeIntegrationForUser(user.id);
          
          if (result.success) {
            usersSuccessful++;
            logger.info('User migration successful', {
              userId: user.id,
              email: user.email,
              paymentMethodsCreated: result.paymentMethodsCreated
            });
          } else {
            usersFailed++;
            errors.push(`User ${user.id} (${user.email}): ${result.errors.join(', ')}`);
          }
        } catch (error) {
          usersFailed++;
          const errorMsg = `User ${user.id} (${user.email}): ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMsg);
        }
      }

      const success = usersFailed === 0;

      logger.info('Bulk migration completed', {
        usersProcessed: usersArray.length,
        usersSuccessful,
        usersFailed,
        success
      });

      return {
        success,
        usersProcessed: usersArray.length,
        usersSuccessful,
        usersFailed,
        errors
      };

    } catch (error) {
      logger.error('Error in bulk migration to Stripe integration', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        success: false,
        usersProcessed: 0,
        usersSuccessful: 0,
        usersFailed: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }
}
