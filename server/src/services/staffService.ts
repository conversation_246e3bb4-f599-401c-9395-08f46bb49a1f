// import { UserDetails } from './staffService';
import { comparePin } from '../models/user';
import { executeQuery, executeQuerySingle, executeSecondaryQuery, executeSecondaryQuerySingle, executeUpdate, recordExists } from '../utils/database';
import logger from '../utils/logger';
import { createWalletTransaction, getUserMasterWallet } from './walletService';

export interface StaffMember {
  id: number;
  added_by: number;
  user_id: number;
  name: string;
  email: string;
  contact: string;
  role: string;
  department: string;
  status: string;
  avatar: string;
  created_at: Date;
  updated_at: Date;
}

export interface UserDetails {
  id: number;
  name: string;
  contact: string;
  department?: string;
  position?: string;
  avatar?: number;
  created_at: Date;
  updated_at: Date;
    totalPaymentPaid: number;
  upcomingPayment: number;
  overduePayment: number;
  gameActivityCount: number;
  transactionsCount: number;
  transactions: {
    id: string;
    transactionId: string;
    date: string;
    paidByReceivedFrom: string;
    paidTo: string;
    type: string;
    amount: number;
    status: string;
  }[];
}

/**
 * Get current user's staff members from secondary database
 * @param userId - User ID (from JWT token)
 * @param parentUserId - Parent User ID (organizer ID from JWT token)
 * @returns Promise<StaffMember[]>
 */
// Interface for importing users
export interface ImportableUser {
  id: number;
  added_by:number;
  name: string;
  email: string;
  contact: string;
  department?: string;
  position?: string;
  avatar?: number;
  role?: 'Office Staff' | 'Referee' | 'Other Staff';
}

export interface ImportUsersResult {
  success: boolean;
  imported: number;
  failed: number;
  message: string;
  errors?: string[];
}

export async function getCurrentUserStaff(userId: number, parentUserId?: number): Promise<StaffMember[]> {
  try {
    // Use parentUserId if available, otherwise fall back to userId
    const organizerId = parentUserId || userId;

    const staff = await executeSecondaryQuery<StaffMember>(
      `SELECT DISTINCT 
    u.id,
    u.added_by,
    CONCAT(COALESCE(u.firstname, ''), ' ', COALESCE(u.lastname, '')) AS name,
    u.email,
    u.contact_number AS contact,
    CASE 
      WHEN u.profile_pic IS NOT NULL AND u.profile_pic != '' 
      THEN u.profile_pic 
      ELSE CONCAT('https://ui-avatars.com/api/?name=', REPLACE(CONCAT(COALESCE(u.firstname, ''), ' ', COALESCE(u.lastname, '')), ' ', '+'))
    END AS avatar,
    r.role_name AS department,
    u.created_at,
    u.updated_at
FROM 
    users u
LEFT JOIN 
    user_roles ur ON u.id = ur.user_id
LEFT JOIN 
    roles r ON ur.role_id = r.id
LEFT JOIN 
    organizer_members om ON u.id = om.member_id AND om.organizer_user_id = ?
WHERE 
    (u.added_by = ? OR om.organizer_user_id = ?)
    AND u.enabled = 1
    AND u.status_id NOT IN (3, 28)
    AND ur.role_id IN (5, 10);
`,
      [organizerId, organizerId, organizerId]
    );

    logger.info('Staff members retrieved for user', { userId, count: staff.length });
    return staff;
  } catch (error) {
    logger.error('Error fetching user staff', { userId, error });
    throw error;
  }
}

/**
 * Get staff member's transaction history with pagination and filtering
 * @param userId - User ID
 * @param options - Query options for pagination and filtering
 * @returns Promise<{transactions: any[], total: number, hasMore: boolean}>
 */
export async function getStaffTransactionHistory(
  userId: number,
  options: {
    limit?: number;
    offset?: number;
    search?: string;
    type?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
  } = {}
): Promise<{transactions: any[], total: number, hasMore: boolean}> {
  try {
    const {
      limit = 20,
      offset = 0,
      search = '',
      type = '',
      status = '',
      dateFrom = '',
      dateTo = ''
    } = options;

    logger.info('Fetching staff transaction history', {
      userId,
      userIdType: typeof userId,
      options
    });

    // Build WHERE conditions - user_id is bigint in database
    let whereConditions = ['wt.user_id = ?'];
    let queryParams: any[] = [userId];

    // Add search filter
    if (search) {
      whereConditions.push('(wt.description LIKE ? OR wt.reference_id LIKE ?)');
      queryParams.push(`%${search}%`, `%${search}%`);
    }

    // Add type filter (payment_provider)
    if (type && type !== 'all') {
      whereConditions.push('wt.payment_provider = ?');
      queryParams.push(type);
    }

    // Add status filter - status_id is varchar(50) in database
    if (status && status !== 'all') {
      const statusMap: { [key: string]: string } = {
        'paid': '1',
        'pending': '2',
        'failed': '3'
      };
      if (statusMap[status]) {
        whereConditions.push('wt.status_id = ?');
        queryParams.push(statusMap[status]);
      }
    }

    // Add date range filters
    if (dateFrom) {
      whereConditions.push('DATE(wt.created_at) >= ?');
      queryParams.push(dateFrom);
    }
    if (dateTo) {
      whereConditions.push('DATE(wt.created_at) <= ?');
      queryParams.push(dateTo);
    }

    const whereClause = whereConditions.join(' AND ');

    logger.info('Built WHERE clause for staff transactions', {
      userId,
      whereClause,
      queryParams,
      whereConditions
    });

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM tbl_wallet_transactions wt
      WHERE ${whereClause}
    `;

    logger.info('Executing count query', { countQuery, queryParams });
    const countResult = await executeQuerySingle<{total: number}>(countQuery, queryParams);
    const total = countResult?.total || 0;

    logger.info('Count query result', { total, countResult });

    // First, let's do a simple test query to see what's actually in the database
    const testQuery = `SELECT * FROM tbl_wallet_transactions WHERE user_id = ? LIMIT 5`;
    const testResult = await executeQuery<any>(testQuery, [userId]);
    logger.info('Test query result for user', { userId, testResult });

    // Get transactions with pagination - use string interpolation for LIMIT/OFFSET to avoid MySQL prepared statement issues
    const transactionsQuery = `
      SELECT
        wt.id,
        wt.user_id,
        wt.type,
        wt.amount,
        wt.reference_id,
        wt.payment_provider,
        wt.description,
        wt.status_id,
        wt.created_at,
        wt.meta_data
      FROM tbl_wallet_transactions wt
      WHERE ${whereClause}
      ORDER BY wt.created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    logger.info('Executing transactions query', { transactionsQuery, queryParams });

    const transactions = await executeQuery<any>(
      transactionsQuery,
      queryParams
    );

    logger.info('Raw transactions from database', {
      userId,
      transactionCount: transactions?.length,
      total,
      hasMore: (offset + limit) < total
    });

    if (!transactions || transactions.length === 0) {
      return { transactions: [], total, hasMore: false };
    }

    // Transform the raw data to match the expected format
    const formattedTransactions = transactions.map((wt: any) => {
      let paidByReceivedFrom = 'Unknown';
      let paidTo = 'Unknown';
      let transactionType = 'Transaction';

      // Parse meta_data if it exists
      let metaData = {};
      if (wt.meta_data) {
        try {
          metaData = typeof wt.meta_data === 'string' ? JSON.parse(wt.meta_data) : wt.meta_data;
        } catch (e) {
          logger.warn('Failed to parse meta_data', { userId, transactionId: wt.id, meta_data: wt.meta_data });
        }
      }

      // Handle different payment providers and transaction types
      logger.info('Processing transaction', {
        transactionId: wt.id,
        payment_provider: wt.payment_provider,
        type: wt.type,
        amount: wt.amount,
        hasMetaData: !!wt.meta_data
      });

      if (wt.payment_provider === 'wallet_transfer') {
        if (wt.type === 'credit' && wt.amount > 0) {
          // Incoming transfer
          paidByReceivedFrom = (metaData as any).senderName || `User ${(metaData as any).senderUserId || 'Unknown'}`;
          paidTo = 'My Wallet';
          transactionType = 'Wallet Transfer (Received)';
        } else if (wt.type === 'debit' && wt.amount < 0) {
          // Outgoing transfer
          paidByReceivedFrom = 'My Wallet';
          paidTo = (metaData as any).recipientName || `User ${(metaData as any).recipientUserId || 'Unknown'}`;
          transactionType = 'Wallet Transfer (Sent)';
        } else {
          logger.warn('Wallet transfer transaction with unexpected type/amount combination', {
            transactionId: wt.id,
            type: wt.type,
            amount: wt.amount,
            metaData
          });
        }
      } else {
        // Default case
        logger.info('Transaction using default case', {
          transactionId: wt.id,
          payment_provider: wt.payment_provider,
          description: wt.description
        });
        paidByReceivedFrom = wt.description || 'Transaction';
        paidTo = wt.amount > 0 ? 'My Wallet' : 'External';
        transactionType = wt.payment_provider || 'Transaction';
      }

      return {
        id: wt.id.toString(),
        transactionId: wt.reference_id || `TXN-${wt.id}`,
        date: wt.created_at ? new Date(wt.created_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        dateTime: wt.created_at ? new Date(wt.created_at).toISOString() : new Date().toISOString(),
        paidByReceivedFrom,
        paidTo,
        type: transactionType,
        amount: Math.abs(parseFloat(wt.amount) || 0),
        status: parseInt(wt.status_id) === 1 ? 'Paid' : parseInt(wt.status_id) === 2 ? 'Pending' : parseInt(wt.status_id) === 3 ? 'Failed' : 'Pending',
        provider: wt.payment_provider,
        description: wt.description,
        metadata: metaData
      };
    });

    const hasMore = (offset + limit) < total;

    logger.info('Formatted staff transactions', {
      userId,
      formattedCount: formattedTransactions.length,
      total,
      hasMore
    });

    return {
      transactions: formattedTransactions,
      total,
      hasMore
    };
  } catch (error) {
    logger.error('Error fetching staff transaction history', { userId, error });
    return { transactions: [], total: 0, hasMore: false };
  }
}

/**
 * Get user's transaction history from wallet transactions (simplified version for user details)
 * @param userId - User ID
 * @param limit - Number of transactions to fetch (default: 10)
 * @returns Promise<Transaction[]>
 */
async function getUserTransactionHistory(userId: number, limit: number = 10): Promise<any[]> {
  try {
    logger.info('Fetching transaction history for user', { userId, limit });

    // Query based on the actual database structure
    const transactions = await executeQuery<any>(
      `SELECT
        wt.id,
        wt.user_id,
        wt.type,
        wt.amount,
        wt.reference_id,
        wt.payment_provider,
        wt.description,
        wt.status_id,
        wt.created_at,
        wt.meta_data
       FROM tbl_wallet_transactions wt
       WHERE wt.user_id = ?
       ORDER BY wt.created_at DESC
       LIMIT ?`,
      [userId, limit]
    );

    logger.info('Raw transactions from database', { userId, transactionCount: transactions?.length, transactions });

    if (!transactions || transactions.length === 0) {
      logger.info('No transactions found for user', { userId });
      return [];
    }

    // Transform the raw data to match the expected format
    const formattedTransactions = transactions.map((wt: any) => {
      let paidByReceivedFrom = 'Unknown';
      let paidTo = 'Unknown';
      let transactionType = 'Transaction';

      // Parse meta_data if it exists
      let metaData = {};
      if (wt.meta_data) {
        try {
          metaData = typeof wt.meta_data === 'string' ? JSON.parse(wt.meta_data) : wt.meta_data;
        } catch (e) {
          logger.warn('Failed to parse meta_data', { userId, transactionId: wt.id, meta_data: wt.meta_data });
        }
      }

      // Handle different payment providers and transaction types
      if (wt.payment_provider === 'wallet_transfer') {
        if (wt.type === 'credit' && wt.amount > 0) {
          // Incoming transfer
          paidByReceivedFrom = (metaData as any).senderName || `User ${(metaData as any).senderUserId || 'Unknown'}`;
          paidTo = 'My Wallet';
          transactionType = 'Wallet Transfer (Received)';
        } else if (wt.type === 'debit' && wt.amount < 0) {
          // Outgoing transfer
          paidByReceivedFrom = 'My Wallet';
          paidTo = (metaData as any).recipientName || `User ${(metaData as any).recipientUserId || 'Unknown'}`;
          transactionType = 'Wallet Transfer (Sent)';
        }
      } else {
        // Default case
        paidByReceivedFrom = wt.description || 'Transaction';
        paidTo = wt.amount > 0 ? 'My Wallet' : 'External';
        transactionType = wt.payment_provider || 'Transaction';
      }

      return {
        id: wt.id.toString(),
        transactionId: wt.reference_id || `TXN-${wt.id}`,
        date: wt.created_at ? new Date(wt.created_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        paidByReceivedFrom,
        paidTo,
        type: transactionType,
        amount: Math.abs(parseFloat(wt.amount) || 0),
        status: wt.status_id === 1 ? 'Paid' : wt.status_id === 2 ? 'Pending' : wt.status_id === 3 ? 'Failed' : 'Pending'
      };
    });

    logger.info('Formatted transactions', { userId, formattedTransactions });
    return formattedTransactions;
  } catch (error) {
    logger.error('Error fetching user transaction history', { userId, error });
    return [];
  }
}

/**
 * Get user details from secondary database
 * @param userId - User ID
 * @returns Promise<UserDetails | null>
 */
export async function getUserDetails(userId: number): Promise<UserDetails | null> {
  try {
    logger.info('Fetching user details', { userId });

    const userDetails = await executeQuerySingle<UserDetails>(
      'SELECT tu.*, tr.role_name as role FROM tbl_users tu JOIN tbl_roles tr on tr.id = tu.role_id WHERE tu.id = ?',
      [userId]
    );

    logger.info('User details from database', { userId, userDetails: userDetails ? { id: userDetails.id, name: userDetails.name } : null });

    if (userDetails && userDetails.id != null) {
      // Fetch real transaction history from database
      const transactions = await getUserTransactionHistory(userId, 10);

      logger.info('Transactions fetched for user details', { userId, transactionCount: transactions.length });

      // Calculate transaction-based metrics
      const totalPaymentPaid = transactions
        .filter(t => t.status === 'Paid' && t.amount > 0)
        .reduce((sum, t) => sum + t.amount, 0);

      const upcomingPayment = transactions
        .filter(t => t.status === 'Pending')
        .reduce((sum, t) => sum + t.amount, 0);

      const overduePayment = transactions
        .filter(t => t.status === 'Failed')
        .reduce((sum, t) => sum + t.amount, 0);

      const userData: UserDetails = {
        ...userDetails,
        totalPaymentPaid,
        upcomingPayment,
        overduePayment,
        gameActivityCount: 10, // Keep static for now
        transactionsCount: transactions.length,
        transactions
      };

      logger.info('Final user data prepared', {
        userId,
        transactionsCount: userData.transactionsCount,
        totalPaymentPaid: userData.totalPaymentPaid
      });

      return userData;
    }

    return null; // ✅ Add this fallback
  } catch (error) {
    logger.error('Error fetching user details', { userId, error });
    throw error;
  }
}


/**
 * Get user's department information from secondary database
 * @param userId - User ID
 * @returns Promise<any>
 */
export async function getUserDepartment(userId: number): Promise<any> {
  try {
    const department = await executeSecondaryQuerySingle(
      `SELECT d.* FROM departments d 
       JOIN user_details ud ON d.id = ud.department_id 
       WHERE ud.user_id = ?`,
      [userId]
    );

    return department;
  } catch (error) {
    logger.error('Error fetching user department', { userId, error });
    throw error;
  }
}

/**
 * Get user's manager information from secondary database
 * @param userId - User ID
 * @returns Promise<any>
 */
export async function getUserManager(userId: number): Promise<any> {
  try {
    const manager = await executeSecondaryQuerySingle(
      `SELECT m.* FROM user_details m 
       JOIN user_details u ON m.user_id = u.manager_id 
       WHERE u.user_id = ?`,
      [userId]
    );

    return manager;
  } catch (error) {
    logger.error('Error fetching user manager', { userId, error });
    throw error;
  }
}

/**
 * Get user's team members from secondary database
 * @param userId - User ID
 * @returns Promise<UserDetails[]>
 */
export async function getUserTeamMembers(userId: number): Promise<UserDetails[]> {
  try {
    const teamMembers = await executeQuery<UserDetails>(
      `SELECT tu.id, tu.full_name name , tu.email, tu.phone contact, tr.role_name as role,  0 as amount, "" as due_date, "Pending" as status ,tu.profile_pic as avatar  FROM  tbl_users tu
JOIN tbl_roles tr on tr.id  = tu.role_id where master_parent_user_id = ?`,
      [userId]
    );
    logger.info('Team members retrieved for user', { userId, count: teamMembers ? teamMembers.length : 0 });
    return teamMembers ?? [];
  } catch (error) {
    logger.error('Error fetching user team members', { userId, error });
    throw error;
  }
}

/**
 * Get user's projects from secondary database
 * @param userId - User ID
 * @returns Promise<any[]>
 */
export async function getUserProjects(userId: number): Promise<any[]> {
  try {
    const projects = await executeSecondaryQuery(
      `SELECT p.* FROM projects p 
       JOIN project_members pm ON p.id = pm.project_id 
       WHERE pm.user_id = ? AND p.status = 'active'`,
      [userId]
    );

    logger.info('Projects retrieved for user', { userId, count: projects.length });
    return projects;
  } catch (error) {
    logger.error('Error fetching user projects', { userId, error });
    throw error;
  }
}

/**
 * Get user's permissions from secondary database
 * @param userId - User ID
 * @returns Promise<any[]>
 */
export async function getUserPermissions(userId: number): Promise<any[]> {
  try {
    const permissions = await executeSecondaryQuery(
      `SELECT p.* FROM permissions p 
       JOIN user_permissions up ON p.id = up.permission_id 
       WHERE up.user_id = ? AND up.status = 'active'`,
      [userId]
    );

    logger.info('Permissions retrieved for user', { userId, count: permissions.length });
    return permissions;
  } catch (error) {
    logger.error('Error fetching user permissions', { userId, error });
    throw error;
  }
}

/**
 * Get comprehensive user information from secondary database
 * @param userId - User ID
 * @param parentUserId - Parent User ID (organizer ID)
 * @returns Promise<any>
 */
export async function getComprehensiveUserInfo(userId: number, parentUserId?: number): Promise<any> {
  try {
    const [
      userDetails,
      staff,
      department,
      manager,
      teamMembers,
      projects,
      permissions
    ] = await Promise.all([
      getUserDetails(userId),
      getCurrentUserStaff(userId, parentUserId),
      getUserDepartment(userId),
      getUserManager(userId),
      getUserTeamMembers(userId),
      getUserProjects(userId),
      getUserPermissions(userId)
    ]);

    const comprehensiveInfo = {
      userDetails,
      staff,
      department,
      manager,
      teamMembers,
      projects,
      permissions
    };

    logger.info('Comprehensive user info retrieved', { userId });
    return comprehensiveInfo;
  } catch (error) {
    logger.error('Error fetching comprehensive user info', { userId, error });
    throw error;
  }
}

/**
 * Import multiple users as staff members
 * @param users - Array of users to import
 * @param organizerId - Organizer ID (parent_user_id)
 * @param defaultRole - Default role for imported users
 * @returns Promise<ImportUsersResult>
 */
export async function importStaffUsers(
  users: ImportableUser[],
  organizerId: number,
  defaultRole: 'Staff' | 'Referee'
): Promise<ImportUsersResult> {
  try {
    let imported = 0;
    let failed = 0;
    const errors: string[] = [];

    // Role mapping
    const roleMapping = {
      'Staff': 10,
      'Referee': 5,
    };

    for (const user of users) {
      try {
        // Check if user already exists in organizer_members
        const existingMember = await recordExists(
          'SELECT id FROM tbl_users WHERE email = ? AND master_parent_user_id = ?',
          [user.email, organizerId]
        );

       if (existingMember) {
           console.log(existingMember, 'existingMemberexistingMember');
           errors.push(`User ${user.name} (${user.email}) is already a staff member`);
           failed++;
           continue;
         }

        const role =user.department === 'Referee' ? 'Referee' : 'Staff'
        // Insert into organizer_members table
   console.log(user.added_by,'user.added_by')
     const UserDetail=   await executeUpdate(
          `INSERT INTO tbl_users (team_connect_user_id,master_parent_user_id,role_id, email,full_name,phone,profile_pic)
           VALUES (?, ?, ?, ?, ?, ?,?)`,
          [user.id, user.added_by,roleMapping[role],user.email,user.name, user.contact ,user.avatar]
        );

         await executeUpdate(
          'INSERT INTO tbl_user_parents (user_id, parent_user_id, role_id) VALUES (?, ?, ?)',
          [UserDetail.insertId, user.added_by, roleMapping[role]] 
        );
           // Create wallet for new user
      const walletUniqueId = `wallet_${user.id}_${Date.now()}`;
      const walletName = user.name || `Wallet ${user.id}`;
      const walletUsername = user.email.split('@')[0];

      await executeUpdate(
        `INSERT INTO tbl_masterwallet
         (wallet_unique_id, user_id, name, username, balance, status_id, created_at, last_updated)
         VALUES (?, ?, ?, ?, 0.00, 1, NOW(), NOW())`,
        [walletUniqueId, UserDetail.insertId, walletName, walletUsername]
      );
        imported++;
        logger.info('User imported as staff member', {
          userId: user.id,
          userName: user.name,
          organizerId,
          role: roleMapping[role]
        });

      } catch (userError) {
        const errorMessage = `Failed to import ${user.name}: ${userError instanceof Error ? userError.message : 'Unknown error'}`;
        errors.push(errorMessage);
        failed++;
        logger.error('Error importing individual user', {
          userId: user.id,
          userName: user.name,
          error: userError
        });
      }
    }

    const result: ImportUsersResult = {
      success: imported > 0,
      imported,
      failed,
      message: `Import completed: ${imported} successful, ${failed} failed`,
      errors: errors.length > 0 ? errors : undefined
    };

    logger.info('Staff import completed', {
      organizerId,
      totalUsers: users.length,
      imported,
      failed
    });

    return result;

  } catch (error) {
    logger.error('Error importing staff users', { organizerId, error });
    return {
      success: false,
      imported: 0,
      failed: users.length,
      message: 'Import failed due to system error',
      errors: [error instanceof Error ? error.message : 'Unknown error']
    };
  }
}





// Wallet-to-wallet transfer function (bank account functionality removed)
export async function transferAmountToStaff(
  staffId: string,
  amount: number,
  description: string,
  organizerId: number,
  paymentSource: 'wallet' = 'wallet',
  pin?: string,
  userId?: number
): Promise<{ success: boolean; message: string; transactionId?: number }> {
  try {
    console.log('[START] transferAmountToStaff (wallet-only)', { staffId, amount, organizerId });

    // Get staff details
    const staffDetails = await executeQuery(
      `SELECT u.full_name as staff_name, u.email as staff_email
       FROM tbl_users u
       WHERE u.id = ?`,
      [staffId]
    );

    if (!staffDetails.length) {
      console.log('[ERROR] Staff member not found.');
      return {
        success: false,
        message: 'Staff member not found.'
      };
    }

    const staff = staffDetails[0];
    let transactionId: number;

    if (!userId) {
      console.log('[ERROR] User ID not provided');
      return { success: false, message: 'User ID not provided' };
    }

    if (paymentSource === 'wallet') {
      console.log('[INFO] Payment Source: Wallet');

      // Verify organizer's wallet and PIN
      const wallet = await getUserMasterWallet(userId);
      console.log('[INFO] Fetched organizer wallet:', wallet);

      if (!wallet) {
        console.log('[ERROR] Organizer wallet not found');
        return { success: false, message: 'Organizer wallet not found' };
      }

      if (!wallet.wallet_master_pin) {
        console.log('[ERROR] Wallet PIN not set');
        return { success: false, message: 'Wallet PIN not set for organizer' };
      }

      const isPinValid = await comparePin(pin!, wallet.wallet_master_pin);
      console.log('[INFO] PIN validation result:', isPinValid);

      if (!isPinValid) {
        console.log('[ERROR] Invalid PIN');
        return { success: false, message: 'Invalid wallet PIN' };
      }

      const currentBalance = parseFloat(wallet.balance.toString());
      console.log('[INFO] Current Wallet Balance:', currentBalance);

      if (currentBalance < amount) {
        console.log('[ERROR] Insufficient wallet balance');
        return { 
          success: false, 
          message: `Insufficient wallet balance. Available: $${currentBalance.toFixed(2)}, Required: $${amount.toFixed(2)}` 
        };
      }
       // Create wallet transaction for organizer (debit)
      transactionId = await createWalletTransaction(
        organizerId,
        -amount,
        `Wallet transfer to ${staff.staff_name} - ${description}`,
        'debit',
        `STAFF_WALLET_${staffId}_${Date.now()}`,
        {
          staffId,
          staffName: staff.staff_name,
          staffEmail: staff.staff_email,
          organizerId,
          transferType: 'wallet_to_wallet',
          paymentSource: 'wallet',
          description,
          isInternalTransfer: true,
          recipientName: staff.staff_name,
          recipientUserId: staffId
        },
        1, // status_id = 1 (completed)
        null, // plaidTransferId
        null, // plaidEventId
        'wallet_transfer' // paymentProvider
      ) || 0;

      // Create credit transaction for staff
       const staffTransactionId = await createWalletTransaction(
         parseInt(staffId),
         amount,
         `Received wallet transfer from Organizer - ${description}`,
         'credit',
         `STAFF_CREDIT_${organizerId}_${Date.now()}`,
         {
           organizerId,
           organizerWallet: true,
           staffId,
           staffName: staff.staff_name,
           staffEmail: staff.staff_email,
           transferType: 'wallet_to_wallet',
           paymentSource: 'wallet',
           description,
           isInternalTransfer: true,
           senderName: 'Organizer',
           senderUserId: organizerId
         },
         1, // status_id = 1 (completed)
         null, // plaidTransferId
         null, // plaidEventId
         'wallet_transfer' // paymentProvider
       ) || 0;
       console.log('[INFO] Credit transaction created for staff.');
       

      console.log('[INFO] Wallet transaction created with ID:', transactionId);

      if (!transactionId) {
        console.log('[ERROR] Failed to create wallet transaction');
        return { success: false, message: 'Failed to create transaction record' };
      }

      // Use the proper wallet service to update balances
      const { updateWalletBalance } = await import('./walletService');

      const newBalance = currentBalance - amount;
      console.log('[DEBUG] About to update organizer balance:', {
        organizerId,
        currentBalance,
        amount,
        newBalance
      });

      const organizerBalanceUpdated = await updateWalletBalance(organizerId, newBalance);

      if (!organizerBalanceUpdated) {
        console.log('[ERROR] Failed to update organizer wallet balance');
        return { success: false, message: 'Failed to update organizer wallet balance' };
      }
      console.log('[INFO] Organizer wallet balance updated successfully:', newBalance);

      // Update staff's wallet balance
      const staffWallet = await executeQuery(
        'SELECT balance FROM tbl_masterwallet WHERE user_id = ?',
        [staffId]
      );

      const staffCurrentBalance = parseFloat(staffWallet[0]?.balance) || 0;
      const staffNewBalance = staffCurrentBalance + amount;

      console.log('[DEBUG] About to update staff balance:', {
        staffId,
        staffCurrentBalance,
        amount,
        staffNewBalance
      });

      const staffBalanceUpdated = await updateWalletBalance(parseInt(staffId), staffNewBalance);

      if (!staffBalanceUpdated) {
        console.log('[ERROR] Failed to update staff wallet balance');
        // Rollback organizer balance
        await updateWalletBalance(organizerId, currentBalance);
        return { success: false, message: 'Failed to update staff wallet balance' };
      }
      console.log('[INFO] Staff wallet balance updated successfully:', staffNewBalance);

      // Create ledger entries for audit trail
      const { createLedgerEntry } = await import('./walletService');

      // Create ledger entry for organizer (debit)
      if (transactionId) {
        await createLedgerEntry(transactionId, 'debit', currentBalance, newBalance);
      }

      // Create ledger entry for staff (credit)
      if (staffTransactionId) {
        await createLedgerEntry(staffTransactionId, 'credit', staffCurrentBalance, staffNewBalance);
      }

    }

    logger.info('Staff wallet transfer completed', {
      organizerId,
      staffId,
      staffName: staff.staff_name,
      amount,
      transactionId
    });

    console.log('[SUCCESS] Transfer completed successfully');

    return {
      success: true,
      message: `Successfully transferred $${amount.toFixed(2)} to ${staff.staff_name}'s wallet`,
      transactionId
    };

  } catch (error) {
    console.log('[FATAL ERROR] Transfer failed:', error);
    logger.error('Error in staff wallet transfer', { staffId, amount, organizerId, error });
    return { 
      success: false, 
      message: 'Transfer failed due to system error. Please try again.' 
    };
  }
}






