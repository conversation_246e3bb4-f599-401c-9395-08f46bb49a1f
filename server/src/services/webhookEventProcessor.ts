import logger from '../utils/logger';
import { executeUpdate } from '../utils/database';
import { WebhookEventData } from './webhookEventQueue';

/**
 * Interface for webhook processing result
 */
export interface WebhookProcessingResult {
  success: boolean;
  action: string;
  metadata?: Record<string, any>;
  error?: string;
}

/**
 * Handle transfer-specific webhook events with enhanced correlation and tracking
 * This is extracted from the controller to be used by the queue processor
 */
export async function handleTransferWebhook(webhookEvent: WebhookEventData): Promise<WebhookProcessingResult> {
  const startTime = Date.now();
  const { webhook_code, transfer_id, transfer_status, failure_reason, return_reason, event_type } = webhookEvent;
  const eventId = webhookEvent.event_id;
  const correlationId = webhookEvent.correlation_id || `proc_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  
  if (!transfer_id) {
    logger.warn('Transfer webhook received without transfer_id', { 
      eventId,
      correlationId,
      webhookEvent 
    });
    return {
      success: false,
      action: 'ignored',
      error: 'No transfer_id provided',
      metadata: {
        eventId,
        correlationId,
        processingTime: Date.now() - startTime
      }
    };
  }

  // Enhanced logging with correlation tracking
  logger.info('Processing transfer webhook with correlation tracking', {
    eventId,
    correlationId,
    webhookCode: webhook_code,
    eventType: event_type,
    transferId: transfer_id,
    transferStatus: transfer_status,
    failureReason: failure_reason,
    returnReason: return_reason,
    verified: webhookEvent.verified || false,
    receivedAt: webhookEvent.received_at,
    enqueuedAt: webhookEvent.enqueued_at
  });

  try {
    let action = 'processed';
    let metadata: Record<string, any> = {
      eventId,
      correlationId,
      transferId: transfer_id,
      webhookCode: webhook_code,
      eventType: event_type,
      verified: webhookEvent.verified || false,
      processingStartTime: new Date().toISOString()
    };

    // Track event correlation before processing
    await trackWebhookEventCorrelation(webhookEvent, correlationId, 'processing_started');

    // Handle different webhook event types with enhanced tracking
    switch (webhook_code) {
      case 'TRANSFER_EVENTS_UPDATE':
        // Transfer status has changed - use the status from the webhook
        await handleTransferStatusUpdate(transfer_id, transfer_status, failure_reason, correlationId, webhookEvent);
        action = 'status_updated';
        metadata = { 
          ...metadata, 
          status: transfer_status, 
          reason: failure_reason?.description,
          statusChangeType: 'events_update'
        };
        break;
        
      case 'TRANSFER_POSTED':
        // Transfer has been posted (completed)
        await handleTransferPosted(transfer_id, correlationId, webhookEvent);
        action = 'transfer_posted';
        metadata = { ...metadata, statusChangeType: 'posted' };
        break;
        
      case 'TRANSFER_SETTLED':
        // Transfer has been settled (final completion)
        await handleTransferSettled(transfer_id, correlationId, webhookEvent);
        action = 'transfer_settled';
        metadata = { ...metadata, statusChangeType: 'settled' };
        break;
        
      case 'TRANSFER_FAILED':
        // Transfer has failed
        await handleTransferFailed(transfer_id, failure_reason, correlationId, webhookEvent);
        action = 'transfer_failed';
        metadata = { 
          ...metadata, 
          reason: failure_reason?.description,
          statusChangeType: 'failed'
        };
        break;
        
      case 'TRANSFER_CANCELLED':
        // Transfer was cancelled
        await handleTransferCancelled(transfer_id, correlationId, webhookEvent);
        action = 'transfer_cancelled';
        metadata = { ...metadata, statusChangeType: 'cancelled' };
        break;
        
      case 'TRANSFER_RETURNED':
        // Transfer was returned
        await handleTransferReturned(transfer_id, return_reason, correlationId, webhookEvent);
        action = 'transfer_returned';
        metadata = { 
          ...metadata, 
          reason: return_reason?.description,
          statusChangeType: 'returned'
        };
        break;
        
      case 'TRANSFER_PENDING_APPROVAL':
        // Transfer is pending approval
        await handleTransferPendingApproval(transfer_id, correlationId, webhookEvent);
        action = 'transfer_pending_approval';
        metadata = { ...metadata, statusChangeType: 'pending_approval' };
        break;
        
      case 'TRANSFER_APPROVED':
        // Transfer has been approved
        await handleTransferApproved(transfer_id, correlationId, webhookEvent);
        action = 'transfer_approved';
        metadata = { ...metadata, statusChangeType: 'approved' };
        break;
        
      case 'TRANSFER_REJECTED':
        // Transfer has been rejected
        await handleTransferRejected(transfer_id, failure_reason, correlationId, webhookEvent);
        action = 'transfer_rejected';
        metadata = { 
          ...metadata, 
          reason: failure_reason?.description,
          statusChangeType: 'rejected'
        };
        break;
        
      // Handle Dashboard-specific events
      case 'TRANSFER_UPDATED':
        // Generic transfer update - check the event_type
        await handleTransferUpdated(transfer_id, webhookEvent, correlationId);
        action = 'transfer_updated';
        metadata = { 
          ...metadata, 
          eventType: event_type,
          statusChangeType: 'generic_update'
        };
        break;
        
      default:
        // Handle unknown webhook codes by checking event_type
        if (event_type) {
          await handleTransferByEventType(transfer_id, event_type, webhookEvent, correlationId);
          action = 'handled_by_event_type';
          metadata = { 
            ...metadata, 
            eventType: event_type,
            statusChangeType: 'event_type_based'
          };
        } else {
          logger.info('Unhandled transfer webhook code', {
            eventId,
            correlationId,
            webhookCode: webhook_code,
            transferId: transfer_id,
            transferStatus: transfer_status,
            eventType: event_type
          });
          action = 'unhandled';
          metadata = { 
            ...metadata, 
            webhookCode: webhook_code,
            statusChangeType: 'unhandled'
          };
        }
    }

    const processingTime = Date.now() - startTime;
    metadata.processingTime = processingTime;
    metadata.processingEndTime = new Date().toISOString();

    // Track successful completion
    await trackWebhookEventCorrelation(webhookEvent, correlationId, 'processing_completed', {
      action,
      processingTime,
      success: true
    });

    logger.info('Transfer webhook processed successfully with correlation tracking', {
      eventId,
      correlationId,
      transferId: transfer_id,
      action,
      processingTime,
      webhookCode: webhook_code
    });

    return {
      success: true,
      action,
      metadata
    };

  } catch (error) {
    const processingTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    logger.error('Error handling transfer webhook with correlation tracking', {
      eventId,
      correlationId,
      webhookCode: webhook_code,
      transferId: transfer_id,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
      processingTime
    });

    // Track error
    await trackWebhookEventCorrelation(webhookEvent, correlationId, 'processing_failed', {
      error: errorMessage,
      processingTime,
      success: false
    });

    return {
      success: false,
      action: 'error',
      error: errorMessage,
      metadata: {
        eventId,
        correlationId,
        transferId: transfer_id,
        webhookCode: webhook_code,
        processingTime,
        errorDetails: error instanceof Error ? error.stack : undefined
      }
    };
  }
}
/**
 * H
andle transfer updates by event type (for Dashboard events)
 */
async function handleTransferByEventType(transferId: string, eventType: string, webhookEvent: WebhookEventData, correlationId: string) {
  logger.info('Handling transfer by event type', { transferId, eventType, correlationId });
  
  switch (eventType) {
    case 'PENDING':
      // Transfer was created and is pending
      await handleTransferStatusUpdate(transferId, 'pending', undefined, correlationId, webhookEvent);
      break;
      
    case 'POSTED':
      // Transfer was submitted to payment network
      await handleTransferPosted(transferId, correlationId, webhookEvent);
      break;
      
    case 'SETTLED':
      // Transfer was settled
      await handleTransferSettled(transferId, correlationId, webhookEvent);
      break;
      
    case 'FUNDS_AVAILABLE':
      // Funds are available in ledger
      await handleTransferFundsAvailable(transferId, correlationId, webhookEvent);
      break;
      
    case 'FAILED':
      // Transfer failed
      await handleTransferFailed(transferId, webhookEvent.failure_reason, correlationId, webhookEvent);
      break;
      
    case 'CANCELLED':
      // Transfer was cancelled
      await handleTransferCancelled(transferId, correlationId, webhookEvent);
      break;
      
    case 'RETURNED':
      // Transfer was returned
      await handleTransferReturned(transferId, webhookEvent.return_reason, correlationId, webhookEvent);
      break;
      
    default:
      logger.info('Unknown event type', { transferId, eventType, correlationId });
      // Try to process as a status update
      await handleTransferStatusUpdate(transferId, eventType.toLowerCase(), undefined, correlationId, webhookEvent);
  }
}

/**
 * Handle generic transfer update
 */
async function handleTransferUpdated(transferId: string, webhookEvent: WebhookEventData, correlationId: string) {
  const { event_type, transfer_status } = webhookEvent;
  
  logger.info('Handling transfer update', { transferId, eventType: event_type, transferStatus: transfer_status, correlationId });
  
  // Use event_type if available, otherwise use transfer_status
  const status = event_type || transfer_status;
  
  if (status) {
    await handleTransferStatusUpdate(transferId, status, webhookEvent.failure_reason, correlationId, webhookEvent);
  } else {
    logger.warn('No status found in transfer update', { transferId, webhookEvent, correlationId });
  }
}

/**
 * Handle funds available event
 */
async function handleTransferFundsAvailable(transferId: string, correlationId: string, webhookEvent?: WebhookEventData) {
  try {
    logger.info('Transfer funds available', { transferId, correlationId });
    
    // Update transfer status to funds_available (direct database update)
    await executeUpdate(
      'UPDATE tbl_wallet_transactions SET status_id = 1 WHERE reference_id = ?',
      [transferId]
    );
    
    // Process as a successful transfer
    await handleTransferSuccess(transferId, 'funds_available', correlationId, webhookEvent);
    
  } catch (error) {
    logger.error('Error handling transfer funds available', {
      transferId,
      correlationId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Handle general transfer status updates
 * Now uses the status directly from the webhook instead of making an API call
 */
async function handleTransferStatusUpdate(transferId: string, transferStatus: string, failureReason?: any, correlationId?: string, webhookEvent?: WebhookEventData) {
  try {
    logger.info('Handling transfer status update', { transferId, transferStatus, failureReason, correlationId });
    
    // Map Plaid status to our database status
    let mappedStatus = transferStatus;
    let shouldProcess = false;
    
    switch (transferStatus?.toUpperCase()) {
      case 'PENDING':
        mappedStatus = 'pending';
        break;
      case 'POSTED':
        mappedStatus = 'posted';
        shouldProcess = true;
        break;
      case 'SETTLED':
        mappedStatus = 'settled';
        shouldProcess = true;
        break;
      case 'FUNDS_AVAILABLE':
        mappedStatus = 'funds_available';
        shouldProcess = true;
        break;
      case 'FAILED':
        mappedStatus = 'failed';
        break;
      case 'CANCELLED':
        mappedStatus = 'cancelled';
        break;
      case 'RETURNED':
        mappedStatus = 'returned';
        break;
      default:
        mappedStatus = transferStatus?.toLowerCase() || 'unknown';
        logger.warn('Unknown transfer status', { transferId, originalStatus: transferStatus, mappedStatus, correlationId });
    }
    
    // Update the database status directly from webhook (direct database update)
    const statusId = mappedStatus === 'posted' || mappedStatus === 'settled' || mappedStatus === 'funds_available' ? 1 : 
                     mappedStatus === 'pending' ? 2 : 3; // 1=completed, 2=pending, 3=failed
    await executeUpdate(
      'UPDATE tbl_wallet_transactions SET status_id = ? WHERE reference_id = ?',
      [statusId, transferId]
    );
    
    // Process pending holds based on status
    if (shouldProcess) {
      await handleTransferSuccess(transferId, mappedStatus, correlationId, webhookEvent);
    } else if (mappedStatus === 'failed' || mappedStatus === 'cancelled' || mappedStatus === 'returned') {
      await handleTransferFailure(transferId, failureReason?.description || 'Transfer failed', correlationId, webhookEvent);
    } else if (mappedStatus === 'pending') {
      // Just update status, don't process yet
      logger.info('Transfer is pending', { transferId, status: mappedStatus, correlationId });
    }
    
    logger.info('Transfer status updated via webhook', {
      transferId,
      originalStatus: transferStatus,
      mappedStatus,
      failureReason: failureReason?.description,
      correlationId
    });
  } catch (error) {
    logger.error('Error handling transfer status update', {
      transferId,
      transferStatus,
      correlationId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}

/**
 * Handle successful transfer completion with enhanced webhook context
 */
async function handleTransferSuccess(transferId: string, status: string, correlationId?: string, webhookEvent?: WebhookEventData) {
  try {
    logger.info('Processing successful transfer with webhook context', { transferId, status, correlationId });
    
    // Create webhook context for enhanced integration
    const webhookContext = webhookEvent ? {
      eventId: webhookEvent.event_id,
      correlationId: correlationId,
      webhookCode: webhookEvent.webhook_code,
      timestamp: webhookEvent.timestamp,
      verified: webhookEvent.verified,
      processingStartTime: Date.now()
    } : undefined;

    // Use the enhanced webhook-aware wallet balance manager
    const { handleTransferStatusChangeWithWebhook, triggerRealTimeBalanceNotification } = await import('./walletBalanceManager');
    const result = await handleTransferStatusChangeWithWebhook(transferId, status, undefined, webhookContext);

    if (result.success) {
      logger.info('Transfer success handled via enhanced wallet manager', {
        transferId,
        status,
        correlationId,
        operation: result.operation,
        userId: result.userId,
        amount: result.amount,
        newBalance: result.newBalance,
        processingTime: result.processingTime,
        realTimeUpdate: result.realTimeUpdate,
        webhookEventId: result.webhookEventId
      });

      // Trigger real-time balance notification if user ID is available
      if (result.userId) {
        await triggerRealTimeBalanceNotification(result.userId, result, webhookContext);
      }
    } else {
      logger.warn('Transfer completed but enhanced wallet manager failed', {
        transferId,
        status,
        correlationId,
        message: result.message,
        webhookEventId: result.webhookEventId
      });
      throw new Error(result.message || 'Enhanced wallet manager failed');
    }

  } catch (error) {
    logger.error('Error handling transfer success with webhook context', {
      transferId,
      status,
      correlationId,
      webhookEventId: webhookEvent?.event_id,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}

/**
 * Handle transfer failure with enhanced webhook context
 */
async function handleTransferFailure(transferId: string, failureReason: string, correlationId?: string, webhookEvent?: WebhookEventData) {
  try {
    logger.info('Processing failed transfer with webhook context', { transferId, failureReason, correlationId });
    
    // Create webhook context for enhanced integration
    const webhookContext = webhookEvent ? {
      eventId: webhookEvent.event_id,
      correlationId: correlationId,
      webhookCode: webhookEvent.webhook_code,
      timestamp: webhookEvent.timestamp,
      verified: webhookEvent.verified,
      processingStartTime: Date.now()
    } : undefined;

    // Use the enhanced webhook-aware wallet balance manager
    const { handleTransferStatusChangeWithWebhook, triggerRealTimeBalanceNotification } = await import('./walletBalanceManager');
    const result = await handleTransferStatusChangeWithWebhook(transferId, 'failed', failureReason, webhookContext);

    if (result.success) {
      logger.info('Transfer failure handled via enhanced wallet manager', {
        transferId,
        failureReason,
        correlationId,
        operation: result.operation,
        userId: result.userId,
        amount: result.amount,
        newBalance: result.newBalance,
        processingTime: result.processingTime,
        realTimeUpdate: result.realTimeUpdate,
        webhookEventId: result.webhookEventId
      });

      // Trigger real-time balance notification if user ID is available
      if (result.userId) {
        await triggerRealTimeBalanceNotification(result.userId, result, webhookContext);
      }
    } else {
      logger.warn('Transfer failed but enhanced wallet manager failed', {
        transferId,
        failureReason,
        correlationId,
        message: result.message,
        webhookEventId: result.webhookEventId
      });
      throw new Error(result.message || 'Enhanced wallet manager failed');
    }

  } catch (error) {
    logger.error('Error handling transfer failure with webhook context', {
      transferId,
      failureReason,
      correlationId,
      webhookEventId: webhookEvent?.event_id,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}

/**
 * Handle transfer posted event
 */
async function handleTransferPosted(transferId: string, correlationId?: string, webhookEvent?: WebhookEventData) {
  await handleTransferSuccess(transferId, 'posted', correlationId, webhookEvent);
}

/**
 * Handle transfer settled event
 */
async function handleTransferSettled(transferId: string, correlationId?: string, webhookEvent?: WebhookEventData) {
  await handleTransferSuccess(transferId, 'settled', correlationId, webhookEvent);
}

/**
 * Handle transfer failed event
 */
async function handleTransferFailed(transferId: string, failureReason: any, correlationId?: string, webhookEvent?: WebhookEventData) {
  const reason = failureReason?.description || 'Transfer failed';
  await handleTransferFailure(transferId, reason, correlationId, webhookEvent);
}

/**
 * Handle transfer cancelled event
 */
async function handleTransferCancelled(transferId: string, correlationId?: string, webhookEvent?: WebhookEventData) {
  await handleTransferFailure(transferId, 'Transfer cancelled', correlationId, webhookEvent);
}

/**
 * Handle transfer returned event
 */
async function handleTransferReturned(transferId: string, returnReason: any, correlationId?: string, webhookEvent?: WebhookEventData) {
  const reason = returnReason?.description || 'Transfer returned';
  await handleTransferFailure(transferId, `Transfer returned: ${reason}`, correlationId, webhookEvent);
}

/**
 * Handle transfer pending approval event
 */
async function handleTransferPendingApproval(transferId: string, correlationId?: string, webhookEvent?: WebhookEventData) {
  try {
    logger.info('Transfer pending approval with webhook context', { transferId, correlationId });
    
    // Create webhook context for enhanced integration
    const webhookContext = webhookEvent ? {
      eventId: webhookEvent.event_id,
      correlationId: correlationId,
      webhookCode: webhookEvent.webhook_code,
      timestamp: webhookEvent.timestamp,
      verified: webhookEvent.verified,
      processingStartTime: Date.now()
    } : undefined;

    // Use the enhanced webhook-aware wallet balance manager for pending status
    const { handleTransferStatusChangeWithWebhook } = await import('./walletBalanceManager');
    await handleTransferStatusChangeWithWebhook(transferId, 'pending_approval', undefined, webhookContext);
    
  } catch (error) {
    logger.error('Error handling transfer pending approval with webhook context', {
      transferId,
      correlationId,
      webhookEventId: webhookEvent?.event_id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Handle transfer approved event
 */
async function handleTransferApproved(transferId: string, correlationId?: string, webhookEvent?: WebhookEventData) {
  try {
    logger.info('Transfer approved with webhook context', { transferId, correlationId });
    
    // Create webhook context for enhanced integration
    const webhookContext = webhookEvent ? {
      eventId: webhookEvent.event_id,
      correlationId: correlationId,
      webhookCode: webhookEvent.webhook_code,
      timestamp: webhookEvent.timestamp,
      verified: webhookEvent.verified,
      processingStartTime: Date.now()
    } : undefined;

    // Use the enhanced webhook-aware wallet balance manager for approved status
    const { handleTransferStatusChangeWithWebhook } = await import('./walletBalanceManager');
    await handleTransferStatusChangeWithWebhook(transferId, 'approved', undefined, webhookContext);
    
  } catch (error) {
    logger.error('Error handling transfer approved with webhook context', {
      transferId,
      correlationId,
      webhookEventId: webhookEvent?.event_id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Handle transfer rejected event
 */
async function handleTransferRejected(transferId: string, failureReason: any, correlationId?: string, webhookEvent?: WebhookEventData) {
  const reason = failureReason?.description || 'Transfer rejected';
  await handleTransferFailure(transferId, `Transfer rejected: ${reason}`, correlationId, webhookEvent);
}

// ============================================================================
// CORRELATION TRACKING FUNCTIONS
// ============================================================================

/**
 * Track webhook event correlation for monitoring and debugging
 * Enhanced with comprehensive correlation tracking throughout processing pipeline
 */
async function trackWebhookEventCorrelation(
  webhookEvent: WebhookEventData,
  correlationId: string,
  stage: string,
  additionalData?: Record<string, any>
): Promise<void> {
  try {
    const correlationData = {
      correlation_id: correlationId,
      event_id: webhookEvent.event_id,
      stage,
      webhook_type: webhookEvent.webhook_type,
      webhook_code: webhookEvent.webhook_code,
      transfer_id: webhookEvent.transfer_id,
      item_id: webhookEvent.item_id,
      timestamp: webhookEvent.timestamp,
      received_at: webhookEvent.received_at,
      enqueued_at: webhookEvent.enqueued_at,
      stage_timestamp: new Date().toISOString(),
      verified: webhookEvent.verified || false,
      additional_data: additionalData || {},
      tracking_metadata: {
        environment: process.env.NODE_ENV,
        processing_stage: stage,
        correlation_chain: correlationId,
        processor_version: '3.0_enhanced',
        stage_sequence: await getStageSequenceNumber(webhookEvent.event_id, stage),
        processing_metadata: {
          verification_method: webhookEvent.verification_method,
          priority: webhookEvent.processing_metadata?.priority,
          controller_version: webhookEvent.processing_metadata?.controller_version,
          deduplication_checked: webhookEvent.processing_metadata?.deduplication_checked,
          request_metadata: webhookEvent.processing_metadata?.request_metadata
        }
      }
    };
    
    // Enhanced correlation tracking with stage progression
    await executeUpdate(`
      UPDATE tbl_webhook_events_enhanced 
      SET processed_result = JSON_SET(
        COALESCE(processed_result, '{}'),
        '$.correlation_tracking',
        JSON_ARRAY_APPEND(
          COALESCE(JSON_EXTRACT(processed_result, '$.correlation_tracking'), '[]'),
          '$',
          ?
        ),
        '$.processing_stages',
        JSON_SET(
          COALESCE(JSON_EXTRACT(processed_result, '$.processing_stages'), '{}'),
          CONCAT('$.', ?),
          JSON_OBJECT(
            'timestamp', ?,
            'stage_data', ?,
            'correlation_id', ?
          )
        )
      )
      WHERE event_id = ?
    `, [
      JSON.stringify(correlationData),
      stage,
      new Date().toISOString(),
      JSON.stringify(additionalData || {}),
      correlationId,
      webhookEvent.event_id
    ]);
    
    logger.info('Enhanced event correlation tracked', {
      eventId: webhookEvent.event_id,
      correlationId,
      stage,
      transferId: webhookEvent.transfer_id,
      webhookType: webhookEvent.webhook_type,
      webhookCode: webhookEvent.webhook_code,
      verified: webhookEvent.verified,
      verificationMethod: webhookEvent.verification_method,
      priority: webhookEvent.processing_metadata?.priority,
      controllerVersion: webhookEvent.processing_metadata?.controller_version,
      additionalData
    });
    
  } catch (error) {
    logger.error('Error tracking enhanced event correlation', {
      eventId: webhookEvent.event_id,
      correlationId,
      stage,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    // Don't throw error as this is non-critical tracking
  }
}

/**
 * Get the sequence number for a processing stage to track progression
 */
async function getStageSequenceNumber(eventId: string, stage: string): Promise<number> {
  try {
    const { executeQuerySingle } = await import('../utils/database');
    const result = await executeQuerySingle(`
      SELECT JSON_LENGTH(JSON_EXTRACT(processed_result, '$.correlation_tracking')) as stage_count
      FROM tbl_webhook_events_enhanced 
      WHERE event_id = ?
    `, [eventId]);
    
    return (result?.stage_count || 0) + 1;
  } catch (error) {
    logger.error('Error getting stage sequence number', {
      eventId,
      stage,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return 1; // Default to 1 if error
  }
}