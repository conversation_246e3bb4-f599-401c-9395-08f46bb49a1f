import { Stripe } from 'stripe';
import { executeQuerySingle, executeUpdate } from '../utils/database';
import { getWalletBalance, updateWalletBalance, createWalletTransaction } from './walletService';
import { BankAccountValidationService } from './bankAccountValidationService';
import { StripePaymentMethodService } from './stripePaymentMethodService';
import logger from '../utils/logger';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-07-30.basil',
});

export interface BankAccount {
  id: string;
  plaid_account_id: string;
  stripe_payment_method_id?: string;
  stripe_bank_account_id?: string;
  bank_name: string;
  account_mask: string;
  account_type: string;
  routing_number?: string;
  is_primary: boolean;
}

export interface TransferResult {
  success: boolean;
  newBalance?: number;
  transaction?: any;
  paymentIntent?: Stripe.PaymentIntent;
  transfer?: Stripe.Transfer;
  requiresAction?: boolean;
  clientSecret?: string;
  error?: string;
  transferId?: string;
}

export class StripeWalletService {

  /**
   * Get or create Stripe customer for a user
   * @param userId The user ID
   * @returns Promise<string> - The Stripe customer ID
   */
  static async getOrCreateStripeCustomer(userId: number): Promise<string> {
    try {
      // First, try to get existing Stripe customer ID
      const user = await executeQuerySingle(
        'SELECT id, stripe_customer_id, email, full_name FROM tbl_users WHERE id = ?',
        [userId]
      );

      if (!user) {
        throw new Error('User not found');
      }

      // If user already has a Stripe customer ID, return it
      if (user.stripe_customer_id) {
        return user.stripe_customer_id;
      }

      // Create new Stripe customer
      logger.info('Creating new Stripe customer for user', { userId, email: user.email });

      const stripeCustomer = await stripe.customers.create({
        email: user.email,
        name: user.full_name || `User ${userId}`,
        metadata: {
          user_id: userId.toString(),
          created_by: 'auto_migration'
        }
      });

      // Update user record with Stripe customer ID
      await executeUpdate(
        'UPDATE tbl_users SET stripe_customer_id = ? WHERE id = ?',
        [stripeCustomer.id, userId]
      );

      logger.info('Stripe customer created successfully', {
        userId,
        stripeCustomerId: stripeCustomer.id,
        email: user.email
      });

      return stripeCustomer.id;

    } catch (error) {
      logger.error('Error getting or creating Stripe customer', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId
      });
      throw error;
    }
  }

  /**
   * Add money to a user's wallet using Stripe.
   * @param userId The ID of the user.
   * @param amount The amount to add.
   * @param bankAccountId The ID of the bank account to charge.
   * @param description A description for the transaction.
   * @returns An object with the result of the operation.
   */
  static async addMoneyToWallet(
    userId: number,
    amount: number,
    bankAccountId: string,
    description: string
  ): Promise<TransferResult> {
    try {
      // Get or create Stripe customer for the user
      const stripeCustomerId = await this.getOrCreateStripeCustomer(userId);

      // Get or create payment method for this bank account
      const paymentMethodResult = await StripePaymentMethodService.getOrCreatePaymentMethod(
        userId,
        bankAccountId,
        stripeCustomerId
      );

      if (!paymentMethodResult.success || !paymentMethodResult.paymentMethodId) {
        throw new Error(paymentMethodResult.error || 'Failed to create payment method for this bank account.');
      }

      const paymentMethod = { stripe_payment_method_id: paymentMethodResult.paymentMethodId };

      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100),
        currency: 'usd',
        customer: stripeCustomerId,
        payment_method: paymentMethod.stripe_payment_method_id,
        description,
        confirm: true,
        off_session: true,
      });

      if (paymentIntent.status === 'succeeded') {
        const currentBalance = await getWalletBalance(userId);
        if (currentBalance === null) {
          throw new Error('Could not retrieve wallet balance.');
        }
        const newBalance = currentBalance + amount;
        await updateWalletBalance(userId, newBalance);

        const transactionId = await createWalletTransaction(
          userId,
          amount,
          description,
          'deposit',
          paymentIntent.id,
          {
            bankAccountId,
            paymentProvider: 'stripe',
            stripePaymentIntentId: paymentIntent.id
          }
        );

        return {
          success: true,
          newBalance,
          transaction: { id: transactionId },
          paymentIntent,
        };
      } else if (paymentIntent.status === 'requires_action' || paymentIntent.status === 'requires_confirmation') {
        return {
          success: true,
          requiresAction: true,
          clientSecret: paymentIntent.client_secret || undefined,
          paymentIntent,
        };
      } else {
        throw new Error(`PaymentIntent failed with status: ${paymentIntent.status}`);
      }
    } catch (error) {
      logger.error('Error adding money to wallet via Stripe', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId,
        amount,
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred.',
      };
    }
  }

  /**
   * Withdraw money from a user's wallet using Stripe.
   * @param userId The ID of the user.
   * @param amount The amount to withdraw.
   * @param bankAccountId The ID of the bank account to send the money to.
   * @param description A description for the transaction.
   * @returns An object with the result of the operation.
   */
  static async withdrawFromWallet(
    userId: number,
    amount: number,
    bankAccountId: string,
    description: string
  ): Promise<TransferResult> {
    try {
      const currentBalance = await getWalletBalance(userId);
      if (currentBalance === null || currentBalance < amount) {
        throw new Error('Insufficient funds.');
      }

      // Get or create Stripe customer for the user
      const stripeCustomerId = await this.getOrCreateStripeCustomer(userId);

      const bankAccount = await executeQuerySingle<{ stripe_bank_account_id: string | null }>(
        'SELECT stripe_bank_account_id FROM tbl_bank_accounts WHERE id = ?',
        [bankAccountId]
      );
      if (!bankAccount || !bankAccount.stripe_bank_account_id) {
        throw new Error('Stripe bank account not found.');
      }

      const destination = bankAccount.stripe_bank_account_id;
      if (!destination) {
        throw new Error('Stripe bank account ID is null.');
      }

      const transfer = await stripe.transfers.create({
        amount: Math.round(amount * 100),
        currency: 'usd',
        destination: destination,
        description,
      });

      const newBalance = currentBalance - amount;
      await updateWalletBalance(userId, newBalance);

      const transactionId = await createWalletTransaction(
        userId,
        -amount,
        description,
        'withdraw',
        transfer.id,
        {
          bankAccountId,
          paymentProvider: 'stripe',
          stripeTransferId: transfer.id
        }
      );

      return {
        success: true,
        newBalance,
        transaction: { id: transactionId },
        transfer,
        transferId: transfer.id
      };
    } catch (error) {
      logger.error('Error withdrawing money from wallet via Stripe', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId,
        amount,
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred.',
      };
    }
  }

  /**
   * Transfer money directly from one bank account to another using Stripe.
   * This bypasses the wallet and transfers directly between bank accounts.
   * @param fromUserId The ID of the sender.
   * @param toUserId The ID of the recipient.
   * @param amount The amount to transfer.
   * @param fromBankAccountId The sender's bank account ID.
   * @param toBankAccountId The recipient's bank account ID.
   * @param description A description for the transaction.
   * @returns An object with the result of the operation.
   */
  static async transferBankToBank(
    fromUserId: number,
    toUserId: number,
    amount: number,
    fromBankAccountId: string,
    toBankAccountId: string,
    description: string
  ): Promise<TransferResult> {
    try {
      // Get or create Stripe customers for both users
      const fromStripeCustomerId = await this.getOrCreateStripeCustomer(fromUserId);
      const toStripeCustomerId = await this.getOrCreateStripeCustomer(toUserId);

      // Get or create sender's payment method
      const fromPaymentMethodResult = await StripePaymentMethodService.getOrCreatePaymentMethod(
        fromUserId,
        fromBankAccountId,
        fromStripeCustomerId
      );

      if (!fromPaymentMethodResult.success || !fromPaymentMethodResult.paymentMethodId) {
        throw new Error(fromPaymentMethodResult.error || 'Failed to create sender payment method.');
      }

      const fromPaymentMethod = { stripe_payment_method_id: fromPaymentMethodResult.paymentMethodId };

      // Get recipient's bank account
      const toBankAccount = await executeQuerySingle<{ stripe_bank_account_id: string | null }>(
        'SELECT stripe_bank_account_id FROM tbl_bank_accounts WHERE id = ?',
        [toBankAccountId]
      );
      if (!toBankAccount || !toBankAccount.stripe_bank_account_id) {
        throw new Error('Recipient bank account not found.');
      }

      // Step 1: Create payment intent to charge sender's bank account
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100),
        currency: 'usd',
        customer: fromStripeCustomerId,
        payment_method: fromPaymentMethod.stripe_payment_method_id,
        description: `Bank transfer: ${description}`,
        confirm: true,
        off_session: true,
      });

      if (paymentIntent.status !== 'succeeded') {
        if (paymentIntent.status === 'requires_action' || paymentIntent.status === 'requires_confirmation') {
          return {
            success: true,
            requiresAction: true,
            clientSecret: paymentIntent.client_secret || undefined,
            paymentIntent,
          };
        } else {
          throw new Error(`Payment failed with status: ${paymentIntent.status}`);
        }
      }

      // Step 2: Create transfer to recipient's bank account
      const transfer = await stripe.transfers.create({
        amount: Math.round(amount * 100),
        currency: 'usd',
        destination: toBankAccount.stripe_bank_account_id!,
        description: `Bank transfer: ${description}`,
      });

      // Step 3: Record transactions for both users
      const referenceId = `B2B_${Date.now()}_${fromUserId}_${toUserId}`;

      const senderTransactionId = await createWalletTransaction(
        fromUserId,
        -amount,
        `Bank transfer to user ${toUserId}: ${description}`,
        'bank_transfer_out',
        referenceId,
        {
          recipientUserId: toUserId,
          paymentProvider: 'stripe',
          stripePaymentIntentId: paymentIntent.id,
          stripeTransferId: transfer.id,
          transferType: 'bank_to_bank_out'
        }
      );

      const recipientTransactionId = await createWalletTransaction(
        toUserId,
        amount,
        `Bank transfer from user ${fromUserId}: ${description}`,
        'bank_transfer_in',
        referenceId,
        {
          senderUserId: fromUserId,
          paymentProvider: 'stripe',
          stripePaymentIntentId: paymentIntent.id,
          stripeTransferId: transfer.id,
          transferType: 'bank_to_bank_in'
        }
      );

      return {
        success: true,
        transaction: {
          senderTransactionId,
          recipientTransactionId
        },
        paymentIntent,
        transfer,
        transferId: transfer.id
      };
    } catch (error) {
      logger.error('Error in bank-to-bank transfer via Stripe', {
        error: error instanceof Error ? error.message : 'Unknown error',
        fromUserId,
        toUserId,
        amount,
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred.',
      };
    }
  }

  /**
   * Validate bank account for transfers using enhanced Plaid and Stripe validation.
   * @param bankAccountId The bank account ID to validate.
   * @param userId The user ID for authorization.
   * @returns Promise with comprehensive validation results.
   */
  static async validateBankAccount(bankAccountId: string, userId: number): Promise<{
    valid: boolean;
    error?: string;
    account?: BankAccount;
    warnings?: string[];
    validationDetails?: any;
  }> {
    try {
      // Use enhanced validation service
      const validation = await BankAccountValidationService.validateBankAccountForTransfers(
        bankAccountId,
        userId
      );

      if (validation.valid) {
        return {
          valid: true,
          account: validation.account,
          warnings: validation.warnings,
          validationDetails: validation.validationDetails
        };
      } else {
        return {
          valid: false,
          error: validation.error,
          account: validation.account,
          warnings: validation.warnings,
          validationDetails: validation.validationDetails
        };
      }
    } catch (error) {
      logger.error('Error validating bank account', {
        error: error instanceof Error ? error.message : 'Unknown error',
        bankAccountId,
        userId,
      });
      return { valid: false, error: 'Failed to validate bank account.' };
    }
  }

  /**
   * Check if bank account is ready for immediate transfers.
   * @param bankAccountId The bank account ID to check.
   * @param userId The user ID for authorization.
   * @returns Promise with transfer readiness status.
   */
  static async isAccountReadyForTransfers(bankAccountId: string, userId: number): Promise<{
    ready: boolean;
    canDeposit: boolean;
    canWithdraw: boolean;
    issues: string[];
  }> {
    try {
      return await BankAccountValidationService.isAccountReadyForTransfers(bankAccountId, userId);
    } catch (error) {
      logger.error('Error checking account transfer readiness', {
        error: error instanceof Error ? error.message : 'Unknown error',
        bankAccountId,
        userId,
      });
      return {
        ready: false,
        canDeposit: false,
        canWithdraw: false,
        issues: ['Failed to check account readiness due to system error.']
      };
    }
  }

  /**
   * Get transfer status from Stripe.
   * @param transferId The Stripe transfer ID.
   * @returns Promise with transfer status information.
   */
  static async getTransferStatus(transferId: string): Promise<{
    success: boolean;
    status?: string;
    transfer?: Stripe.Transfer;
    error?: string;
  }> {
    try {
      const transfer = await stripe.transfers.retrieve(transferId);
      // Stripe transfers are typically completed immediately or failed
      // We can infer status from the transfer object properties
      const status = transfer.amount > 0 ? 'completed' : 'failed';

      return {
        success: true,
        status,
        transfer,
      };
    } catch (error) {
      logger.error('Error getting transfer status', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transferId,
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get transfer status.',
      };
    }
  }

  /**
   * Get payment intent status from Stripe.
   * @param paymentIntentId The Stripe payment intent ID.
   * @returns Promise with payment intent status information.
   */
  static async getPaymentIntentStatus(paymentIntentId: string): Promise<{
    success: boolean;
    status?: string;
    paymentIntent?: Stripe.PaymentIntent;
    error?: string;
  }> {
    try {
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      return {
        success: true,
        status: paymentIntent.status,
        paymentIntent,
      };
    } catch (error) {
      logger.error('Error getting payment intent status', {
        error: error instanceof Error ? error.message : 'Unknown error',
        paymentIntentId,
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get payment intent status.',
      };
    }
  }

  /**
   * Cancel a pending transfer.
   * @param transferId The Stripe transfer ID to cancel.
   * @returns Promise with cancellation result.
   */
  static async cancelTransfer(transferId: string): Promise<{
    success: boolean;
    transfer?: Stripe.Transfer;
    error?: string;
  }> {
    try {
      // Note: Stripe transfers cannot be cancelled once created
      const transfer = await stripe.transfers.retrieve(transferId);

      // Stripe transfers are typically processed immediately
      // We can only return information about the transfer
      return {
        success: false,
        error: 'Stripe transfers cannot be cancelled once created. Contact support if needed.',
        transfer,
      };
    } catch (error) {
      logger.error('Error retrieving transfer for cancellation', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transferId,
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to retrieve transfer.',
      };
    }
  }

  /**
   * Get comprehensive transfer status from database and Stripe.
   * @param transactionId The database transaction ID.
   * @returns Promise with comprehensive transfer status.
   */
  static async getComprehensiveTransferStatus(transactionId: number): Promise<{
    success: boolean;
    transaction?: any;
    stripeStatus?: any;
    error?: string;
  }> {
    try {
      // Get transaction from database
      const transaction = await executeQuerySingle(
        `SELECT * FROM tbl_wallet_transactions WHERE id = ?`,
        [transactionId]
      );

      if (!transaction) {
        return { success: false, error: 'Transaction not found.' };
      }

      let stripeStatus = null;
      const metaData = transaction.meta_data ? JSON.parse(transaction.meta_data) : {};

      // Get Stripe status based on transaction type
      if (metaData.stripePaymentIntentId) {
        const paymentIntentStatus = await this.getPaymentIntentStatus(metaData.stripePaymentIntentId);
        if (paymentIntentStatus.success) {
          stripeStatus = {
            type: 'payment_intent',
            ...paymentIntentStatus
          };
        }
      } else if (metaData.stripeTransferId) {
        const transferStatus = await this.getTransferStatus(metaData.stripeTransferId);
        if (transferStatus.success) {
          stripeStatus = {
            type: 'transfer',
            ...transferStatus
          };
        }
      }

      return {
        success: true,
        transaction: {
          ...transaction,
          meta_data: metaData
        },
        stripeStatus
      };

    } catch (error) {
      logger.error('Error getting comprehensive transfer status', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId,
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get transfer status.',
      };
    }
  }

  /**
   * Update transaction status based on Stripe webhook data.
   * @param stripeId The Stripe payment intent or transfer ID.
   * @param status The new status.
   * @param metadata Additional metadata to store.
   * @returns Promise with update result.
   */
  static async updateTransactionFromWebhook(
    stripeId: string,
    status: string,
    metadata?: any
  ): Promise<{
    success: boolean;
    transactionId?: number;
    error?: string;
  }> {
    try {
      // Find transaction by Stripe ID
      const transaction = await executeQuerySingle(
        `SELECT * FROM tbl_wallet_transactions
         WHERE reference_id = ? OR JSON_EXTRACT(meta_data, '$.stripePaymentIntentId') = ?
         OR JSON_EXTRACT(meta_data, '$.stripeTransferId') = ?`,
        [stripeId, stripeId, stripeId]
      );

      if (!transaction) {
        return { success: false, error: 'Transaction not found for Stripe ID.' };
      }

      // Map Stripe status to database status
      let statusId = 2; // Default to pending
      switch (status) {
        case 'succeeded':
        case 'paid':
        case 'completed':
          statusId = 1; // Completed
          break;
        case 'failed':
        case 'canceled':
          statusId = 3; // Failed
          break;
        case 'requires_action':
        case 'requires_confirmation':
          statusId = 4; // Requires action
          break;
        case 'processing':
        case 'pending':
        default:
          statusId = 2; // Pending
          break;
      }

      // Update transaction
      await executeUpdate(
        'UPDATE tbl_wallet_transactions SET status_id = ?, updated_at = NOW() WHERE id = ?',
        [statusId, transaction.id]
      );

      // Update metadata if provided
      if (metadata) {
        const currentMetaData = transaction.meta_data ? JSON.parse(transaction.meta_data) : {};
        const updatedMetaData = { ...currentMetaData, ...metadata };

        await executeUpdate(
          'UPDATE tbl_wallet_transactions SET meta_data = ? WHERE id = ?',
          [JSON.stringify(updatedMetaData), transaction.id]
        );
      }

      return {
        success: true,
        transactionId: transaction.id
      };

    } catch (error) {
      logger.error('Error updating transaction from webhook', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stripeId,
        status
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update transaction.',
      };
    }
  }

  /**
   * Migrate existing users to Stripe customers (bulk operation)
   * @param limit Maximum number of users to migrate in one batch
   * @returns Promise with migration results
   */
  static async migrateUsersToStripeCustomers(limit: number = 50): Promise<{
    success: boolean;
    migrated: number;
    failed: number;
    errors: string[];
  }> {
    try {
      logger.info('Starting bulk migration of users to Stripe customers', { limit });

      // Get users without Stripe customer IDs
      const usersToMigrate = await executeQuerySingle(
        `SELECT id, email, full_name FROM tbl_users
         WHERE stripe_customer_id IS NULL OR stripe_customer_id = ''
         LIMIT ?`,
        [limit]
      );

      if (!usersToMigrate) {
        return {
          success: true,
          migrated: 0,
          failed: 0,
          errors: []
        };
      }

      const users = Array.isArray(usersToMigrate) ? usersToMigrate : [usersToMigrate];
      let migrated = 0;
      let failed = 0;
      const errors: string[] = [];

      for (const user of users) {
        try {
          await this.getOrCreateStripeCustomer(user.id);
          migrated++;

          logger.info('User migrated to Stripe customer', {
            userId: user.id,
            email: user.email
          });
        } catch (error) {
          failed++;
          const errorMessage = `User ${user.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);

          logger.error('Failed to migrate user to Stripe customer', {
            userId: user.id,
            email: user.email,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      logger.info('Bulk migration completed', {
        totalUsers: users.length,
        migrated,
        failed,
        errorCount: errors.length
      });

      return {
        success: failed === 0,
        migrated,
        failed,
        errors
      };

    } catch (error) {
      logger.error('Error in bulk migration to Stripe customers', {
        error: error instanceof Error ? error.message : 'Unknown error',
        limit
      });

      return {
        success: false,
        migrated: 0,
        failed: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }
}
