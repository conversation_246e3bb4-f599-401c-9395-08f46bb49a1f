import Stripe from 'stripe';
import { executeQuerySingle, executeUpdate } from '../utils/database';
import { updateWalletBalance } from './walletService';
import { TransferRecoveryService } from './transferRecoveryService';
import { WithdrawalProgressFallback as WithdrawalProgressService } from './withdrawalProgressFallback';
import logger from '../utils/logger';

/**
 * Process Stripe webhook events for wallet transactions
 */
export async function processStripeWebhookEvent(event: Stripe.Event): Promise<void> {
  logger.info('Processing Stripe webhook event', {
    eventId: event.id,
    eventType: event.type,
    created: event.created
  });

  switch (event.type) {
    case 'payment_intent.succeeded':
      await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
      break;

    case 'payment_intent.payment_failed':
      await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
      break;

    case 'payment_intent.requires_action':
      await handlePaymentIntentRequiresAction(event.data.object as Stripe.PaymentIntent);
      break;

    case 'transfer.created':
      await handleTransferCreated(event.data.object as Stripe.Transfer);
      break;

    case 'transfer.paid':
      await handleTransferPaid(event.data.object as Stripe.Transfer);
      break;

    case 'transfer.failed':
      await handleTransferFailed(event.data.object as Stripe.Transfer);
      break;

    case 'payout.paid':
      await handlePayoutPaid(event.data.object as Stripe.Payout);
      break;

    case 'payout.failed':
      await handlePayoutFailed(event.data.object as Stripe.Payout);
      break;

    default:
      logger.info('Unhandled Stripe webhook event type', {
        eventType: event.type,
        eventId: event.id
      });
  }
}

/**
 * Handle successful payment intent (money added to wallet)
 */
async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<void> {
  try {
    logger.info('Processing payment_intent.succeeded', {
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency
    });

    // Find the transaction record by Stripe payment intent ID
    const transaction = await executeQuerySingle(
      `SELECT * FROM tbl_wallet_transactions 
       WHERE reference_id = ? OR JSON_EXTRACT(meta_data, '$.stripePaymentIntentId') = ?`,
      [paymentIntent.id, paymentIntent.id]
    );

    if (!transaction) {
      logger.warn('No transaction found for payment intent', {
        paymentIntentId: paymentIntent.id
      });
      return;
    }

    // Update transaction status to completed
    await executeUpdate(
      'UPDATE tbl_wallet_transactions SET status_id = 1, updated_at = NOW() WHERE id = ?',
      [transaction.id]
    );

    // If this was a pending transaction, update the wallet balance
    if (transaction.status_id === 2) { // 2 = pending
      const amount = paymentIntent.amount / 100; // Convert from cents
      const currentBalance = await executeQuerySingle(
        'SELECT balance FROM tbl_masterwallet WHERE user_id = ?',
        [transaction.user_id]
      );

      if (currentBalance) {
        const newBalance = currentBalance.balance + amount;
        await updateWalletBalance(transaction.user_id, newBalance);

        logger.info('Wallet balance updated after payment success', {
          userId: transaction.user_id,
          previousBalance: currentBalance.balance,
          newBalance,
          amount,
          transactionId: transaction.id
        });
      }
    }

    logger.info('Payment intent succeeded processed successfully', {
      paymentIntentId: paymentIntent.id,
      transactionId: transaction.id,
      userId: transaction.user_id
    });

  } catch (error) {
    logger.error('Error handling payment_intent.succeeded', {
      paymentIntentId: paymentIntent.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Handle failed payment intent
 */
async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent): Promise<void> {
  try {
    logger.info('Processing payment_intent.payment_failed', {
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount
    });

    // Find and update the transaction record
    const transaction = await executeQuerySingle(
      `SELECT * FROM tbl_wallet_transactions
       WHERE reference_id = ? OR JSON_EXTRACT(meta_data, '$.stripePaymentIntentId') = ?`,
      [paymentIntent.id, paymentIntent.id]
    );

    if (transaction) {
      // Update transaction status to failed
      await executeUpdate(
        'UPDATE tbl_wallet_transactions SET status_id = 3, updated_at = NOW() WHERE id = ?',
        [transaction.id]
      );

      // Attempt automatic recovery for failed payment
      const failureReason = `Stripe payment failed: ${paymentIntent.last_payment_error?.message || 'Unknown error'}`;

      try {
        const recoveryResult = await TransferRecoveryService.handleStripeFailure(
          transaction.id,
          paymentIntent.id,
          failureReason
        );

        logger.info('Automatic recovery attempted for failed payment', {
          paymentIntentId: paymentIntent.id,
          transactionId: transaction.id,
          recoveryResult
        });
      } catch (recoveryError) {
        logger.error('Failed to execute automatic recovery', {
          paymentIntentId: paymentIntent.id,
          transactionId: transaction.id,
          recoveryError: recoveryError instanceof Error ? recoveryError.message : 'Unknown error'
        });
      }

      logger.info('Payment intent failed processed successfully', {
        paymentIntentId: paymentIntent.id,
        transactionId: transaction.id,
        userId: transaction.user_id
      });
    }

  } catch (error) {
    logger.error('Error handling payment_intent.payment_failed', {
      paymentIntentId: paymentIntent.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Handle payment intent that requires action
 */
async function handlePaymentIntentRequiresAction(paymentIntent: Stripe.PaymentIntent): Promise<void> {
  try {
    logger.info('Processing payment_intent.requires_action', {
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount
    });

    // Find and update the transaction record
    const transaction = await executeQuerySingle(
      `SELECT * FROM tbl_wallet_transactions 
       WHERE reference_id = ? OR JSON_EXTRACT(meta_data, '$.stripePaymentIntentId') = ?`,
      [paymentIntent.id, paymentIntent.id]
    );

    if (transaction) {
      // Update transaction status to pending action
      await executeUpdate(
        'UPDATE tbl_wallet_transactions SET status_id = 4, updated_at = NOW() WHERE id = ?',
        [transaction.id]
      );

      logger.info('Payment intent requires action processed successfully', {
        paymentIntentId: paymentIntent.id,
        transactionId: transaction.id,
        userId: transaction.user_id
      });
    }

  } catch (error) {
    logger.error('Error handling payment_intent.requires_action', {
      paymentIntentId: paymentIntent.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Handle transfer created (withdrawal initiated)
 */
async function handleTransferCreated(transfer: Stripe.Transfer): Promise<void> {
  try {
    logger.info('Processing transfer.created', {
      transferId: transfer.id,
      amount: transfer.amount,
      destination: transfer.destination
    });

    // Find the transaction record by Stripe transfer ID
    const transaction = await executeQuerySingle(
      `SELECT * FROM tbl_wallet_transactions 
       WHERE reference_id = ? OR JSON_EXTRACT(meta_data, '$.stripeTransferId') = ?`,
      [transfer.id, transfer.id]
    );

    if (transaction) {
      // Update transaction with transfer details
      await executeUpdate(
        'UPDATE tbl_wallet_transactions SET status_id = 2, updated_at = NOW() WHERE id = ?',
        [transaction.id]
      );

      logger.info('Transfer created processed successfully', {
        transferId: transfer.id,
        transactionId: transaction.id,
        userId: transaction.user_id
      });
    }

  } catch (error) {
    logger.error('Error handling transfer.created', {
      transferId: transfer.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Handle transfer paid (withdrawal completed)
 */
async function handleTransferPaid(transfer: Stripe.Transfer): Promise<void> {
  try {
    logger.info('Processing transfer.paid', {
      transferId: transfer.id,
      amount: transfer.amount
    });

    // Find and update the transaction record
    const transaction = await executeQuerySingle(
      `SELECT * FROM tbl_wallet_transactions 
       WHERE reference_id = ? OR JSON_EXTRACT(meta_data, '$.stripeTransferId') = ?`,
      [transfer.id, transfer.id]
    );

    if (transaction) {
      // Update transaction status to completed
      await executeUpdate(
        'UPDATE tbl_wallet_transactions SET status_id = 1, updated_at = NOW() WHERE id = ?',
        [transaction.id]
      );

      // Update withdrawal progress to completed
      await WithdrawalProgressService.processSuccessfulWithdrawal(transaction.id);

      logger.info('Transfer paid processed successfully', {
        transferId: transfer.id,
        transactionId: transaction.id,
        userId: transaction.user_id
      });
    }

  } catch (error) {
    logger.error('Error handling transfer.paid', {
      transferId: transfer.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Handle transfer failed (withdrawal failed)
 */
async function handleTransferFailed(transfer: Stripe.Transfer): Promise<void> {
  try {
    logger.info('Processing transfer.failed', {
      transferId: transfer.id,
      amount: transfer.amount
    });

    // Find the transaction record
    const transaction = await executeQuerySingle(
      `SELECT * FROM tbl_wallet_transactions
       WHERE reference_id = ? OR JSON_EXTRACT(meta_data, '$.stripeTransferId') = ?`,
      [transfer.id, transfer.id]
    );

    if (transaction) {
      // Update transaction status to failed
      await executeUpdate(
        'UPDATE tbl_wallet_transactions SET status_id = 3, updated_at = NOW() WHERE id = ?',
        [transaction.id]
      );

      // Use recovery service for proper handling
      const failureReason = `Stripe transfer failed: Transfer to bank account failed`;

      try {
        const recoveryResult = await TransferRecoveryService.handleStripeFailure(
          transaction.id,
          transfer.id,
          failureReason
        );

        logger.info('Automatic recovery executed for failed transfer', {
          transferId: transfer.id,
          transactionId: transaction.id,
          recoveryResult
        });
      } catch (recoveryError) {
        logger.error('Failed to execute automatic recovery for transfer', {
          transferId: transfer.id,
          transactionId: transaction.id,
          recoveryError: recoveryError instanceof Error ? recoveryError.message : 'Unknown error'
        });

        // Fallback: manual balance reversal
        const amount = Math.abs(transaction.amount);
        const currentBalance = await executeQuerySingle(
          'SELECT balance FROM tbl_masterwallet WHERE user_id = ?',
          [transaction.user_id]
        );

        if (currentBalance) {
          const newBalance = currentBalance.balance + amount;
          await updateWalletBalance(transaction.user_id, newBalance);

          logger.info('Fallback wallet balance reversal completed', {
            userId: transaction.user_id,
            previousBalance: currentBalance.balance,
            newBalance,
            reversedAmount: amount,
            transactionId: transaction.id
          });
        }
      }

      // Update withdrawal progress to failed
      await WithdrawalProgressService.processFailedWithdrawal(
        transaction.id,
        'bank_transfer',
        'Stripe transfer failed'
      );

      logger.info('Transfer failed processed successfully', {
        transferId: transfer.id,
        transactionId: transaction.id,
        userId: transaction.user_id
      });
    }

  } catch (error) {
    logger.error('Error handling transfer.failed', {
      transferId: transfer.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Handle payout paid (for future use)
 */
async function handlePayoutPaid(payout: Stripe.Payout): Promise<void> {
  logger.info('Processing payout.paid', {
    payoutId: payout.id,
    amount: payout.amount
  });
  // Implementation for payout handling if needed
}

/**
 * Handle payout failed (for future use)
 */
async function handlePayoutFailed(payout: Stripe.Payout): Promise<void> {
  logger.info('Processing payout.failed', {
    payoutId: payout.id,
    amount: payout.amount
  });
  // Implementation for payout failure handling if needed
}
