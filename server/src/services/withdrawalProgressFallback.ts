import { executeQuery<PERSON>ingle } from '../utils/database';
import logger from '../utils/logger';

/**
 * Fallback withdrawal progress service that works without dedicated progress tables
 * Uses transaction status and metadata to provide progress information
 */
export class WithdrawalProgressFallback {

  /**
   * Get withdrawal progress based on transaction status
   */
  static async getWithdrawalProgress(transactionId: number): Promise<any[]> {
    try {
      // Get transaction details
      const transaction = await executeQuerySingle(
        'SELECT id, status_id, created_at, updated_at, meta_data FROM tbl_wallet_transactions WHERE id = ?',
        [transactionId]
      );

      if (!transaction) {
        return [];
      }

      // Parse metadata
      let metadata = {};
      try {
        metadata = transaction.meta_data ? JSON.parse(transaction.meta_data) : {};
      } catch (error) {
        logger.warn('Failed to parse transaction metadata', { transactionId, error });
      }

      // Generate progress steps based on transaction status
      const steps = this.generateProgressSteps(transaction.status_id, transaction.created_at, transaction.updated_at);

      return steps;

    } catch (error) {
      logger.error('Error getting withdrawal progress (fallback)', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId
      });
      return [];
    }
  }

  /**
   * Get withdrawal status summary
   */
  static async getWithdrawalStatus(transactionId: number): Promise<any> {
    try {
      const transaction = await executeQuerySingle(
        'SELECT id, status_id, created_at, updated_at FROM tbl_wallet_transactions WHERE id = ?',
        [transactionId]
      );

      if (!transaction) {
        return null;
      }

      const steps = this.generateProgressSteps(transaction.status_id, transaction.created_at, transaction.updated_at);
      const completedSteps = steps.filter(s => s.status === 'completed').length;

      // Map status_id to overall status
      let overall: 'pending' | 'completed' | 'failed';
      switch (transaction.status_id) {
        case 1: // completed
          overall = 'completed';
          break;
        case 3: // failed
          overall = 'failed';
          break;
        default:
          overall = 'pending';
      }

      // Find current step
      const currentStep = steps.find(s => s.status === 'in_progress') || 
                         steps.find(s => s.status === 'pending') ||
                         steps[steps.length - 1];

      return {
        overall,
        currentStep: currentStep.step_key,
        currentStepTitle: currentStep.title,
        currentStepDescription: currentStep.description,
        lastUpdated: transaction.updated_at || transaction.created_at,
        progress: {
          completed: completedSteps,
          total: steps.length,
          percentage: Math.round((completedSteps / steps.length) * 100)
        }
      };

    } catch (error) {
      logger.error('Error getting withdrawal status (fallback)', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId
      });
      return null;
    }
  }

  /**
   * Generate progress steps based on transaction status
   */
  private static generateProgressSteps(statusId: number, createdAt: string, updatedAt: string): any[] {
    const now = new Date().toISOString();
    const steps = [
      {
        step_key: 'initiated',
        title: 'Withdrawal Initiated',
        description: 'Your withdrawal request has been received and is being processed.',
        step_order: 1,
        status: 'completed',
        completed_at: createdAt,
        notes: null
      },
      {
        step_key: 'validation',
        title: 'Account Validation',
        description: 'Verifying your bank account details and withdrawal eligibility.',
        step_order: 2,
        status: 'completed',
        completed_at: createdAt,
        notes: 'Account validated successfully'
      }
    ];

    switch (statusId) {
      case 1: // completed
        steps.push(
          {
            step_key: 'processing',
            title: 'Processing Payment',
            description: 'Your withdrawal is being processed through our payment provider.',
            step_order: 3,
            status: 'completed',
            completed_at: updatedAt || createdAt,
            notes: 'Payment processed via Stripe'
          },
          {
            step_key: 'bank_transfer',
            title: 'Bank Transfer',
            description: 'Funds are being transferred to your bank account.',
            step_order: 4,
            status: 'completed',
            completed_at: updatedAt || createdAt,
            notes: 'Funds transferred to bank account'
          },
          {
            step_key: 'completed',
            title: 'Transfer Completed',
            description: 'Your withdrawal has been successfully completed.',
            step_order: 5,
            status: 'completed',
            completed_at: updatedAt || createdAt,
            notes: 'Withdrawal completed successfully'
          }
        );
        break;

      case 3: // failed
        steps.push(
          {
            step_key: 'processing',
            title: 'Processing Payment',
            description: 'Your withdrawal is being processed through our payment provider.',
            step_order: 3,
            status: 'failed',
            completed_at: updatedAt || createdAt,
            notes: 'Payment processing failed'
          },
          {
            step_key: 'bank_transfer',
            title: 'Bank Transfer',
            description: 'Funds are being transferred to your bank account.',
            step_order: 4,
            status: 'skipped',
            completed_at: null,
            notes: 'Skipped due to processing failure'
          },
          {
            step_key: 'completed',
            title: 'Transfer Completed',
            description: 'Your withdrawal has been successfully completed.',
            step_order: 5,
            status: 'skipped',
            completed_at: null,
            notes: 'Skipped due to processing failure'
          }
        );
        break;

      case 2: // pending/processing
      default:
        steps.push(
          {
            step_key: 'processing',
            title: 'Processing Payment',
            description: 'Your withdrawal is being processed through our payment provider.',
            step_order: 3,
            status: 'in_progress',
            completed_at: null,
            notes: 'Processing withdrawal via Stripe'
          },
          {
            step_key: 'bank_transfer',
            title: 'Bank Transfer',
            description: 'Funds are being transferred to your bank account.',
            step_order: 4,
            status: 'pending',
            completed_at: null,
            notes: null
          },
          {
            step_key: 'completed',
            title: 'Transfer Completed',
            description: 'Your withdrawal has been successfully completed.',
            step_order: 5,
            status: 'pending',
            completed_at: null,
            notes: null
          }
        );
        break;
    }

    return steps;
  }

  /**
   * Initialize withdrawal progress (no-op for fallback)
   */
  static async initializeWithdrawalProgress(transactionId: number): Promise<boolean> {
    logger.info('Using fallback withdrawal progress (no dedicated tables)', { transactionId });
    return true;
  }

  /**
   * Update withdrawal step (no-op for fallback)
   */
  static async updateWithdrawalStep(
    transactionId: number,
    stepKey: string,
    status: string,
    notes?: string
  ): Promise<boolean> {
    logger.info('Using fallback withdrawal progress update', { transactionId, stepKey, status });
    return true;
  }

  /**
   * Process successful withdrawal (no-op for fallback)
   */
  static async processSuccessfulWithdrawal(transactionId: number): Promise<boolean> {
    logger.info('Using fallback successful withdrawal processing', { transactionId });
    return true;
  }

  /**
   * Process failed withdrawal (no-op for fallback)
   */
  static async processFailedWithdrawal(
    transactionId: number,
    failedStep: string,
    reason: string
  ): Promise<boolean> {
    logger.info('Using fallback failed withdrawal processing', { transactionId, failedStep, reason });
    return true;
  }
}
