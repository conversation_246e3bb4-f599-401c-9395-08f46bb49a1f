import { plaidClient } from '../config/plaidConfig';
import { executeQuerySingle } from '../utils/database';
import { AccountsGetRequest, AccountBase } from 'plaid';
import logger from '../utils/logger';

export interface BankAccountValidationResult {
  valid: boolean;
  account?: any;
  plaidAccount?: AccountBase;
  validationDetails?: {
    hasStripePaymentMethod: boolean;
    hasStripeBankAccount: boolean;
    plaidAccountActive: boolean;
    accountTypeSupported: boolean;
    routingNumberValid: boolean;
  };
  error?: string;
  warnings?: string[];
}

export interface PlaidAccountStatus {
  active: boolean;
  available: boolean;
  balance?: number;
  lastUpdated?: Date;
  error?: string;
}

/**
 * Enhanced bank account validation service using both Plaid and Stripe data
 */
export class BankAccountValidationService {
  
  /**
   * Validate a bank account for transfers using comprehensive checks
   */
  static async validateBankAccountForTransfers(
    bankAccountId: string, 
    userId: number
  ): Promise<BankAccountValidationResult> {
    try {
      logger.info('Starting comprehensive bank account validation', {
        bankAccountId,
        userId
      });

      // Get bank account from database
      const bankAccount = await executeQuerySingle(
        `SELECT id, user_id, plaid_account_id, plaid_access_token, stripe_payment_method_id, 
         stripe_bank_account_id, bank_name, account_mask, account_type, routing_number, 
         is_primary, created_at, updated_at 
         FROM tbl_bank_accounts WHERE id = ? AND user_id = ?`,
        [bankAccountId, userId]
      );

      if (!bankAccount) {
        return {
          valid: false,
          error: 'Bank account not found or not authorized.'
        };
      }

      const warnings: string[] = [];
      const validationDetails = {
        hasStripePaymentMethod: !!bankAccount.stripe_payment_method_id,
        hasStripeBankAccount: !!bankAccount.stripe_bank_account_id,
        plaidAccountActive: false,
        accountTypeSupported: false,
        routingNumberValid: false
      };

      // Validate Stripe integration
      if (!bankAccount.stripe_payment_method_id) {
        warnings.push('Bank account not set up for deposits (missing Stripe payment method)');
      }

      if (!bankAccount.stripe_bank_account_id) {
        warnings.push('Bank account not set up for withdrawals (missing Stripe bank account)');
      }

      // Validate account type
      const supportedAccountTypes = ['checking', 'savings'];
      validationDetails.accountTypeSupported = supportedAccountTypes.includes(
        bankAccount.account_type?.toLowerCase()
      );

      if (!validationDetails.accountTypeSupported) {
        warnings.push(`Account type '${bankAccount.account_type}' may not be supported for transfers`);
      }

      // Validate routing number format
      if (bankAccount.routing_number) {
        validationDetails.routingNumberValid = /^\d{9}$/.test(bankAccount.routing_number);
        if (!validationDetails.routingNumberValid) {
          warnings.push('Invalid routing number format');
        }
      }

      // Validate with Plaid if access token is available
      let plaidAccount: AccountBase | undefined;
      if (bankAccount.plaid_access_token && bankAccount.plaid_account_id) {
        try {
          const plaidStatus = await this.getPlaidAccountStatus(
            bankAccount.plaid_access_token,
            bankAccount.plaid_account_id
          );

          if (plaidStatus.active) {
            validationDetails.plaidAccountActive = true;
          } else {
            warnings.push('Plaid reports account as inactive or unavailable');
          }

          // Get detailed account info from Plaid
          plaidAccount = await this.getPlaidAccountDetails(
            bankAccount.plaid_access_token,
            bankAccount.plaid_account_id
          );

        } catch (error) {
          logger.warn('Failed to validate with Plaid', {
            error: error instanceof Error ? error.message : 'Unknown error',
            bankAccountId,
            userId
          });
          warnings.push('Unable to verify account status with bank (Plaid validation failed)');
        }
      } else {
        warnings.push('No Plaid access token available for real-time validation');
      }

      // Determine overall validity
      const isValid = validationDetails.hasStripePaymentMethod && 
                     validationDetails.hasStripeBankAccount &&
                     validationDetails.accountTypeSupported &&
                     (validationDetails.routingNumberValid || !bankAccount.routing_number);

      return {
        valid: isValid,
        account: bankAccount,
        plaidAccount,
        validationDetails,
        warnings: warnings.length > 0 ? warnings : undefined
      };

    } catch (error) {
      logger.error('Error in comprehensive bank account validation', {
        error: error instanceof Error ? error.message : 'Unknown error',
        bankAccountId,
        userId
      });

      return {
        valid: false,
        error: 'Failed to validate bank account due to system error.'
      };
    }
  }

  /**
   * Get Plaid account status
   */
  static async getPlaidAccountStatus(
    accessToken: string, 
    accountId: string
  ): Promise<PlaidAccountStatus> {
    try {
      const request = {
        access_token: accessToken,
        account_ids: [accountId]
      } as AccountsGetRequest;

      const response = await plaidClient.accountsGet(request);
      const account = response.data.accounts[0];

      if (!account) {
        return {
          active: false,
          available: false,
          error: 'Account not found in Plaid'
        };
      }

      // Check if account is active and available
      const isActive = account.subtype !== null && account.type !== null;
      const isAvailable = account.balances.available !== null || account.balances.current !== null;

      return {
        active: isActive,
        available: isAvailable,
        balance: account.balances.available || account.balances.current || undefined,
        lastUpdated: new Date()
      };

    } catch (error) {
      logger.error('Error getting Plaid account status', {
        error: error instanceof Error ? error.message : 'Unknown error',
        accountId
      });

      return {
        active: false,
        available: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get detailed Plaid account information
   */
  static async getPlaidAccountDetails(
    accessToken: string, 
    accountId: string
  ): Promise<AccountBase | undefined> {
    try {
      const request = {
        access_token: accessToken,
        account_ids: [accountId]
      } as AccountsGetRequest;

      const response = await plaidClient.accountsGet(request);
      return response.data.accounts[0];

    } catch (error) {
      logger.error('Error getting Plaid account details', {
        error: error instanceof Error ? error.message : 'Unknown error',
        accountId
      });
      return undefined;
    }
  }

  /**
   * Validate multiple bank accounts for a user
   */
  static async validateUserBankAccounts(userId: number): Promise<{
    validAccounts: any[];
    invalidAccounts: any[];
    warnings: string[];
  }> {
    try {
      // Get all bank accounts for user
      const bankAccounts = await executeQuerySingle(
        'SELECT id FROM tbl_bank_accounts WHERE user_id = ?',
        [userId]
      );

      if (!bankAccounts) {
        return {
          validAccounts: [],
          invalidAccounts: [],
          warnings: ['No bank accounts found for user']
        };
      }

      const validAccounts: any[] = [];
      const invalidAccounts: any[] = [];
      const allWarnings: string[] = [];

      // Validate each account
      for (const account of Array.isArray(bankAccounts) ? bankAccounts : [bankAccounts]) {
        const validation = await this.validateBankAccountForTransfers(account.id, userId);
        
        if (validation.valid) {
          validAccounts.push({
            ...validation.account,
            validationDetails: validation.validationDetails
          });
        } else {
          invalidAccounts.push({
            ...validation.account,
            error: validation.error,
            validationDetails: validation.validationDetails
          });
        }

        if (validation.warnings) {
          allWarnings.push(...validation.warnings.map(w => `Account ${account.id}: ${w}`));
        }
      }

      return {
        validAccounts,
        invalidAccounts,
        warnings: allWarnings
      };

    } catch (error) {
      logger.error('Error validating user bank accounts', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId
      });

      return {
        validAccounts: [],
        invalidAccounts: [],
        warnings: ['Failed to validate bank accounts due to system error']
      };
    }
  }

  /**
   * Check if a bank account is ready for immediate transfers
   */
  static async isAccountReadyForTransfers(
    bankAccountId: string, 
    userId: number
  ): Promise<{
    ready: boolean;
    canDeposit: boolean;
    canWithdraw: boolean;
    issues: string[];
  }> {
    const validation = await this.validateBankAccountForTransfers(bankAccountId, userId);

    const issues: string[] = [];
    
    if (!validation.valid) {
      issues.push(validation.error || 'Account validation failed');
    }

    if (validation.warnings) {
      issues.push(...validation.warnings);
    }

    const canDeposit = validation.validationDetails?.hasStripePaymentMethod || false;
    const canWithdraw = validation.validationDetails?.hasStripeBankAccount || false;

    return {
      ready: validation.valid && canDeposit && canWithdraw,
      canDeposit,
      canWithdraw,
      issues
    };
  }
}
