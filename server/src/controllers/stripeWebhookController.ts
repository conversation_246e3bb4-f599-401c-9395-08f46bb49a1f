import { Request, Response } from 'express';
import Stripe from 'stripe';
import { verifyStripeWebhookSignature } from '../utils/stripeErrorHandler';
import { processStripeWebhookEvent } from '../services/stripeWebhookService';
import { sendSuccess, sendError } from '../utils/response';
import logger from '../utils/logger';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-07-30.basil',
});

/**
 * Handle Stripe webhook events
 */
export const handleStripeWebhook = async (req: Request, res: Response): Promise<void> => {
  const signature = req.headers['stripe-signature'] as string;
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

  if (!webhookSecret) {
    logger.error('Stripe webhook secret not configured');
    sendError(res, 'Webhook configuration error', 500);
    return;
  }

  let event: Stripe.Event;

  try {
    // Verify webhook signature
    event = verifyStripeWebhookSignature(
      req.body,
      signature,
      webhookSecret
    );

    logger.info('Stripe webhook received', {
      eventId: event.id,
      eventType: event.type,
      created: event.created,
      livemode: event.livemode
    });

  } catch (error) {
    logger.error('Stripe webhook signature verification failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      signature: signature ? 'present' : 'missing'
    });
    sendError(res, 'Invalid webhook signature', 400);
    return;
  }

  try {
    // Process the webhook event
    await processStripeWebhookEvent(event);

    logger.info('Stripe webhook processed successfully', {
      eventId: event.id,
      eventType: event.type
    });

    sendSuccess(res, { received: true }, 'Webhook processed successfully');

  } catch (error) {
    logger.error('Error processing Stripe webhook', {
      eventId: event.id,
      eventType: event.type,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    // Still return 200 to prevent Stripe from retrying
    sendSuccess(res, { received: true, error: 'Processing failed' }, 'Webhook received but processing failed');
  }
};

/**
 * Test endpoint for Stripe webhook debugging
 */
export const testStripeWebhook = async (req: Request, res: Response): Promise<void> => {
  try {
    const webhookEvent = req.body;
    
    logger.info('🔍 STRIPE WEBHOOK DEBUG - Received test webhook', {
      headers: req.headers,
      body: webhookEvent,
      timestamp: new Date().toISOString()
    });

    // Log the complete event structure
    console.log('=== STRIPE WEBHOOK DEBUG EVENT ===');
    console.log('Headers:', JSON.stringify(req.headers, null, 2));
    console.log('Body:', JSON.stringify(webhookEvent, null, 2));
    console.log('===================================');

    sendSuccess(res, { 
      received: true, 
      debug: true,
      event: webhookEvent 
    }, 'Test Stripe webhook received and logged');

  } catch (error) {
    logger.error('Error in test Stripe webhook', { error });
    sendError(res, 'Test webhook error', 500);
  }
};

/**
 * Get Stripe webhook processing statistics
 */
export const getStripeWebhookStats = async (req: Request, res: Response): Promise<void> => {
  try {
    // This would typically fetch stats from a database or cache
    const stats = {
      totalProcessed: 0,
      successfullyProcessed: 0,
      failedProcessing: 0,
      lastProcessedAt: null,
      eventTypes: {}
    };

    logger.info('Stripe webhook stats requested', stats);
    sendSuccess(res, stats, 'Stripe webhook statistics retrieved');

  } catch (error) {
    logger.error('Error getting Stripe webhook stats', { error });
    sendError(res, 'Webhook stats error', 500);
  }
};
