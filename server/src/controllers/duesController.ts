import { Request, Response } from 'express';
import logger from '../utils/logger';
import { sendSuccess, sendError, sendUserError } from '../utils/response';
import {
  getMyDues,
  getAwaitingPayments,
  getTeamDetail,
  getGameSeasons,
  getDuesSummary,
  payDueWithWallet,
  PaymentRequest,
  getDuesDetails,
  getDuesTransactions
} from '../services/duesService';
import { verifyPin } from '../services/walletService';
import { executeSecondaryQuery } from '../utils/database';

/**
 * Get dues for the authenticated user
 * GET /api/dues/get-Dues
 */
export const getMyDuesController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendError(res, 'User ID is required', 400);
    }

    logger.info('Getting my dues', { userId });

    // Get query parameters for filtering
    const { status, search, limit, offset } = req.query;
    const limitNum = limit ? parseInt(limit as string) : 50;
    const offsetNum = offset ? parseInt(offset as string) : 0;
    let query = `
         SELECT 
          u.id,
          u.user_payconnect_id,
          u.firstname,
          u.email,
          u.status_id,
          u.enabled,
          CASE
        WHEN ptp.payment_status = 'paid' THEN 'Paid'
        WHEN sp.final_day_of_payment < CURDATE() THEN 'Overdue'
        ELSE 'Pending'
      END AS status,
          s.id AS season_id,
          s.season_name,
           s.end_date,
          g.game_title,
          us.match_id,
          CASE 
            WHEN pw.wage_unit_id = 1 THEN pw.amount
            WHEN pw.wage_unit_id IS NOT NULL THEN pw.amount * IFNULL(pw.unit_count, 0)
            ELSE NULL
          END AS amount
        FROM match_staff us
        JOIN users u ON u.id = us.user_id 
        JOIN user_roles ur ON ur.user_id = u.id AND ur.role_id = 5
        JOIN matches m ON m.id = us.match_id
        JOIN game_groups_matches ggm ON m.game_group_id = ggm.id
        JOIN seasons s ON s.id = ggm.season_id
        JOIN games g ON g.id = ggm.game_id
           JOIN season_plans sp ON sp.season_id = s.id
          JOIN paywages pw ON pw.user_id = u.id
          LEFT JOIN player_team_payment ptp ON ptp.player_id = u.id AND ptp.season_id = s.id
        WHERE g.user_id = ?
        GROUP BY
          u.id,
          u.firstname,
          u.email,
          u.status_id,
          u.enabled,
          s.id,
          s.season_name,
          g.game_title,
          us.match_id,
          pw.wage_unit_id,
          pw.amount,
          pw.unit_count,
          sp.final_day_of_payment,
          ptp.payment_status `;
const havingConditions: string[] = [];

if (status === 'Paid' || status === 'Pending' || status === 'Overdue') {
  havingConditions.push(`status = '${status}'`);
}
if (search) {
  const likeTerm = `%${search}%`;
  havingConditions.push(`(
    firstname LIKE '${likeTerm}' OR 
    email LIKE '${likeTerm}' 
  )`);
}



if (havingConditions.length > 0) {
  query += ` HAVING ` + havingConditions.join(' AND ');
}

    const rows = await executeSecondaryQuery(query, [userId]);

    // Format response to match frontend expectations
    const responseData = {
      data: rows,
      totalRecords: rows.length,
      page: 1,
      pageSize: rows.length
    };

    sendSuccess(res, responseData, 'My dues retrieved successfully');
  } catch (error) {
    logger.error('Error getting my dues', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve my dues', 500);
  }
};

/**
 * Get dues summary for dashboard
 * GET /api/dues/summary
 */
export const getDuesSummaryController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendUserError(res, 'AUTH', 'MISSING_USER_ID', 400);
    }

    logger.info('Getting dues summary', { userId });

    const summary = await getDuesSummary(userId);

    sendSuccess(res, summary, 'Dues summary retrieved successfully');
  } catch (error) {
    logger.error('Error getting dues summary', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve dues summary', 500);
  }
};

/**
 * Get awaiting payments for organization admin
 * POST /api/dues/awaiting-payments
 */
export const getAwaitingPaymentsController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendUserError(res, 'AUTH', 'MISSING_USER_ID', 400);
    }

    logger.info('Getting awaiting payments', { userId, filters: req.body });

    const { game_id, season_id, group_id, search, page = 1, pageSize = 10 } = req.body;

    const filters = {
      game_id,
      season_id,
      group_id,
      search,
      limit: pageSize,
      offset: (page - 1) * pageSize
    };

    const { data, totalRecords } = await getAwaitingPayments(userId, filters);

    sendSuccess(res, {
      data,
      totalRecords,
      page,
      pageSize
    }, 'Awaiting payments retrieved successfully');
  } catch (error) {
    logger.error('Error getting awaiting payments', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve awaiting payments', 500);
  }
};

/**
 * Get team details with payment information
 * GET /api/dues/team/:id
 */
export const getTeamDetailController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const teamId = req.params.id;
    if (!teamId) {
      return sendUserError(res, 'SYSTEM', 'MISSING_TEAM_ID', 400);
    }

    logger.info('Getting team details', { teamId });

    const teamDetails = await getTeamDetail(teamId);

    sendSuccess(res, teamDetails, 'Team details retrieved successfully');
  } catch (error) {
    logger.error('Error getting team details', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve team details', 500);
  }
};

/**
 * Get game seasons for filters
 * GET /api/dues/game-seasons
 */
export const getGameSeasonsController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendUserError(res, 'AUTH', 'MISSING_USER_ID', 400);
    }

    logger.info('Getting game seasons', { userId });

    const gameSeasons = await getGameSeasons(userId);

    sendSuccess(res, gameSeasons, 'Game seasons retrieved successfully');
  } catch (error) {
    logger.error('Error getting game seasons', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve game seasons', 500);
  }
};

/**
 * Pay due with wallet
 * POST /api/dues/pay
 */
export const payDueController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendUserError(res, 'AUTH', 'MISSING_USER_ID', 400);
    }

    const { dueId, amount, recipientUserId, pin, description } = req.body;

    if (!dueId || !amount || !recipientUserId || !pin) {
      return sendUserError(res, 'SYSTEM', 'MISSING_REQUIRED_FIELDS', 400);
    }

    logger.info('Processing due payment', { userId, dueId, amount });

    // Verify PIN first
    const pinVerification = await verifyPin(userId, pin);
    if (!pinVerification.success) {
      return sendUserError(res, 'PIN', 'INVALID_PIN', 400);
    }

    const paymentRequest: PaymentRequest = {
      dueId,
      amount,
      payerUserId: userId,
      recipientUserId,
      pin,
      description
    };

    const result = await payDueWithWallet(paymentRequest);

    if (result.success) {
      sendSuccess(res, result, 'Payment processed successfully');
    } else {
      sendUserError(res, 'MONEY', 'PAYMENT_FAILED', 400, result.message);
    }
  } catch (error) {
    logger.error('Error processing due payment', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to process payment', 500);
  }
};

/**
 * Pay multiple dues with wallet (bulk payment)
 * POST /api/dues/pay-bulk
 */
export const payBulkDuesController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendUserError(res, 'AUTH', 'MISSING_USER_ID', 400);
    }

    const { payments, pin } = req.body;

    if (!payments || !Array.isArray(payments) || payments.length === 0 || !pin) {
      return sendUserError(res, 'SYSTEM', 'MISSING_REQUIRED_FIELDS', 400);
    }

    logger.info('Processing bulk dues payment', { userId, paymentCount: payments.length });

    // Verify PIN first
    const pinVerification = await verifyPin(userId, pin);
    if (!pinVerification.success) {
      return sendUserError(res, 'PIN', 'INVALID_PIN', 400);
    }

    // Process each payment
    const results = [];
    let successCount = 0;
    let failCount = 0;
    let totalAmount = 0;

    for (const payment of payments) {
      const { dueId, amount, recipientUserId, description } = payment;

      if (!dueId || !amount || !recipientUserId) {
        results.push({
          dueId: payment.dueId || 'unknown',
          success: false,
          message: 'Missing required fields'
        });
        failCount++;
        continue;
      }

      totalAmount += amount;

      const paymentRequest: PaymentRequest = {
        dueId,
        amount,
        payerUserId: userId,
        recipientUserId,
        pin,
        description
      };

      try {
        const result = await payDueWithWallet(paymentRequest);

        results.push({
          dueId,
          success: result.success,
          message: result.message,
          transactionId: result.transactionId
        });

        if (result.success) {
          successCount++;
        } else {
          failCount++;
        }
      } catch (error) {
        results.push({
          dueId,
          success: false,
          message: 'Payment processing failed'
        });
        failCount++;
      }
    }

    sendSuccess(res, {
      success: successCount > 0,
      results,
      totalAmount,
      successfulPayments: successCount,
      failedPayments: failCount
    }, 'Bulk payment processing completed');
  } catch (error) {
    logger.error('Error processing bulk dues payment', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to process bulk payment', 500);
  }
};

/**
 * Get dues payer details following getStaffDetails pattern
 * GET /api/dues/:id/details
 */
export const getDuesDetailsController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const duesPayerId = req.params.id;
    if (!duesPayerId) {
      return sendError(res, 'Dues payer ID is required', 400);
    }

    logger.info('Getting dues payer details', { duesPayerId });

    const duesDetails = await getDuesDetails(parseInt(duesPayerId));

    if (!duesDetails) {
      return sendError(res, 'Dues payer details not found', 404);
    }

    sendSuccess(res, duesDetails, 'Dues payer details retrieved successfully');
  } catch (error) {
    logger.error('Error getting dues payer details', {
      error: error instanceof Error ? error.message : 'Unknown error',
      duesPayerId: req.params.id
    });
    sendError(res, 'Failed to retrieve dues payer details', 500);
  }
};

/**
 * Get dues payer's transaction history with pagination and filtering
 * GET /api/dues/:id/transactions
 */
export const getDuesTransactionsController = async (
  req: Request,
  res: Response
) => {
  try {
    const duesPayerId = req.params.id;
    const {
      limit = '20',
      offset = '0',
      search = '',
      type = '',
      status = '',
      dateFrom = '',
      dateTo = ''
    } = req.query;

    logger.info('Dues payer transactions request received', {
      duesPayerId,
      duesPayerIdType: typeof duesPayerId,
      allQueryParams: req.query,
      limit,
      offset,
      search,
      type,
      status,
      dateFrom,
      dateTo
    });

    if (!duesPayerId) {
      logger.error('Missing duesPayerId parameter');
      return sendError(res, 'duesPayerId parameter required', 400);
    }

    if (duesPayerId === 'undefined' || duesPayerId === 'null') {
      logger.error('Invalid duesPayerId parameter', { duesPayerId });
      return sendError(res, 'Invalid duesPayerId parameter', 400);
    }

    const result = await getDuesTransactions(parseInt(duesPayerId), {
      limit: parseInt(limit as string),
      offset: parseInt(offset as string),
      search: search as string,
      type: type as string,
      status: status as string,
      dateFrom: dateFrom as string,
      dateTo: dateTo as string
    });

    sendSuccess(res, {
      transactions: result.transactions,
      pagination: {
        total: result.total,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        hasMore: result.hasMore,
        currentPage: Math.floor(parseInt(offset as string) / parseInt(limit as string)) + 1,
        totalPages: Math.ceil(result.total / parseInt(limit as string))
      },
      filters: {
        search: search as string,
        type: type as string,
        status: status as string,
        dateFrom: dateFrom as string,
        dateTo: dateTo as string
      }
    }, 'Dues payer transactions retrieved successfully');

  } catch (error) {
    logger.error('Error fetching dues payer transactions', {
      error: error instanceof Error ? error.message : 'Unknown error',
      duesPayerId: req.params.id
    });
    sendError(res, 'Failed to retrieve dues payer transactions', 500);
  }
};
