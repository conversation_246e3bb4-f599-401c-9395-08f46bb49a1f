import { Request, Response } from 'express';
import { webhookEventQueue } from '../services/webhookEventQueue';
import logger from '../utils/logger';
import { sendSuccess, sendError } from '../utils/response';

/**
 * Test endpoint to debug webhook events
 * This helps you see exactly what data Plaid is sending
 */
export const testWebhook = async (req: Request, res: Response): Promise<void> => {
  try {
    const webhookEvent = req.body;
    
    logger.info('🔍 WEBHOOK DEBUG - Received test webhook', {
      headers: req.headers,
      body: webhookEvent,
      timestamp: new Date().toISOString()
    });

    // Log the complete event structure
    console.log('=== WEBHOOK DEBUG EVENT ===');
    console.log('Headers:', JSON.stringify(req.headers, null, 2));
    console.log('Body:', JSON.stringify(webhookEvent, null, 2));
    console.log('===========================');

    // Always respond with 200
    sendSuccess(res, { 
      received: true, 
      debug: true,
      event: webhookEvent 
    }, 'Test webhook received and logged');

  } catch (error) {
    logger.error('Error in test webhook', { error });
    sendError(res, 'Test webhook error', 500);
  }
};

/**
 * Get webhook queue statistics endpoint
 */
export const getWebhookQueueStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const stats = await webhookEventQueue.getQueueStats();
    
    logger.info('Webhook queue stats requested', stats);
    sendSuccess(res, stats, 'Webhook queue statistics retrieved');

  } catch (error) {
    logger.error('Error getting webhook queue stats', { error });
    sendError(res, 'Webhook queue stats error', 500);
  }
};

/**
 * Process webhook queue manually (for debugging)
 */
export const processWebhookQueue = async (req: Request, res: Response): Promise<void> => {
  try {
    logger.info('Manual webhook queue processing requested');
    
    await webhookEventQueue.processQueue();
    const stats = await webhookEventQueue.getQueueStats();
    
    logger.info('Manual webhook queue processing completed', stats);
    sendSuccess(res, stats, 'Webhook queue processed successfully');

  } catch (error) {
    logger.error('Error processing webhook queue manually', { error });
    sendError(res, 'Webhook queue processing error', 500);
  }
};
