
import { Request, Response } from 'express';
import { sendError, sendSuccess } from '../../utils/response';
import * as appBankServices from '../../services/appBankServices';
import logger from '../../utils/logger';

export async function listTransactionsHistory(req: Request & { userId?: string }, res: Response): Promise<void> {
  try {
    const userId = req.userId || (req.query.userId as string);
    const page = parseInt(req.query.page as string) || 1;
    const pageSize = parseInt(req.query.pageSize as string) || 10;

    if (!userId) {
      sendError(res, 'User not authenticated', 401);
      return;
    }

    const { data, totalRecords } = await appBankServices.getTransactionsHistory(userId, page, pageSize);
    const totalPages = Math.ceil(totalRecords / pageSize);

    sendSuccess(
      res,
      {
        transactions: data,
         totalPages,
        totalRecords
      },
      'Bank transaction history retrieved successfully',
      200
    );
  } catch (error) {
    logger.error('Error listing bank accounts', { error });
    sendError(res, 'Failed to list bank accounts', 500);
  }
}

export async function getTransacrionReportIssues(req:Request , res:Response) {
    try {

        const userId = req.userId || (req.query.userId as string);
        const issuesList = await appBankServices.getReportIssues()
        sendSuccess(res, issuesList, 'Report issues retrieved successfully', 200)

        
    }catch(error){
        logger.error('Error getting transaction report issues', { error });
        sendError(res, 'Failed to get transaction report issues', 500);

    }
    
}
export async function listTransactionDetail(req: Request & { userId?: string }, res: Response): Promise<void> {
  try {
    const userId = req.userId ;
    const transaction_id = req.body.transaction_id as string ;


    if (!userId) {
      sendError(res, 'User not authenticated', 401);
      return;
    }

    const { data } = await appBankServices.getTransactionsDetail(userId, transaction_id);
  

    sendSuccess(
      res,
    data,
      'Bank transaction detail retrieved successfully',
      200
    );
  } catch (error) {
    logger.error('Error listing bank accounts', { error });
    sendError(res, 'Failed to list bank accounts', 500);
  }
}