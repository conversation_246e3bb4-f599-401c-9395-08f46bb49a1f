import { createUserSession, updateSessionActivity, validateSession } from "../../services/sessionService";
import { withdrawMoneyToBank } from "../../services/walletService";
import logger from "../../utils/logger";
import { sendError, sendRateLimitExceeded, sendSuccess, sendUnauthorized, sendUserError, sendUserSuccess } from "../../utils/response";
import { formatAmount, getBalanceMessage } from "../../utils/userMessages";
import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';
import { addMoneyFromBank } from "../../services/appBankServices";
import { plaidClient } from "../../config/plaidConfig";
import { CountryCode, LinkTokenCreateRequest, Products } from "plaid";
import { executeQuerySingle, executeSecondaryQuery, executeUpdate } from "../../utils/database";
dotenv.config();

const requestCounts: { [ip: string]: number } = {};
const MAX_REQUESTS_PER_MINUTE = 100;
const applyRateLimit = (req: Request, res: Response): boolean => {
  const ip = req.ip || 'unknown';
  requestCounts[ip] = (requestCounts[ip] || 0) + 1;

  if (requestCounts[ip] > MAX_REQUESTS_PER_MINUTE) {
    logger.warn(`Rate limit exceeded for IP: ${ip}`);
    sendRateLimitExceeded(res);
    return false;
  }

  setTimeout(() => {
    requestCounts[ip]--;
  }, 60000);

  return true;
};

export const getAuthToken = async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers['authorization'];
    const inputToken = authHeader && authHeader.split(' ')[1];

    if (!inputToken) {
      return sendUnauthorized(res, 'Authorization token required');
    }

    const jwtSecret = process.env.GIU_SECRET;
    if (!jwtSecret) {
      logger.error('GIU JWT secret not configured');
      return sendError(res, 'Server configuration error', 500);
    }

    // Verify JWT token
    jwt.verify(inputToken, jwtSecret, async (err: any, user: any) => {
      if (err) {
        logger.warn('Invalid JWT token', { error: err.message });
        return sendUnauthorized(res, 'Invalid token');
      }

      if (typeof user !== 'object' || user === null || !('id' in user)) {
        logger.warn('Invalid token payload', { user });
        return sendUnauthorized(res, 'Invalid token payload');
      }

      // Extract user information from JWT
      const team_connect_user_id = (user as { id: string }).id;
      let payconnect_user_id = (user as { pay_connect_id?: number }).pay_connect_id;
  let userdetail = await executeQuerySingle(
        'SELECT * FROM tbl_users WHERE team_connect_user_id = ?',
        [team_connect_user_id]
      ) as any;

      console.log(userdetail,'userdetail', payconnect_user_id)
      if (!payconnect_user_id && !userdetail) {


        const userResult = await executeSecondaryQuery(`SELECT u.email,u.firstname, u.lastname, u.id,u.added_by,ur.role_id,u.contact_number,u.profile_pic from users u JOIN user_roles ur on u.id = ur.user_id where u.id=? `, [team_connect_user_id]);
        const userRows = (userResult as any).length ? (userResult as any) : [];
        const user = userRows[0];

        console.log(user, 'ppppppppp')
        
      const fullName = [user.firstname, user.lastname].filter(Boolean).join(' ');
      let is_organizer = user.role_id === 2 ? true : false;

        const insertResult = await executeUpdate(
          'INSERT INTO tbl_users (email, full_name, team_connect_user_id, is_organizer, master_parent_user_id, role_id,phone, profile_pic) VALUES (?, ?, ?, ?, ?, ?,?,?)',
          [
            user.email,
            fullName|| null,
            user.id,
            is_organizer,
            user.added_by,
            user.role_id,
            user.contact_number,
            user.profile_pic

          ]
        );

        executeSecondaryQuery(`UPDATE users SET user_payconnect_id=? where id=?`, [insertResult.insertId, team_connect_user_id])

        payconnect_user_id = insertResult.insertId as number;

             // Create wallet for new user
      const walletUniqueId = `wallet_${insertResult.insertId}_${Date.now()}`;
      const walletName = fullName || `Wallet ${user.id}`;
      const walletUsername = user.email.split('@')[0];

      await executeUpdate(
        `INSERT INTO tbl_masterwallet
         (wallet_unique_id, user_id, name, username, balance, status_id, created_at, last_updated)
         VALUES (?, ?, ?, ?, 0.00, 1, NOW(), NOW())`,
        [walletUniqueId, insertResult.insertId, walletName, walletUsername]
      );

      // Create parent relationship if specified
      if (user.parent_user_id) {
        await executeUpdate(
          'INSERT INTO tbl_user_parents (user_id, parent_user_id,role_id) VALUES (?, ?,?)',
          [userdetail.id, userdetail.parent_user_id , userdetail.role_id ]
        );
      }
        logger.warn('Missing payconnect user ID in token', { team_connect_user_id });
        // return sendUnauthorized(res, 'Invalid token payload - missing payconnect user ID');
      }
payconnect_user_id= userdetail.id
      const jwtSecretPayconnect = process.env.JWT_SECRET;
      if (!jwtSecretPayconnect) {
        logger.error('PayConnect JWT secret not configured');
        return sendError(res, 'Server configuration error', 500);
      }

      // Create new PayConnect token
      const newToken = jwt.sign(
        { id: payconnect_user_id, team_connect_user_id: team_connect_user_id },
        jwtSecretPayconnect,
        { expiresIn: '24h' }
      );

      // Validate session using the new token
      // const sessionValidation = await validateSession(newToken);

      // if (!sessionValidation.isValid || !sessionValidation.session) {
      //   logger.warn('Invalid session for new token', {
      //     userId: payconnect_user_id,
      //     error: sessionValidation.error
      //   });
      //   return sendUnauthorized(res, sessionValidation.error || 'Invalid session');
      // }

      // Verify that the session belongs to the user from the JWT
      // if (sessionValidation.session.user_id.toString() !== payconnect_user_id.toString()) {
      //   logger.warn('Session user mismatch', {
      //     jwtUserId: payconnect_user_id,
      //     sessionUserId: sessionValidation.session.user_id
      //   });
      //   return sendUnauthorized(res, 'Session user mismatch');
      // }

      // Update session activity (extend expiry) using the new token
      // await updateSessionActivity(newToken);
      const userAgent = req.headers['user-agent'] || 'Unknown';
      const ipAddress = req.ip || req.socket.remoteAddress || 'Unknown';

      const { sessionId, expiresAt } = await createUserSession(
        payconnect_user_id,
        newToken, // Use JWT token as session key
        userAgent,
        ipAddress
      );

      // Set request properties
      req.userId = payconnect_user_id;
      req.team_connect_user_id = team_connect_user_id;
      req.sessionToken = newToken; // The JWT token itself
      req.sessionId = sessionId;

      logger.info('Authentication successful', {
        userId: payconnect_user_id,
        sessionId: sessionId,
        expiresAt: expiresAt,
        parentUserId: team_connect_user_id
      });

      

  // Fetch wallet info to determine if PIN is setup
const walletResult = await executeQuerySingle(
  'SELECT wallet_master_pin FROM tbl_masterwallet WHERE user_id = ?',
  [payconnect_user_id]
);
console.log(payconnect_user_id,'payconnect_user_id')
const isPinSetup = walletResult && walletResult.wallet_master_pin ? true : false;

return sendSuccess(res, {
  token: newToken,
  isPinSetup
}, 'Authentication successful');

    });

  } catch (error) {
    logger.error('Error getting auth token', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to get auth token', 500);
  }
};

export const createLinkToken = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId; // Get userId from authenticated token
    if (!applyRateLimit(req, res)) return;

    // const { client_name, country_codes, language, products, user } = req.body;

    // // Validate required fields
    // if (!client_name || !country_codes || !user?.client_user_id) {
    //   return sendError(res, 'Missing required fields', 400);
    // }

    const request: LinkTokenCreateRequest = {

      client_name: 'PayConnect',
      country_codes: [CountryCode.Us],
      language: 'en',
      products: [Products.Transactions, Products.Auth, Products.Transfer],
      user: {
        // client_user_id: 'user-' + Date.now() // In production, use actual user ID

        client_user_id: userId?.toString()
      }
    };

    const response = await plaidClient.linkTokenCreate(request);
    const linkToken = response.data;

    logger.info('Link token created successfully', { client_user_id: userId });

    res.json({
      link_token: linkToken.link_token,
      expiration: linkToken.expiration,
      request_id: linkToken.request_id
    });

  } catch (err) {
    logger.error('Create link token error:', err);
    sendError(res, 'Failed to create link token', 500);
  }
};


export const addMoney = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId; // Get userId from authenticated token
    const { amount , pin , paymentMethod, bankAccountId } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return sendUserError(res, 'MONEY', 'INVALID_AMOUNT', 400);
    }

    if (!pin || typeof pin !== 'string') {
      return sendUserError(res, 'PIN', 'VERIFIED', 400);
    }

    if (amount < 10) {
      return sendError(res, 'Minimum amount is $10.00 to add money to your wallet', 400);
    }

    if (amount > 10000) {
      return sendError(res, 'Maximum amount is $10,000.00 per transaction for your security', 400);
    }

    // Use the new service function that handles PIN verification and bank transfer
    const result = await addMoneyFromBank(parseInt(userId), amount, pin, bankAccountId);
    console.log(result, "result in addMoney");
    if (result.success) {
      const newBalance = result.newBalance || 0;
      sendUserSuccess(res, 'MONEY', 'ADDED', {
        success: true,
        newBalance,
        formattedNewBalance: formatAmount(newBalance),
        amount,
        formattedAmount: formatAmount(amount),
        transactionId: result.transactionId,
        message: result.message,
        balanceMessage: getBalanceMessage(newBalance)
      });
    } else {
      sendUserError(res, 'MONEY', 'ADDED', 400, result.message);
    }

  } catch (error) {
    logger.error('Error adding money to wallet', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to add money to wallet', 500);
  }
};

/**
 * Withdraw money from wallet to primary bank account with PIN verification
 */
export const withdrawMoney = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { amount , pin , bankAccountId , paymentMethod } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return sendError(res, 'Valid amount is required', 400);
    }

    if (!pin || typeof pin !== 'string') {
      return sendError(res, 'Wallet PIN is required', 400);
    }

    if (amount < 10) {
      return sendError(res, 'Minimum withdrawal amount is $10', 400);
    }

    if (amount > 5000) {
      return sendError(res, 'Maximum withdrawal amount is $5,000', 400);
    }

    // Use the new service function that handles PIN verification and bank transfer
    const result = await withdrawMoneyToBank(parseInt(userId), amount, pin, bankAccountId, paymentMethod);

    if (result.success) {
      sendSuccess(res, {
        success: true,
        newBalance: result.newBalance,
        amount,
        transactionId: result.transactionId,
        estimatedArrival: '1-3 business days',
        message: result.message
      }, 'Withdrawal initiated successfully');
    } else {
      sendError(res, result.message || 'Failed to withdraw money', 400);
    }

  } catch (error) {
    logger.error('Error withdrawing money from wallet', {
      error: error instanceof Error ? error.message : 'Unknown error',
      errorStack: error instanceof Error ? error.stack : undefined,
      errorName: error instanceof Error ? error.name : undefined,
      userId: req.userId,
      requestBody: req.body,
      sqlState: (error as any)?.sqlState,
      sqlMessage: (error as any)?.sqlMessage,
      errno: (error as any)?.errno,
      code: (error as any)?.code
    });
    sendError(res, 'Failed to withdraw money from wallet', 500);
  }
};

export async function getPlaidKeys(req: Request & { userId?: string }, res: Response): Promise<void> {
  try {
    const userId = req.userId as string;
    if (!userId) {
      sendError(res, 'User not authenticated', 401);
      return;
    }

    const plaidKeys = {
      PLAID_BUSINESS_ACCESS_TOKEN: process.env.PLAID_BUSINESS_ACCESS_TOKEN,
      PLAID_BUSINESS_ACCOUNT_ID: process.env.PLAID_BUSINESS_ACCOUNT_ID,
      PLAID_BUSINESS_BANK_NAME: process.env.PLAID_BUSINESS_BANK_NAME,
      PLAID_BUSINESS_ACCOUNT_MASK: process.env.PLAID_BUSINESS_ACCOUNT_MASK,
      PLAID_CLIENT_ID: process.env.PLAID_CLIENT_ID,
      PLAID_SECRET: process.env.PLAID_SECRET,
      PLAID_ENV: process.env.PLAID_ENV,
    };

    sendSuccess(res, plaidKeys, 'Plaid keys fetched successfully', 200);
  } catch (error) {
    sendError(res, 'Failed to fetch Plaid keys', 500);
  }
}