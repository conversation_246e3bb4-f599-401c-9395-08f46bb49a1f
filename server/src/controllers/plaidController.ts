import { Request, Response } from 'express';
import {
  LinkTokenCreateRequest,
  AccountsGetRequest,
  ItemPublicTokenExchangeRequest,
  CountryCode,
  Products
} from 'plaid';
import { plaidClient } from '../config/plaidConfig';
import logger from '../utils/logger';
import { sendSuccess, sendError, sendRateLimitExceeded, sendUnauthorized } from '../utils/response';
import { executeQuery, executeUpdate, executeQuerySingle } from '../utils/database';
import { PlaidStripeIntegrationService } from '../services/plaidStripeIntegrationService';

// Simple in-memory rate limiter
const requestCounts: { [ip: string]: number } = {};
const MAX_REQUESTS_PER_MINUTE = 100;

// Rate limiting helper
const applyRateLimit = (req: Request, res: Response): boolean => {
  const ip = req.ip || 'unknown';
  requestCounts[ip] = (requestCounts[ip] || 0) + 1;

  if (requestCounts[ip] > MAX_REQUESTS_PER_MINUTE) {
    logger.warn(`Rate limit exceeded for IP: ${ip}`);
    sendRateLimitExceeded(res);
    return false;
  }

  setTimeout(() => {
    requestCounts[ip]--;
  }, 60000);

  return true;
};

// Interface for stored account data (using existing tbl_bank_accounts structure)
interface StoredAccount {
  id: string;
  user_id: string;
  plaid_account_id: string;
  account_mask: string;
  bank_name: string;
  account_type: string;
  routing_number: string;
  account_number_last4: string;
  is_primary: boolean;
  created_at: string;
  plaid_access_token?: string; // We'll add this to store access token per account
  plaid_item_id?: string; // We'll add this to store item ID per account
  stripe_bank_account_id?: string;
}

// Interface for master wallet (using existing tbl_masterwallet structure)
interface MasterWallet {
  id: string;
  user_id: string;
  plaid_access_token: string;
  wallet_master_pin: string;
  balance: number;
  status_id: number;
  create_at: string;
  last_updated: string;
}

// 1. Create Link Token
export const createLinkToken = async (req: Request, res: Response) => {
  try {
    if (!applyRateLimit(req, res)) return;

    const { client_name, country_codes, language, products, user } = req.body;

    // Validate required fields
    if (!client_name || !country_codes || !user?.client_user_id) {
      return sendError(res, 'Missing required fields', 400);
    }

    const request: LinkTokenCreateRequest = {
      client_name: client_name || 'PayConnect',
      country_codes: country_codes || [CountryCode.Us],
      language: language || 'en',
      products: products || [Products.Transactions, Products.Auth], // Auth for bank verification, Transactions for history
      user: {
        client_user_id: user.client_user_id
      }
    };

    const response = await plaidClient.linkTokenCreate(request);
    const linkToken = response.data;

    logger.info('Link token created successfully', { client_user_id: user.client_user_id });

    res.json({
      link_token: linkToken.link_token,
      expiration: linkToken.expiration,
      request_id: linkToken.request_id
    });

  } catch (err) {
    logger.error('Create link token error:', err);
    sendError(res, 'Failed to create link token', 500);
  }
};

// 2. Exchange Public Token
export const exchangePublicToken = async (req: Request, res: Response) => {
  try {
    if (!applyRateLimit(req, res)) return;

    const { public_token, accounts, institution, link_session_id } = req.body;
    const userId = (req as any).userId; // Assuming user ID comes from auth middleware

    if (!public_token) {
      return sendError(res, 'Missing public_token', 400);
    }

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    // Exchange public token for access token
    const exchangeRequest: ItemPublicTokenExchangeRequest = {
      public_token
    };

    const exchangeResponse = await plaidClient.itemPublicTokenExchange(exchangeRequest);
    const { access_token, item_id } = exchangeResponse.data;

    // Get account details from Plaid
    const accountsRequest: AccountsGetRequest = {
      access_token
    };

    const accountsResponse = await plaidClient.accountsGet(accountsRequest);
    const plaidAccounts = accountsResponse.data.accounts;
    const institutionData = accountsResponse.data.item;

    // Store accounts in existing tbl_bank_accounts table with access tokens
    for (const account of plaidAccounts) {
      // Check if this account already exists
      const existingAccount = await executeQuerySingle(
        'SELECT id FROM tbl_bank_accounts WHERE user_id = ? AND plaid_account_id = ?',
        [userId, account.account_id]
      );

      if (existingAccount) {
        // Update existing account with new access token
        await executeUpdate(
          `UPDATE tbl_bank_accounts
           SET plaid_access_token = ?, plaid_item_id = ?
           WHERE user_id = ? AND plaid_account_id = ?`,
          [access_token, item_id, userId, account.account_id]
        );
        logger.info('Updated existing account with new access token', {
          userId,
          accountId: account.account_id
        });
      } else {
        // Check if this is the first account for this user (make it primary)
        const existingAccounts = await executeQuery(
          'SELECT COUNT(*) as count FROM tbl_bank_accounts WHERE user_id = ?',
          [userId]
        );
        const isPrimary = existingAccounts[0].count === 0;

        // Insert new account into tbl_bank_accounts
        await executeUpdate(
          `INSERT INTO tbl_bank_accounts
           (user_id, plaid_account_id, account_mask, bank_name, account_type, routing_number, account_number_last4, is_primary, plaid_access_token, plaid_item_id, stripe_bank_account_id)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            userId,
            account.account_id,
            account.mask || '****',
            institution?.name || 'Unknown Bank',
            `${account.type}_${account.subtype}`,
            '', // We'll get routing number from auth endpoint if needed
            account.mask || '****',
            isPrimary,
            access_token, // Store access token with each account
            item_id,
            null
          ]
        );
        logger.info('Added new account with access token', {
          userId,
          accountId: account.account_id,
          bankName: institution?.name
        });
      }
    }

    // After successfully storing bank accounts, create Stripe bank accounts and payment methods
    try {
      logger.info('Creating Stripe bank accounts and payment methods for linked accounts', { userId });

      // Import the services here to avoid circular dependencies
      const { StripeCustomerService } = await import('../services/stripeCustomerService');
      const { PlaidStripeIntegrationService } = await import('../services/plaidStripeIntegrationService');

      // Get or create Stripe customer for the user
      const customerResult = await StripeCustomerService.getOrCreateStripeCustomer(userId);
      
      if (!customerResult.success || !customerResult.customer) {
        logger.error('Failed to get or create Stripe customer during Plaid integration', {
          userId,
          error: customerResult.error
        });
        // Don't fail the entire process, just log the error
        throw new Error(`Failed to create Stripe customer: ${customerResult.error}`);
      }

      const stripeCustomerId = customerResult.customer.id;

      // Create Stripe bank accounts using Plaid processor tokens for all newly linked accounts
      const newlyLinkedAccounts = await executeQuery(
        'SELECT id, plaid_account_id, bank_name, account_mask FROM tbl_bank_accounts WHERE user_id = ? AND plaid_item_id = ?',
        [userId, item_id]
      );

      const accountArray = Array.isArray(newlyLinkedAccounts) ? newlyLinkedAccounts : [newlyLinkedAccounts];

      for (const account of accountArray) {
        if (account) {
          try {
            // Create Stripe payment method using Plaid processor token
            const integrationResult = await PlaidStripeIntegrationService.createStripePaymentMethodFromPlaid(
              userId,
              account.plaid_account_id,
              access_token
            );

            console.log('Integration result:', integrationResult);

            if (integrationResult.success && integrationResult.stripePaymentMethodId) {
              // Update the bank account with the Stripe bank account ID
              await executeUpdate(
                'UPDATE tbl_bank_accounts SET stripe_bank_account_id = ? WHERE id = ?',
                [integrationResult.stripePaymentMethodId, account.id]
              );

              logger.info('Created Stripe bank account for Plaid account', {
                userId,
                bankAccountId: account.id,
                plaidAccountId: account.plaid_account_id,
                stripePaymentMethodId: integrationResult.stripePaymentMethodId
              });
            } else {
              logger.warn('Failed to create Stripe bank account', {
                userId,
                bankAccountId: account.id,
                plaidAccountId: account.plaid_account_id,
                error: integrationResult.error
              });
            }
          } catch (error) {
            logger.error('Error creating Stripe bank account for Plaid account', {
              userId,
              bankAccountId: account.id,
              plaidAccountId: account.plaid_account_id,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }
      }

      logger.info('Stripe integration completed for Plaid accounts', {
        userId,
        accountsProcessed: accountArray.length
      });

    } catch (error) {
      // Don't fail the entire process if Stripe integration fails
      logger.error('Error during Stripe integration for Plaid accounts', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    logger.info('Public token exchanged successfully', { userId, item_id });

    res.json({
      access_token: access_token,
      item_id: item_id,
      request_id: exchangeResponse.data.request_id
    });

  } catch (err) {
    logger.error('Exchange public token error:', err);
    sendError(res, 'Failed to exchange public token', 500);
  }
};

// 3. Get Connected Accounts
export const getAccounts = async (req: Request, res: Response) => {
  try {
    if (!applyRateLimit(req, res)) return;

    const userId = (req as any).userId;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    // Get stored accounts from existing tbl_bank_accounts table
    const storedAccounts = await executeQuery<StoredAccount>(
      'SELECT * FROM tbl_bank_accounts WHERE user_id = ? ORDER BY is_primary DESC, created_at ASC',
      [userId]
    );

    if (storedAccounts.length === 0) {
      logger.info(`No bank accounts found for user ${userId}`);
      return res.json([]);
    }

    const accountsWithBalances = [];

    // Get current balances from Plaid for each account using its own access token
    for (const storedAccount of storedAccounts) {
      try {
        // Skip accounts without access tokens
        if (!storedAccount.plaid_access_token) {
          logger.warn(`No access token for account ${storedAccount.plaid_account_id}`);
          // Include account without balance if no access token
          accountsWithBalances.push({
            id: storedAccount.id,
            account_id: storedAccount.plaid_account_id,
            name: storedAccount.bank_name + ' Account',
            official_name: storedAccount.bank_name + ' Account',
            mask: storedAccount.account_mask,
            type: storedAccount.account_type.split('_')[0] || 'depository',
            subtype: storedAccount.account_type.split('_')[1] || 'checking',
            institution_name: storedAccount.bank_name,
            balance: {
              available: null,
              current: null,
              currency: 'USD'
            },
            is_primary: storedAccount.is_primary,
            connected_at: storedAccount.created_at,
            stripe_bank_account_id: storedAccount.stripe_bank_account_id,
            has_stripe_integration: !!storedAccount.stripe_bank_account_id
          });
          continue;
        }

        const accountsRequest: AccountsGetRequest = {
          access_token: storedAccount.plaid_access_token
        };

        const accountsResponse = await plaidClient.accountsGet(accountsRequest);
        const plaidAccount = accountsResponse.data.accounts.find(
          acc => acc.account_id === storedAccount.plaid_account_id
        );

        if (plaidAccount) {
          accountsWithBalances.push({
            id: storedAccount.id,
            account_id: storedAccount.plaid_account_id,
            name: plaidAccount.name,
            official_name: plaidAccount.official_name || plaidAccount.name,
            mask: storedAccount.account_mask,
            type: plaidAccount.type,
            subtype: plaidAccount.subtype,
            institution_name: storedAccount.bank_name,
            balance: {
              available: plaidAccount.balances.available,
              current: plaidAccount.balances.current,
              currency: plaidAccount.balances.iso_currency_code || 'USD'
            },
            is_primary: storedAccount.is_primary,
            connected_at: storedAccount.created_at,
            stripe_bank_account_id: storedAccount.stripe_bank_account_id,
            has_stripe_integration: !!storedAccount.stripe_bank_account_id
          });
        }
      } catch (accountError) {
        logger.error(`Error fetching balance for account ${storedAccount.plaid_account_id}:`, accountError);
        // Include account without balance if Plaid call fails
        accountsWithBalances.push({
          id: storedAccount.id,
          account_id: storedAccount.plaid_account_id,
          name: storedAccount.bank_name + ' Account',
          official_name: storedAccount.bank_name + ' Account',
          mask: storedAccount.account_mask,
          type: storedAccount.account_type.split('_')[0] || 'depository',
          subtype: storedAccount.account_type.split('_')[1] || 'checking',
          institution_name: storedAccount.bank_name,
          balance: {
            available: null,
            current: null,
            currency: 'USD'
          },
          is_primary: storedAccount.is_primary,
          connected_at: storedAccount.created_at,
          stripe_bank_account_id: storedAccount.stripe_bank_account_id,
          has_stripe_integration: !!storedAccount.stripe_bank_account_id
        });
      }
    }

    logger.info('Accounts retrieved successfully', { userId, count: accountsWithBalances.length });
    res.json(accountsWithBalances);

  } catch (err) {
    logger.error('Get accounts error:', err);
    sendError(res, 'Failed to retrieve accounts', 500);
  }
};

// 4. Set Primary Account
export const setPrimaryAccount = async (req: Request, res: Response) => {
  try {
    if (!applyRateLimit(req, res)) return;

    const { accountId } = req.params;
    const userId = (req as any).userId;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    if (!accountId) {
      return sendError(res, 'Account ID is required', 400);
    }

    // Verify account belongs to user
    const account = await executeQuerySingle(
      'SELECT id FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
      [accountId, userId]
    );

    if (!account) {
      return sendError(res, 'Account not found', 404);
    }

    // Update all accounts to not primary, then set the specified one as primary
    await executeUpdate(
      'UPDATE tbl_bank_accounts SET is_primary = FALSE WHERE user_id = ?',
      [userId]
    );

    await executeUpdate(
      'UPDATE tbl_bank_accounts SET is_primary = TRUE WHERE id = ? AND user_id = ?',
      [accountId, userId]
    );

    logger.info('Primary account updated successfully', { userId, accountId });
    res.json({ success: true });

  } catch (err) {
    logger.error('Set primary account error:', err);
    sendError(res, 'Failed to set primary account', 500);
  }
};

// 5. Disconnect Account
export const disconnectAccount = async (req: Request, res: Response) => {
  try {
    if (!applyRateLimit(req, res)) return;

    const { accountId } = req.params;
    const userId = (req as any).userId;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    if (!accountId) {
      return sendError(res, 'Account ID is required', 400);
    }

    // Verify account belongs to user and get account details
    const account = await executeQuerySingle<StoredAccount>(
      'SELECT * FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
      [accountId, userId]
    );

    if (!account) {
      return sendError(res, 'Account not found', 404);
    }

    // Remove account from database (access token is stored with the account)
    await executeUpdate(
      'DELETE FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
      [accountId, userId]
    );

    // If this was the primary account, set another account as primary
    if (account.is_primary) {
      const remainingAccounts = await executeQuery(
        'SELECT id FROM tbl_bank_accounts WHERE user_id = ? ORDER BY created_at ASC LIMIT 1',
        [userId]
      );

      if (remainingAccounts.length > 0) {
        await executeUpdate(
          'UPDATE tbl_bank_accounts SET is_primary = TRUE WHERE id = ?',
          [remainingAccounts[0].id]
        );
      }
    }

    logger.info('Account disconnected successfully', { userId, accountId });
    res.json({ success: true });

  } catch (err) {
    logger.error('Disconnect account error:', err);
    sendError(res, 'Failed to disconnect account', 500);
  }
};

/**
 * Create Stripe payment method from Plaid account using processor token
 */
export const createStripePaymentMethodFromPlaid = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { plaidAccountId, accessToken } = req.body;

    if (!plaidAccountId || !accessToken) {
      return sendError(res, 'Plaid account ID and access token are required', 400);
    }

    logger.info('Creating Stripe payment method from Plaid account', {
      userId,
      plaidAccountId
    });

    const result = await PlaidStripeIntegrationService.createStripePaymentMethodFromPlaid(
      parseInt(userId),
      plaidAccountId,
      accessToken
    );

    if (result.success) {
      sendSuccess(res, {
        stripeCustomerId: result.stripeCustomerId,
        stripePaymentMethodId: result.stripePaymentMethodId,
        processorToken: result.processorToken,
        bankAccountId: result.bankAccountId
      }, 'Stripe payment method created successfully from Plaid account');
    } else {
      sendError(res, result.error || 'Failed to create Stripe payment method', 400);
    }

  } catch (error) {
    logger.error('Error creating Stripe payment method from Plaid', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to create Stripe payment method from Plaid account', 500);
  }
};

/**
 * Get payment method status for Plaid account
 */
export const getPlaidStripePaymentMethodStatus = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { plaidAccountId } = req.params;

    if (!plaidAccountId) {
      return sendError(res, 'Plaid account ID is required', 400);
    }

    const status = await PlaidStripeIntegrationService.getPaymentMethodStatus(
      parseInt(userId),
      plaidAccountId
    );

    sendSuccess(res, {
      plaidAccountId,
      status
    }, 'Payment method status retrieved successfully');

  } catch (error) {
    logger.error('Error getting payment method status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      plaidAccountId: req.params.plaidAccountId
    });
    sendError(res, 'Failed to get payment method status', 500);
  }
};

/**
 * Debug endpoint to check bank accounts without authentication (for testing only)
 */
export const debugGetBankAccounts = async (req: Request, res: Response) => {
  try {
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'userId query parameter is required',
        example: '/debug/bank-accounts?userId=3'
      });
    }

    logger.info('Debug: Getting bank accounts for user', { userId });

    // Get stored accounts from existing tbl_bank_accounts table
    const storedAccounts = await executeQuery(
      'SELECT * FROM tbl_bank_accounts WHERE user_id = ? ORDER BY is_primary DESC, created_at ASC',
      [userId]
    );

    if (storedAccounts.length === 0) {
      return res.json({
        success: true,
        message: `No bank accounts found for user ${userId}`,
        data: []
      });
    }

    const accountsArray = Array.isArray(storedAccounts) ? storedAccounts : [storedAccounts];

    const formattedAccounts = accountsArray.map(account => ({
      id: account.id,
      account_id: account.plaid_account_id,
      name: account.bank_name + ' Account',
      official_name: account.bank_name + ' Account',
      mask: account.account_mask,
      type: account.account_type?.split('_')[0] || 'depository',
      subtype: account.account_type?.split('_')[1] || 'checking',
      institution_name: account.bank_name,
      balance: {
        available: null,
        current: null,
        currency: 'USD'
      },
      is_primary: account.is_primary,
      connected_at: account.created_at,
      stripe_bank_account_id: account.stripe_bank_account_id,
      has_stripe_integration: !!account.stripe_bank_account_id
    }));

    res.json({
      success: true,
      message: `Found ${formattedAccounts.length} bank accounts for user ${userId}`,
      data: formattedAccounts,
      debug_info: {
        userId,
        accountCount: formattedAccounts.length,
        stripeIntegrationCount: formattedAccounts.filter(acc => acc.has_stripe_integration).length
      }
    });

  } catch (error) {
    logger.error('Debug: Error getting bank accounts', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.query.userId
    });
    res.status(500).json({
      success: false,
      message: 'Failed to get bank accounts',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Legacy controller for backward compatibility
export const plaidController = exchangePublicToken;
