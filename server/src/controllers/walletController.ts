import { Request, Response } from 'express';
import {
  checkPinSetupRequired,
  getUserMasterWallet,
  createMasterWallet,
  setWalletMasterPin,
  verifyWalletMasterPin,
  updateWalletBalance,
  getWalletBalance,
  getWalletStats,
  getWalletTransactions,
  getPrimaryBankAccount,
  transferWalletToWallet,
  searchTransferRecipients
} from '../services/walletService';
import { StripeWalletService } from '../services/stripeWalletService';
import { TransferHistoryService } from '../services/transferHistoryService';
import { TransferRecoveryService } from '../services/transferRecoveryService';
import { TransferTestingService } from '../services/transferTestingService';
import { WithdrawalProgressFallback as WithdrawalProgressService } from '../services/withdrawalProgressFallback';
import { StripePaymentMethodService } from '../services/stripePaymentMethodService';
import { BankStripeIntegrationService } from '../services/bankStripeIntegrationService';
import { executeQuery, executeQuerySingle } from '../utils/database';


import { sendSuccess, sendError, sendUnauthorized, sendUserSuccess, sendUserError } from '../utils/response';
import { formatAmount, getBalanceMessage } from '../utils/userMessages';
import { hashPin, comparePin } from '../models/user';
import {
  generateAndSaveOTP,
  generateAndSendOTP,
  verifyOTPCode,
  markOTPAsUsed,
  invalidateUserOTPs,
  OTP_CONFIG
} from '../services/otpService';


import logger from '../utils/logger';

/**
 * Get user's wallet information
 */
export const getWalletInfo = async (req: Request & { userId?: string; parent_user_id?: string; sessionToken?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const wallet = await getUserMasterWallet(parseInt(userId));
    const requiresPinSetup = await checkPinSetupRequired(parseInt(userId));
    const stats = await getWalletStats(parseInt(userId));

    if (!wallet) {
      return sendSuccess(res, {
        hasWallet: false,
        requiresPinSetup: true,
        stats
      }, 'No wallet found for user');
    }

    sendSuccess(res, {
      hasWallet: true,
      requiresPinSetup,
      wallet: {
        id: wallet.id,
        walletUniqueId: wallet.wallet_unique_id,
        name: wallet.name,
        username: wallet.username,
        balance: wallet.balance,
        statusId: wallet.status_id,
        createdAt: wallet.created_at,
        lastUpdated: wallet.last_updated
      },
      stats
    }, 'Wallet information retrieved successfully');

  } catch (error) {
    logger.error('Error getting wallet info', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve wallet information', 500);
  }
};

/**
 * Create a new master wallet for user with PIN
 */
export const createWallet = async (req: Request & { userId?: string; parent_user_id?: string; sessionToken?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { pin } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!pin || typeof pin !== 'string') {
      return sendError(res, 'Master PIN is required', 400);
    }

    // Validate PIN format (should be 4-6 digits)
    if (!/^\d{4,6}$/.test(pin)) {
      return sendError(res, 'PIN must be 4-6 digits', 400);
    }

    // Check if user already has a wallet
    const existingWallet = await getUserMasterWallet(parseInt(userId));
    if (existingWallet) {
      return sendError(res, 'User already has a master wallet', 409);
    }

    // Hash the PIN
    const hashedPin = await hashPin(pin);

    // Create wallet with PIN
    const wallet = await createMasterWallet(parseInt(userId), hashedPin);

    if (!wallet) {
      return sendUserError(res, 'WALLET', 'CREATED', 500);
    }

    sendUserSuccess(res, 'WALLET', 'CREATED', {
      wallet: {
        id: wallet.id,
        walletUniqueId: wallet.wallet_unique_id,
        name: wallet.name,
        username: wallet.username,
        balance: wallet.balance,
        formattedBalance: formatAmount(wallet.balance),
        balanceMessage: getBalanceMessage(wallet.balance),
        statusId: wallet.status_id,
        createdAt: wallet.created_at,
        lastUpdated: wallet.last_updated
      },
      requiresPinSetup: false // PIN is already set during creation
    });

  } catch (error) {
    logger.error('Error creating wallet', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to create wallet', 500);
  }
};

/**
 * Send OTP for PIN change verification
 */
export const sendPinChangeOTP = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Get user details
    const user = await executeQuerySingle(
      'SELECT * FROM tbl_users WHERE id = ?',
      [userId]
    );

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    // Check if user has a wallet
    const wallet = await getUserMasterWallet(parseInt(userId));
    if (!wallet || !wallet.wallet_master_pin) {
      return sendError(res, 'No wallet or PIN found', 404);
    }

    // Invalidate all previous unused transaction OTPs
    await invalidateUserOTPs(user.id, 'transaction');

    // Generate, save and send new OTP for transaction (PIN change)
    const { otpCode, otpId, emailSent } = await generateAndSendOTP(user.id, user.email, 'transaction');

    logger.info('PIN change OTP sent', {
      userId: user.id,
      email: user.email,
      otpId,
      emailSent,
      expiresInMinutes: OTP_CONFIG.EXPIRY_MINUTES
    });

    sendUserSuccess(res, 'PIN', 'CHANGE_OTP_SENT', {
      message: 'Security code sent for PIN change',
      email: user.email,
      emailSent,
      otp: process.env.NODE_ENV === 'development' ? otpCode : undefined,
      expiresInMinutes: OTP_CONFIG.EXPIRY_MINUTES
    });

  } catch (error) {
    logger.error('Error sending PIN change OTP', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to send OTP', 500);
  }
};

/**
 * Send OTP for PIN reset (forgot PIN)
 */
export const sendPinResetOTP = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Get user details
    const user = await executeQuerySingle(
      'SELECT * FROM tbl_users WHERE id = ?',
      [userId]
    );

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    // Check if user has a wallet
    const wallet = await getUserMasterWallet(parseInt(userId));
    if (!wallet) {
      return sendError(res, 'No wallet found', 404);
    }

    // Invalidate all previous unused PIN reset OTPs (using 'password_reset' type)
    await invalidateUserOTPs(user.id, 'password_reset');

    // Generate, save and send new OTP for PIN reset (using 'password_reset' type as it's similar)
    const { otpCode, otpId, emailSent } = await generateAndSendOTP(user.id, user.email, 'password_reset');

    logger.info('PIN reset OTP sent', {
      userId: user.id,
      email: user.email,
      otpId,
      emailSent,
      expiresInMinutes: OTP_CONFIG.EXPIRY_MINUTES
    });

    sendUserSuccess(res, 'PIN', 'RESET_OTP_SENT', {
      message: 'Security code sent for PIN reset',
      email: user.email,
      emailSent,
      otp: process.env.NODE_ENV === 'development' ? otpCode : undefined,
      expiresInMinutes: OTP_CONFIG.EXPIRY_MINUTES
    });

  } catch (error) {
    logger.error('Error sending PIN reset OTP', { error, userId: req.userId });
    sendError(res, 'Failed to send PIN reset code', 500);
  }
};

/**
 * Reset wallet PIN with OTP verification (forgot PIN)
 */
export const resetWalletPin = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { newPin, otpCode } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!newPin || !otpCode) {
      return sendError(res, 'New PIN and OTP code are required', 400);
    }

    // Validate new PIN format (should be 4-6 digits)
    if (!/^\d{4,6}$/.test(newPin)) {
      return sendError(res, 'New PIN must be 4-6 digits', 400);
    }

    // Validate OTP format
    if (typeof otpCode !== 'string' || otpCode.length !== OTP_CONFIG.LENGTH) {
      return sendError(res, `OTP must be ${OTP_CONFIG.LENGTH} digits`, 400);
    }

    // Check if user has a wallet
    const wallet = await getUserMasterWallet(parseInt(userId));
    if (!wallet) {
      return sendError(res, 'No wallet found', 404);
    }

    // Verify OTP for PIN reset (using 'password_reset' type as it's similar)
    const otpVerification = await verifyOTPCode(parseInt(userId), otpCode, 'password_reset');
    if (!otpVerification.isValid) {
      logger.warn('OTP verification failed during PIN reset', {
        userId,
        otpCode,
        error: otpVerification.error
      });
      return sendError(res, otpVerification.error || 'Invalid OTP code', 400);
    }

    // Mark OTP as used
    if (otpVerification.otpRecord) {
      await markOTPAsUsed(otpVerification.otpRecord.id);
    }

    // Hash the new PIN
    const hashedNewPin = await hashPin(newPin);

    // Update the PIN
    const success = await setWalletMasterPin(parseInt(userId), hashedNewPin);

    if (!success) {
      return sendError(res, 'Failed to reset wallet PIN', 500);
    }

    logger.info('Wallet PIN reset successfully', { userId });

    sendUserSuccess(res, 'PIN', 'RESET_SUCCESS', {
      message: 'PIN reset successfully'
    });

  } catch (error) {
    logger.error('Error resetting wallet PIN', { error, userId: req.userId });
    sendError(res, 'Failed to reset wallet PIN', 500);
  }
};

/**
 * Change wallet master PIN (for existing wallets) - now requires OTP verification
 */
export const changeWalletPin = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { currentPin, newPin, otpCode } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!currentPin || !newPin || !otpCode) {
      return sendError(res, 'Current PIN, new PIN, and OTP code are required', 400);
    }

    // Validate new PIN format (should be 4-6 digits)
    if (!/^\d{4,6}$/.test(newPin)) {
      return sendError(res, 'New PIN must be 4-6 digits', 400);
    }

    // Validate OTP format
    if (typeof otpCode !== 'string' || otpCode.length !== OTP_CONFIG.LENGTH) {
      return sendError(res, `OTP must be ${OTP_CONFIG.LENGTH} digits`, 400);
    }

    // Check if user has a wallet
    const wallet = await getUserMasterWallet(parseInt(userId));
    if (!wallet || !wallet.wallet_master_pin) {
      return sendError(res, 'No wallet or PIN found', 404);
    }

    // Verify current PIN
    const isCurrentPinValid = await comparePin(currentPin, wallet.wallet_master_pin);
    if (!isCurrentPinValid) {
      logger.warn('Invalid current PIN attempt during PIN change', { userId });
      return sendError(res, 'Current PIN is incorrect', 400);
    }

    // Verify OTP
    const otpVerification = await verifyOTPCode(parseInt(userId), otpCode, 'transaction');
    if (!otpVerification.isValid) {
      logger.warn('OTP verification failed during PIN change', {
        userId,
        otpCode,
        error: otpVerification.error
      });
      return sendError(res, otpVerification.error || 'Invalid OTP code', 400);
    }

    // Mark OTP as used
    if (otpVerification.otpRecord) {
      await markOTPAsUsed(otpVerification.otpRecord.id);
    }

    // Hash the new PIN
    const hashedNewPin = await hashPin(newPin);

    // Update the PIN
    const success = await setWalletMasterPin(parseInt(userId), hashedNewPin);

    if (!success) {
      return sendError(res, 'Failed to change wallet PIN', 500);
    }

    logger.info('Wallet PIN changed successfully with OTP verification', { userId });

    sendSuccess(res, {
      message: 'PIN changed successfully'
    }, 'Wallet PIN changed successfully');

  } catch (error) {
    logger.error('Error changing wallet PIN', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to change wallet PIN', 500);
  }
};

/**
 * Verify wallet master PIN
 */
export const verifyWalletPin = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { pin } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!pin || typeof pin !== 'string') {
      return sendError(res, 'PIN is required', 400);
    }

    // Get wallet with PIN
    const wallet = await getUserMasterWallet(parseInt(userId));
    if (!wallet || !wallet.wallet_master_pin) {
      return sendError(res, 'No wallet PIN found', 404);
    }

    // Verify PIN
    const isValid = await comparePin(pin, wallet.wallet_master_pin);

    if (!isValid) {
      logger.warn('Invalid wallet PIN attempt', { userId });
      return sendError(res, 'Invalid PIN', 400);
    }

    sendSuccess(res, {
      verified: true
    }, 'PIN verified successfully');

  } catch (error) {
    logger.error('Error verifying wallet PIN', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to verify wallet PIN', 500);
  }
};

/**
 * Get wallet balance
 */
export const getBalance = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const balance = await getWalletBalance(parseInt(userId));

    if (balance === null) {
      return sendError(res, 'No wallet found', 404);
    }

    sendSuccess(res, {
      balance
    }, 'Balance retrieved successfully');

  } catch (error) {
    logger.error('Error getting wallet balance', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve balance', 500);
  }
};

/**
 * Add money to wallet using Stripe integration with PIN verification
 */
export const addMoney = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { amount, pin, paymentMethod, bankAccountId } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return sendUserError(res, 'MONEY', 'INVALID_AMOUNT', 400);
    }

    if (!pin || typeof pin !== 'string') {
      return sendUserError(res, 'PIN', 'VERIFIED', 400);
    }

    if (amount < 10) {
      return sendError(res, 'Minimum amount is $10.00 to add money to your wallet', 400);
    }

    if (amount > 10000) {
      return sendError(res, 'Maximum amount is $10,000.00 per transaction for your security', 400);
    }

    // Verify wallet PIN first
    const wallet = await getUserMasterWallet(parseInt(userId));
    if (!wallet || !wallet.wallet_master_pin) {
      return sendError(res, 'No wallet PIN found', 404);
    }

    const isValidPin = await comparePin(pin, wallet.wallet_master_pin);
    if (!isValidPin) {
      logger.warn('Invalid wallet PIN attempt during add money', { userId });
      return sendError(res, 'Invalid PIN', 400);
    }

    // Get bank account details - handle both database ID and plaid_account_id
    let bankAccount = await executeQuerySingle(
      'SELECT * FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
      [bankAccountId, parseInt(userId)]
    );

    // If not found by database ID, try by plaid_account_id (for backward compatibility)
    if (!bankAccount) {
      bankAccount = await executeQuerySingle(
        'SELECT * FROM tbl_bank_accounts WHERE plaid_account_id = ? AND user_id = ?',
        [bankAccountId, parseInt(userId)]
      );
    }

    if (!bankAccount) {
      logger.error('Bank account not found for add money', {
        userId: parseInt(userId),
        bankAccountId,
        searchedByDatabaseId: true,
        searchedByPlaidAccountId: true
      });
      return sendError(res, 'Bank account not found', 404);
    }

    logger.info('Bank account found for add money', {
      userId: parseInt(userId),
      bankAccountId,
      foundBankAccount: {
        id: bankAccount.id,
        plaid_account_id: bankAccount.plaid_account_id,
        bank_name: bankAccount.bank_name,
        account_mask: bankAccount.account_mask
      }
    });

    // Use Stripe wallet service for adding money
    const stripeResult = await StripeWalletService.addMoneyToWallet(
      parseInt(userId),
      amount,
      bankAccount.plaid_account_id,
      `Add money to wallet - $${amount}`
    );

    if (stripeResult.success) {
      const newBalance = stripeResult.newBalance || 0;
      
      logger.info('Money added to wallet via Stripe', {
        userId: parseInt(userId),
        amount,
        stripePaymentIntentId: stripeResult.paymentIntent?.id,
        transactionId: stripeResult.transaction?.id,
        newBalance
      });

      return sendUserSuccess(res, 'MONEY', 'ADDED', {
        success: true,
        newBalance,
        formattedNewBalance: formatAmount(newBalance),
        amount,
        formattedAmount: formatAmount(amount),
        transactionId: stripeResult.transaction?.id,
        stripePaymentIntentId: stripeResult.paymentIntent?.id,
        requiresAction: stripeResult.requiresAction,
        clientSecret: stripeResult.clientSecret,
        message: stripeResult.requiresAction 
          ? 'Payment requires additional verification' 
          : 'Money added successfully',
        balanceMessage: getBalanceMessage(newBalance)
      });
    } else {
      logger.error('Failed to add money via Stripe', {
        userId: parseInt(userId),
        amount,
        error: stripeResult.error
      });
      sendUserError(res, 'MONEY', 'ADDED', 400, stripeResult.error || 'Failed to add money to wallet');
    }

  } catch (error) {
    logger.error('Error adding money to wallet via Stripe', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to add money to wallet', 500);
  }
};

/**
 * Withdraw money from wallet using Stripe integration with PIN verification
 */
export const withdrawMoney = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { amount, pin, bankAccountId, paymentMethod } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return sendError(res, 'Valid amount is required', 400);
    }

    if (!pin || typeof pin !== 'string') {
      return sendError(res, 'Wallet PIN is required', 400);
    }

    if (amount < 10) {
      return sendError(res, 'Minimum withdrawal amount is $10', 400);
    }

    if (amount > 5000) {
      return sendError(res, 'Maximum withdrawal amount is $5,000', 400);
    }

    // Verify wallet PIN first
    const wallet = await getUserMasterWallet(parseInt(userId));
    if (!wallet || !wallet.wallet_master_pin) {
      return sendError(res, 'No wallet PIN found', 404);
    }

    const isValidPin = await comparePin(pin, wallet.wallet_master_pin);
    if (!isValidPin) {
      logger.warn('Invalid wallet PIN attempt during withdrawal', { userId });
      return sendError(res, 'Invalid PIN', 400);
    }

    // Get bank account details - handle both database ID and plaid_account_id
    let bankAccount = await executeQuerySingle(
      'SELECT * FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
      [bankAccountId, parseInt(userId)]
    );

    // If not found by database ID, try by plaid_account_id (for backward compatibility)
    if (!bankAccount) {
      bankAccount = await executeQuerySingle(
        'SELECT * FROM tbl_bank_accounts WHERE plaid_account_id = ? AND user_id = ?',
        [bankAccountId, parseInt(userId)]
      );
    }

    if (!bankAccount) {
      logger.error('Bank account not found for withdrawal', {
        userId: parseInt(userId),
        bankAccountId,
        searchedByDatabaseId: true,
        searchedByPlaidAccountId: true
      });
      return sendError(res, 'Bank account not found', 404);
    }

    logger.info('Bank account found for withdrawal', {
      userId: parseInt(userId),
      bankAccountId,
      foundBankAccount: {
        id: bankAccount.id,
        plaid_account_id: bankAccount.plaid_account_id,
        bank_name: bankAccount.bank_name,
        account_mask: bankAccount.account_mask
      }
    });

    // Use Stripe wallet service for withdrawal
    const result = await StripeWalletService.withdrawFromWallet(
      parseInt(userId),
      amount,
      bankAccount.plaid_account_id,
      `Withdraw from wallet - $${amount}`
    );

    if (result.success) {
      // Initialize withdrawal progress if we have a transaction ID
      if (result.transaction?.id) {
        await WithdrawalProgressService.initializeWithdrawalProgress(result.transaction.id);

        // Update validation step as completed since we've validated the account
        await WithdrawalProgressService.updateWithdrawalStep(
          result.transaction.id,
          'validation',
          'completed',
          'Bank account validated and withdrawal authorized'
        );

        // Update processing step as in progress
        await WithdrawalProgressService.updateWithdrawalStep(
          result.transaction.id,
          'processing',
          'in_progress',
          'Processing withdrawal via Stripe'
        );
      }

      // Log the successful transaction
      logger.info('Money withdrawn from wallet via Stripe', {
        userId: parseInt(userId),
        amount,
        transactionId: result.transaction?.id,
        newBalance: result.newBalance
      });

      sendSuccess(res, {
        success: true,
        newBalance: result.newBalance,
        amount,
        transactionId: result.transaction?.id,
        estimatedArrival: '1-3 business days',
        message: result.transaction ? 'Withdrawal initiated successfully' : 'Withdrawal processed'
      }, 'Withdrawal initiated successfully');
    } else {
      logger.error('Failed to withdraw money via Stripe', {
        userId: parseInt(userId),
        amount,
        error: result.error
      });
      sendError(res, result.error || 'Failed to withdraw money', 400);
    }

  } catch (error) {
    logger.error('Error withdrawing money from wallet via Stripe', {
      error: error instanceof Error ? error.message : 'Unknown error',
      errorStack: error instanceof Error ? error.stack : undefined,
      errorName: error instanceof Error ? error.name : undefined,
      userId: req.userId,
      requestBody: req.body,
      sqlState: (error as any)?.sqlState,
      sqlMessage: (error as any)?.sqlMessage,
      errno: (error as any)?.errno,
      code: (error as any)?.code
    });
    sendError(res, 'Failed to withdraw money from wallet', 500);
  }
};



/**
 * Get wallet transaction history
 */
export const getTransactionHistory = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { limit = 50, offset = 0 } = req.query;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const transactions = await getWalletTransactions(
      parseInt(userId),
      parseInt(limit as string),
      parseInt(offset as string)
    );

    // Format transactions for frontend
    const formattedTransactions = transactions.map(transaction => ({
      id: transaction.id,
      type: transaction.amount > 0 ? 'credit' : 'debit',
      amount: Math.abs(transaction.amount),
      description: transaction.description,
      provider: transaction.payment_provider,
      reference: transaction.reference_id,
      status: parseInt(transaction.status_id.toString()) === 1 ? 'completed' : 'pending',
      date: transaction.created_at,
      metadata: transaction.meta_data
    }));

    sendSuccess(res, {
      transactions: formattedTransactions,
      total: formattedTransactions.length,
      limit: parseInt(limit as string),
      offset: parseInt(offset as string)
    }, 'Transaction history retrieved successfully');

  } catch (error) {
    logger.error('Error getting transaction history', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve transaction history', 500);
  }
};

/**
 * Get enhanced wallet information including bank account details
 */
export const getEnhancedWalletInfo = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const wallet = await getUserMasterWallet(parseInt(userId));
    const requiresPinSetup = await checkPinSetupRequired(parseInt(userId));
    const stats = await getWalletStats(parseInt(userId));
    const primaryBank = await getPrimaryBankAccount(parseInt(userId));

    if (!wallet) {
      return sendSuccess(res, {
        hasWallet: false,
        requiresPinSetup: true,
        stats,
        primaryBank: null
      }, 'No wallet found for user');
    }

    sendSuccess(res, {
      hasWallet: true,
      requiresPinSetup,
      wallet: {
        id: wallet.id,
        walletUniqueId: wallet.wallet_unique_id,
        name: wallet.name,
        username: wallet.username,
        balance: wallet.balance,
        statusId: wallet.status_id,
        createdAt: wallet.created_at,
        lastUpdated: wallet.last_updated
      },
      primaryBank: primaryBank ? {
        id: primaryBank.id,
        bankName: primaryBank.bank_name,
        accountMask: primaryBank.account_mask,
        accountType: primaryBank.account_type,
        isPrimary: primaryBank.is_primary
      } : null,
      stats
    }, 'Enhanced wallet information retrieved successfully');

  } catch (error) {
    logger.error('Error getting enhanced wallet info', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve wallet information', 500);
  }
};

/**
 * Check PIN setup status
 */
export const checkPinStatus = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const requiresPinSetup = await checkPinSetupRequired(parseInt(userId));

    sendSuccess(res, {
      requiresPinSetup
    }, 'PIN status checked successfully');

  } catch (error) {
    logger.error('Error checking PIN status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to check PIN status', 500);
  }
};

/**
 * Get dashboard data (wallet stats + recent transactions)
 */
export const getDashboardData = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    console.log(userId, 'userrrrid')
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Get wallet info
    const wallet = await getUserMasterWallet(parseInt(userId));
    const requiresPinSetup = await checkPinSetupRequired(parseInt(userId));
    const stats = await getWalletStats(parseInt(userId));
    const primaryBank = await getPrimaryBankAccount(parseInt(userId));

    // Get recent transactions (last 10)
    const transactions = await getWalletTransactions(parseInt(userId), 10, 0);

    // Prepare dashboard data
    const dashboardData = {
      wallet: wallet ? {
        id: wallet.id,
        walletUniqueId: wallet.wallet_unique_id,
        name: wallet.name,
        username: wallet.username,
        balance: wallet.balance,
        statusId: wallet.status_id,
        createdAt: wallet.created_at,
        lastUpdated: wallet.last_updated
      } : null,
      hasWallet: !!wallet,
      requiresPinSetup,
      stats,
      primaryBank,
      recentTransactions: transactions
    };

    // Add user-friendly formatting to dashboard data
    const enhancedDashboardData = {
      ...dashboardData,
      wallet: dashboardData.wallet ? {
        ...dashboardData.wallet,
        formattedBalance: formatAmount(dashboardData.wallet.balance),
        balanceMessage: getBalanceMessage(dashboardData.wallet.balance)
      } : null
    };

    sendUserSuccess(res, 'DASHBOARD', 'LOADED', enhancedDashboardData);

  } catch (error) {
    logger.error('Error getting dashboard data', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve dashboard data', 500);
  }
};



/**
 * Transfer money from one wallet to another
 */
export const checkUserWallet = async (req: Request, res: Response) => {
  try {
    const { userId } = req.query;

    if (!userId) {
      return sendError(res, 'User ID is required', 400);
    }

    const wallet = await getUserMasterWallet(parseInt(userId as string));

    sendSuccess(res, {
      userId: parseInt(userId as string),
      hasWallet: !!wallet,
      walletId: wallet?.wallet_unique_id,
      balance: wallet?.balance
    }, 'Wallet check completed');
  } catch (error) {
    logger.error('Error checking user wallet', { userId: req.query.userId, error });
    sendError(res, 'Failed to check user wallet', 500);
  }
};

/**
 * Get withdrawal status and progress with comprehensive failure handling
 */
export const getWithdrawalStatus = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { transactionId } = req.params;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!transactionId) {
      return sendError(res, 'Transaction ID is required', 400);
    }

    // Verify transaction belongs to user
    const transaction = await executeQuerySingle(
      'SELECT * FROM tbl_wallet_transactions WHERE id = ? AND user_id = ?',
      [transactionId, parseInt(userId)]
    );

    if (!transaction) {
      return sendError(res, 'Transaction not found', 404);
    }

    // Get withdrawal progress steps
    const progressSteps = await executeQuery(`
      SELECT
        wp.step_key,
        wp.status,
        wp.completed_at,
        wp.notes,
        wp.created_at,
        ws.title,
        ws.description,
        ws.step_order
      FROM tbl_withdrawal_progress wp
      JOIN tbl_withdrawal_steps ws ON wp.step_key = ws.step_key
      WHERE wp.transaction_id = ?
      ORDER BY ws.step_order ASC
    `, [transactionId]);

    // Get all possible steps for this withdrawal type
    const allSteps = await executeQuery(`
      SELECT step_key, title, description, step_order
      FROM tbl_withdrawal_steps
      WHERE is_active = 1
      ORDER BY step_order ASC
    `);

    // Parse metadata
    const metaData = typeof transaction.meta_data === 'string'
      ? JSON.parse(transaction.meta_data)
      : transaction.meta_data;

    // Determine current status
    const completedSteps = progressSteps.filter(step => step.status === 'completed');
    const failedSteps = progressSteps.filter(step => step.status === 'failed');
    const currentStep = progressSteps.find(step => step.status === 'pending') ||
      completedSteps[completedSteps.length - 1];

    let overallStatus = 'pending';
    if (failedSteps.length > 0) {
      overallStatus = 'failed';
    } else if (progressSteps.some(step => step.step_key === 'completed' && step.status === 'completed')) {
      overallStatus = 'completed';
    } else if (progressSteps.some(step => step.step_key === 'reversed' && step.status === 'completed')) {
      overallStatus = 'reversed';
    }

    sendSuccess(res, {
      transaction: {
        id: transaction.id,
        amount: Math.abs(parseFloat(transaction.amount.toString())),
        description: transaction.description,
        created_at: transaction.created_at,
        paymentMethod: metaData?.paymentMethodName || 'ACH Standard',
        bankName: metaData?.bankName || 'Bank Account',
        estimatedArrival: metaData?.estimatedArrival || '1-3 business days',
        processingFee: metaData?.processingFee || 0
      },
      status: {
        overall: overallStatus,
        currentStep: currentStep?.step_key || 'initiated',
        currentStepTitle: currentStep?.title || 'Processing',
        currentStepDescription: currentStep?.description || 'Your withdrawal is being processed',
        lastUpdated: currentStep?.completed_at || currentStep?.created_at
      },
      progress: {
        completed: completedSteps.length,
        total: progressSteps.length,
        percentage: progressSteps.length > 0 ? Math.round((completedSteps.length / progressSteps.length) * 100) : 0
      },
      steps: progressSteps.map(step => ({
        key: step.step_key,
        title: step.title,
        description: step.description,
        status: step.status,
        completedAt: step.completed_at,
        notes: step.notes,
        order: step.step_order
      })),
      timeline: progressSteps
        .filter(step => step.status === 'completed' || step.status === 'failed')
        .sort((a, b) => new Date(a.completed_at || a.created_at).getTime() - new Date(b.completed_at || b.created_at).getTime())
        .map(step => ({
          title: step.title,
          description: step.notes || step.description,
          timestamp: step.completed_at || step.created_at,
          status: step.status
        }))
    }, 'Withdrawal status retrieved successfully');

  } catch (error) {
    logger.error('Error getting withdrawal status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      transactionId: req.params.transactionId
    });
    sendError(res, 'Failed to get withdrawal status', 500);
  }
};


export const transferToWallet = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { recipientUserId, amount, pin } = req.body;

    logger.info('Transfer to wallet request received', {
      fromUserId: userId,
      recipientUserId,
      amount,
      pinLength: pin?.length,
      requestBody: req.body
    });

    // Validate input
    if (!recipientUserId || !amount || !pin) {
      return sendError(res, 'Recipient, amount, and PIN are required', 400);
    }

    if (typeof amount !== 'number' || amount <= 0) {
      return sendError(res, 'Invalid amount', 400);
    }

    if (typeof pin !== 'string' || pin.length < 4 || pin.length > 6) {
      return sendError(res, 'PIN must be 4-6 digits', 400);
    }

    // Perform the transfer
    const result = await transferWalletToWallet(
      parseInt(userId),
      parseInt(recipientUserId),
      amount,
      pin
    );

    if (result.success) {
      sendSuccess(res, {
        success: true,
        newBalance: result.newBalance,
        amount,
        transactionId: result.transactionId,
        message: result.message
      }, 'Transfer completed successfully');
    } else {
      sendError(res, result.message || 'Transfer failed', 400);
    }

  } catch (error) {
    logger.error('Error transferring to wallet', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to transfer money', 500);
  }
};

/**
 * Search for users available for wallet transfers
 */
export const searchUsers = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { search = '' } = req.query;

    // Search for users
    const users = await searchTransferRecipients(
      parseInt(userId),
      search as string
    );

    sendSuccess(res, users, 'Users retrieved successfully');

  } catch (error) {
    logger.error('Error searching users', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to search users', 500);
  }
};

/**
 * Transfer money directly from one bank account to another
 */
export const transferBankToBank = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const {
      recipientUserId,
      amount,
      pin,
      fromBankAccountId,
      toBankAccountId,
      description
    } = req.body;

    logger.info('Bank-to-bank transfer request received', {
      fromUserId: userId,
      recipientUserId,
      amount,
      fromBankAccountId,
      toBankAccountId,
      description
    });

    // Validate input
    if (!recipientUserId || !amount || !pin || !fromBankAccountId || !toBankAccountId) {
      return sendError(res, 'All fields are required: recipientUserId, amount, pin, fromBankAccountId, toBankAccountId', 400);
    }

    if (typeof amount !== 'number' || amount <= 0) {
      return sendError(res, 'Invalid amount', 400);
    }

    if (amount < 1) {
      return sendError(res, 'Minimum transfer amount is $1.00', 400);
    }

    if (amount > 50000) {
      return sendError(res, 'Maximum transfer amount is $50,000.00 per transaction', 400);
    }

    if (typeof pin !== 'string' || pin.length < 4 || pin.length > 6) {
      return sendError(res, 'PIN must be 4-6 digits', 400);
    }

    // Verify wallet PIN
    const pinValid = await verifyWalletMasterPin(parseInt(userId), pin);
    if (!pinValid) {
      return sendError(res, 'Invalid PIN', 401);
    }

    // Validate sender's bank account
    const senderAccountValidation = await StripeWalletService.validateBankAccount(fromBankAccountId, parseInt(userId));
    if (!senderAccountValidation.valid) {
      return sendError(res, senderAccountValidation.error || 'Invalid sender bank account', 400);
    }

    // Validate recipient's bank account
    const recipientAccountValidation = await StripeWalletService.validateBankAccount(toBankAccountId, parseInt(recipientUserId));
    if (!recipientAccountValidation.valid) {
      return sendError(res, recipientAccountValidation.error || 'Invalid recipient bank account', 400);
    }

    // Perform the bank-to-bank transfer
    const result = await StripeWalletService.transferBankToBank(
      parseInt(userId),
      parseInt(recipientUserId),
      amount,
      fromBankAccountId,
      toBankAccountId,
      description || `Bank transfer from user ${userId} to user ${recipientUserId}`
    );

    if (result.success) {
      sendSuccess(res, {
        success: true,
        amount,
        transferId: result.transferId,
        paymentIntentId: result.paymentIntent?.id,
        message: 'Bank-to-bank transfer initiated successfully',
        requiresAction: result.requiresAction,
        clientSecret: result.clientSecret
      }, 'Transfer initiated successfully');
    } else {
      sendError(res, result.error || 'Transfer failed', 400);
    }

  } catch (error) {
    logger.error('Error in bank-to-bank transfer', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to process bank-to-bank transfer', 500);
  }
};

/**
 * Validate bank account for transfers
 */
export const validateBankAccountForTransfer = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { bankAccountId } = req.params;

    if (!bankAccountId) {
      return sendError(res, 'Bank account ID is required', 400);
    }

    const validation = await StripeWalletService.validateBankAccount(bankAccountId, parseInt(userId));

    if (validation.valid) {
      sendSuccess(res, {
        valid: true,
        account: validation.account,
        warnings: validation.warnings,
        validationDetails: validation.validationDetails
      }, 'Bank account is valid for transfers');
    } else {
      res.status(400).json({
        success: false,
        message: validation.error || 'Bank account validation failed',
        data: {
          valid: false,
          account: validation.account,
          warnings: validation.warnings,
          validationDetails: validation.validationDetails
        }
      });
      return;
    }

  } catch (error) {
    logger.error('Error validating bank account', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to validate bank account', 500);
  }
};

/**
 * Check if bank account is ready for immediate transfers
 */
export const checkBankAccountReadiness = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { bankAccountId } = req.params;

    if (!bankAccountId) {
      return sendError(res, 'Bank account ID is required', 400);
    }

    const readiness = await StripeWalletService.isAccountReadyForTransfers(bankAccountId, parseInt(userId));

    sendSuccess(res, {
      ready: readiness.ready,
      canDeposit: readiness.canDeposit,
      canWithdraw: readiness.canWithdraw,
      issues: readiness.issues,
      recommendations: {
        nextSteps: readiness.ready
          ? ['Account is ready for transfers']
          : readiness.issues.map(issue => `Resolve: ${issue}`)
      }
    }, readiness.ready ? 'Bank account is ready for transfers' : 'Bank account needs setup before transfers');

  } catch (error) {
    logger.error('Error checking bank account readiness', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to check bank account readiness', 500);
  }
};

/**
 * Get transfer status
 */
export const getTransferStatusController = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { transferId } = req.params;
    const { type } = req.query; // 'transfer', 'payment_intent', or 'transaction'

    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    let result;

    if (type === 'transaction') {
      // Get comprehensive status for a database transaction ID
      result = await StripeWalletService.getComprehensiveTransferStatus(parseInt(transferId));
    } else if (type === 'payment_intent') {
      // Get status for a Stripe payment intent ID
      result = await StripeWalletService.getPaymentIntentStatus(transferId);
    } else {
      // Get status for a Stripe transfer ID
      result = await StripeWalletService.getTransferStatus(transferId);
    }

    if (result.success) {
      const responseData: any = {
        status: 'status' in result ? result.status : 'unknown',
        type: type || 'transfer'
      };

      if ('transaction' in result && result.transaction) {
        responseData.transaction = result.transaction;
      }

      if ('stripeStatus' in result && result.stripeStatus) {
        responseData.stripeStatus = result.stripeStatus;
      }

      if ('transfer' in result && result.transfer) {
        responseData.transfer = result.transfer;
      }

      if ('paymentIntent' in result && result.paymentIntent) {
        responseData.paymentIntent = result.paymentIntent;
      }

      sendSuccess(res, responseData, 'Transfer status retrieved successfully');
    } else {
      sendError(res, result.error || 'Failed to get transfer status', 400);
    }

  } catch (error) {
    logger.error('Error getting transfer status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      transferId: req.params.transferId,
      type: req.query.type
    });
    sendError(res, 'Failed to get transfer status', 500);
  }
};

/**
 * Get comprehensive transfer history
 */
export const getComprehensiveTransferHistory = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const {
      transferType = 'all',
      status = 'all',
      dateFrom,
      dateTo,
      limit = 50,
      offset = 0,
      searchTerm
    } = req.query;

    const filter = {
      userId: parseInt(userId),
      transferType: transferType as any,
      status: status as any,
      dateFrom: dateFrom as string,
      dateTo: dateTo as string,
      limit: parseInt(limit as string),
      offset: parseInt(offset as string),
      searchTerm: searchTerm as string
    };

    const result = await TransferHistoryService.getTransferHistory(filter);

    sendSuccess(res, {
      transfers: result.transfers,
      summary: result.summary,
      pagination: result.pagination,
      filters: {
        transferType,
        status,
        dateFrom,
        dateTo,
        searchTerm
      }
    }, 'Transfer history retrieved successfully');

  } catch (error) {
    logger.error('Error getting comprehensive transfer history', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to get transfer history', 500);
  }
};

/**
 * Get transfer details by ID
 */
export const getTransferDetails = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { transferId } = req.params;

    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    const transfer = await TransferHistoryService.getTransferDetails(
      parseInt(transferId),
      parseInt(userId)
    );

    if (!transfer) {
      return sendError(res, 'Transfer not found or not authorized', 404);
    }

    sendSuccess(res, {
      transfer
    }, 'Transfer details retrieved successfully');

  } catch (error) {
    logger.error('Error getting transfer details', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      transferId: req.params.transferId
    });
    sendError(res, 'Failed to get transfer details', 500);
  }
};

/**
 * Get transfer statistics for dashboard
 */
export const getTransferStatistics = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { period = '30d' } = req.query;

    // Calculate date range based on period
    let dateFrom: string;
    const dateTo = new Date().toISOString();

    switch (period) {
      case '7d':
        dateFrom = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
        break;
      case '30d':
        dateFrom = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
        break;
      case '90d':
        dateFrom = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString();
        break;
      case '1y':
        dateFrom = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString();
        break;
      default:
        dateFrom = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
    }

    const filter = {
      userId: parseInt(userId),
      dateFrom,
      dateTo,
      limit: 1000 // Get all for statistics
    };

    const result = await TransferHistoryService.getTransferHistory(filter);

    // Calculate additional statistics
    const statistics = {
      ...result.summary,
      period,
      dateRange: {
        from: dateFrom,
        to: dateTo
      },
      averageTransferAmount: result.summary.totalTransfers > 0
        ? result.summary.totalAmountTransferred / result.summary.totalTransfers
        : 0,
      successRate: result.summary.totalTransfers > 0
        ? (result.summary.completedTransfers / result.summary.totalTransfers) * 100
        : 0,
      recentActivity: result.transfers.slice(0, 5) // Last 5 transfers
    };

    sendSuccess(res, {
      statistics
    }, 'Transfer statistics retrieved successfully');

  } catch (error) {
    logger.error('Error getting transfer statistics', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to get transfer statistics', 500);
  }
};

/**
 * Handle failed transfer recovery
 */
export const handleFailedTransferRecovery = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { transactionId } = req.params;
    const { failureReason, automaticRecovery = true } = req.body;

    if (!transactionId) {
      return sendError(res, 'Transaction ID is required', 400);
    }

    if (!failureReason) {
      return sendError(res, 'Failure reason is required', 400);
    }

    // Verify transaction belongs to user
    const transaction = await executeQuerySingle(
      'SELECT id, user_id FROM tbl_wallet_transactions WHERE id = ? AND user_id = ?',
      [transactionId, userId]
    );

    if (!transaction) {
      return sendError(res, 'Transaction not found or not authorized', 404);
    }

    const result = await TransferRecoveryService.handleFailedTransfer(
      parseInt(transactionId),
      failureReason,
      automaticRecovery
    );

    if (result.success) {
      sendSuccess(res, {
        recovery: result,
        transactionId: parseInt(transactionId)
      }, `Recovery ${result.action}: ${result.message}`);
    } else {
      sendError(res, result.error || 'Recovery failed', 400);
    }

  } catch (error) {
    logger.error('Error handling failed transfer recovery', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      transactionId: req.params.transactionId
    });
    sendError(res, 'Failed to handle transfer recovery', 500);
  }
};

/**
 * Compensate user for system errors (admin only)
 */
export const compensateUser = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const { targetUserId, amount, reason, originalTransactionId } = req.body;

    if (!targetUserId || !amount || !reason) {
      return sendError(res, 'Target user ID, amount, and reason are required', 400);
    }

    if (amount <= 0 || amount > 10000) {
      return sendError(res, 'Invalid compensation amount (must be between $0.01 and $10,000)', 400);
    }

    // TODO: Add admin role verification here
    // For now, allowing authenticated users (should be restricted to admins)

    const result = await TransferRecoveryService.compensateUser(
      parseInt(targetUserId),
      amount,
      reason,
      originalTransactionId ? parseInt(originalTransactionId) : undefined
    );

    if (result.success) {
      sendSuccess(res, {
        compensation: result,
        targetUserId: parseInt(targetUserId)
      }, `User compensated: ${result.message}`);
    } else {
      sendError(res, result.error || 'Compensation failed', 400);
    }

  } catch (error) {
    logger.error('Error compensating user', {
      error: error instanceof Error ? error.message : 'Unknown error',
      adminUserId: req.userId
    });
    sendError(res, 'Failed to compensate user', 500);
  }
};

/**
 * Get failed transactions that need recovery (admin only)
 */
export const getFailedTransactionsForRecovery = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const { limit = 50 } = req.query;

    // TODO: Add admin role verification here
    // For now, allowing authenticated users (should be restricted to admins)

    const failedTransactions = await TransferRecoveryService.getFailedTransactionsForRecovery(
      parseInt(limit as string)
    );

    sendSuccess(res, {
      failedTransactions,
      count: failedTransactions.length,
      limit: parseInt(limit as string)
    }, 'Failed transactions retrieved successfully');

  } catch (error) {
    logger.error('Error getting failed transactions for recovery', {
      error: error instanceof Error ? error.message : 'Unknown error',
      adminUserId: req.userId
    });
    sendError(res, 'Failed to get failed transactions', 500);
  }
};

/**
 * Run transfer flow tests (development/testing only)
 */
export const runTransferFlowTests = async (req: Request & { userId?: string }, res: Response) => {
  try {
    // Only allow in development/testing environment
    if (process.env.NODE_ENV === 'production') {
      return sendError(res, 'Testing endpoints not available in production', 403);
    }

    const { testSuite } = req.query;

    logger.info('Running transfer flow tests', {
      requestedSuite: testSuite,
      environment: process.env.NODE_ENV
    });

    let testResults;

    if (testSuite && typeof testSuite === 'string') {
      // Run specific test suite
      switch (testSuite) {
        case 'stripe':
          testResults = [await TransferTestingService.runStripeIntegrationTests()];
          break;
        case 'validation':
          testResults = [await TransferTestingService.runBankAccountValidationTests()];
          break;
        case 'history':
          testResults = [await TransferTestingService.runTransferHistoryTests()];
          break;
        case 'recovery':
          testResults = [await TransferTestingService.runRecoveryTests()];
          break;
        case 'e2e':
          testResults = [await TransferTestingService.runEndToEndTests()];
          break;
        default:
          return sendError(res, 'Invalid test suite. Available: stripe, validation, history, recovery, e2e', 400);
      }
    } else {
      // Run all tests
      testResults = await TransferTestingService.runAllTests();
    }

    // Generate test report
    const report = TransferTestingService.generateTestReport(testResults);

    const summary = {
      totalSuites: testResults.length,
      totalTests: testResults.reduce((sum, suite) => sum + suite.summary.total, 0),
      totalPassed: testResults.reduce((sum, suite) => sum + suite.summary.passed, 0),
      totalFailed: testResults.reduce((sum, suite) => sum + suite.summary.failed, 0),
      totalDuration: testResults.reduce((sum, suite) => sum + suite.summary.duration, 0)
    };

    sendSuccess(res, {
      summary,
      testResults,
      report,
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    }, 'Transfer flow tests completed');

  } catch (error) {
    logger.error('Error running transfer flow tests', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to run transfer flow tests', 500);
  }
};

/**
 * Validate Stripe and Plaid integration (health check)
 */
export const validateIntegrations = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const validationResults = {
      stripe: {
        configured: !!process.env.STRIPE_SECRET_KEY,
        webhookConfigured: !!process.env.STRIPE_WEBHOOK_SECRET,
        status: 'unknown'
      },
      plaid: {
        configured: !!process.env.PLAID_CLIENT_ID && !!process.env.PLAID_SECRET,
        environment: process.env.PLAID_ENV || 'unknown',
        status: 'unknown'
      },
      database: {
        connected: false,
        tablesExist: false
      }
    };

    // Test database connection
    try {
      const dbTest = await executeQuerySingle('SELECT 1 as test', []);
      validationResults.database.connected = !!dbTest;

      // Check if required tables exist
      const tableCheck = await executeQuerySingle(
        "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_name IN ('tbl_wallet_transactions', 'tbl_masterwallet', 'tbl_bank_accounts')",
        []
      );
      validationResults.database.tablesExist = tableCheck?.count >= 3;
    } catch (error) {
      logger.error('Database validation failed', { error });
    }

    // Basic Stripe validation
    if (validationResults.stripe.configured) {
      try {
        // This would require actual Stripe API call in real implementation
        validationResults.stripe.status = 'configured';
      } catch (error) {
        validationResults.stripe.status = 'error';
      }
    }

    // Basic Plaid validation
    if (validationResults.plaid.configured) {
      try {
        // This would require actual Plaid API call in real implementation
        validationResults.plaid.status = 'configured';
      } catch (error) {
        validationResults.plaid.status = 'error';
      }
    }

    const allValid = validationResults.stripe.configured &&
                    validationResults.plaid.configured &&
                    validationResults.database.connected &&
                    validationResults.database.tablesExist;

    sendSuccess(res, {
      valid: allValid,
      validationResults,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV
    }, allValid ? 'All integrations validated successfully' : 'Some integrations need attention');

  } catch (error) {
    logger.error('Error validating integrations', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to validate integrations', 500);
  }
};

/**
 * Migrate existing users to Stripe customers (admin only)
 */
export const migrateUsersToStripe = async (req: Request & { userId?: string }, res: Response) => {
  try {
    // Only allow in development/testing environment or for admin users
    if (process.env.NODE_ENV === 'production') {
      // TODO: Add proper admin role check here
      logger.warn('Stripe migration attempted in production', {
        userId: req.userId,
        ip: req.ip
      });
    }

    const { limit = 50 } = req.query;

    logger.info('Starting user migration to Stripe customers', {
      requestedBy: req.userId,
      limit: parseInt(limit as string)
    });

    const result = await StripeWalletService.migrateUsersToStripeCustomers(
      parseInt(limit as string)
    );

    if (result.success) {
      sendSuccess(res, {
        migration: result,
        summary: {
          migrated: result.migrated,
          failed: result.failed,
          successRate: result.migrated + result.failed > 0
            ? ((result.migrated / (result.migrated + result.failed)) * 100).toFixed(2) + '%'
            : '0%'
        }
      }, `Migration completed: ${result.migrated} users migrated successfully`);
    } else {
      res.status(400).json({
        success: false,
        message: `Migration completed with errors: ${result.failed} failed`,
        data: {
          migration: result,
          summary: {
            migrated: result.migrated,
            failed: result.failed,
            errors: result.errors
          }
        }
      });
      return;
    }

  } catch (error) {
    logger.error('Error in user migration to Stripe', {
      error: error instanceof Error ? error.message : 'Unknown error',
      requestedBy: req.userId
    });
    sendError(res, 'Failed to migrate users to Stripe', 500);
  }
};

/**
 * Get withdrawal progress for a transaction
 */
export const getWithdrawalProgress = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { transactionId } = req.params;

    if (!transactionId) {
      return sendError(res, 'Transaction ID is required', 400);
    }

    // Verify transaction belongs to user
    const transaction = await executeQuerySingle(
      'SELECT id, user_id, amount, description, created_at FROM tbl_wallet_transactions WHERE id = ? AND user_id = ?',
      [transactionId, userId]
    );

    if (!transaction) {
      return sendError(res, 'Transaction not found or not authorized', 404);
    }

    // Get withdrawal progress
    const progress = await WithdrawalProgressService.getWithdrawalProgress(parseInt(transactionId));
    const status = await WithdrawalProgressService.getWithdrawalStatus(parseInt(transactionId));

    sendSuccess(res, {
      transaction: {
        id: transaction.id,
        amount: transaction.amount,
        description: transaction.description,
        created_at: transaction.created_at
      },
      progress,
      status,
      transactionId: parseInt(transactionId)
    }, 'Withdrawal progress retrieved successfully');

  } catch (error) {
    logger.error('Error fetching withdrawal progress', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      transactionId: req.params.transactionId
    });
    sendError(res, 'Failed to get withdrawal progress', 500);
  }
};

/**
 * Setup missing database tables (admin only)
 */
export const setupMissingTables = async (req: Request & { userId?: string }, res: Response) => {
  try {
    // Only allow in development/testing environment
    if (process.env.NODE_ENV === 'production') {
      return sendError(res, 'Database setup not available in production', 403);
    }

    logger.info('Setting up missing database tables', {
      requestedBy: req.userId,
      environment: process.env.NODE_ENV
    });

    // Import and run the setup function
    const { createMissingTables } = await import('../scripts/setupMissingTables');
    await createMissingTables();

    sendSuccess(res, {
      setup: {
        completed: true,
        tables: [
          'tbl_stripe_payment_methods',
          'tbl_withdrawal_steps',
          'tbl_withdrawal_progress'
        ],
        columns: [
          'tbl_users.stripe_customer_id',
          'tbl_bank_accounts.stripe_bank_account_id'
        ]
      },
      timestamp: new Date().toISOString()
    }, 'Database tables setup completed successfully');

  } catch (error) {
    logger.error('Error setting up missing database tables', {
      error: error instanceof Error ? error.message : 'Unknown error',
      requestedBy: req.userId
    });
    sendError(res, 'Failed to setup database tables', 500);
  }
};

/**
 * Ensure Stripe integration for user's bank accounts
 */
export const ensureStripeIntegration = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    logger.info('Ensuring Stripe integration for user', { userId });

    const result = await BankStripeIntegrationService.ensureStripeIntegrationForUser(parseInt(userId));

    if (result.success) {
      sendSuccess(res, {
        integration: result,
        message: `Stripe integration complete: ${result.paymentMethodsCreated} payment methods created`
      }, 'Stripe integration ensured successfully');
    } else {
      res.status(400).json({
        success: false,
        message: 'Stripe integration completed with errors',
        data: {
          integration: result,
          errors: result.errors
        }
      });
    }

  } catch (error) {
    logger.error('Error ensuring Stripe integration', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to ensure Stripe integration', 500);
  }
};

/**
 * Check Stripe integration status for user
 */
export const checkStripeIntegrationStatus = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const status = await BankStripeIntegrationService.checkStripeIntegrationStatus(parseInt(userId));

    sendSuccess(res, {
      status,
      recommendations: status.isComplete
        ? ['Your Stripe integration is complete and ready for transfers']
        : [
            !status.hasStripeCustomer ? 'Create Stripe customer account' : null,
            status.bankAccountsCount === 0 ? 'Connect at least one bank account' : null,
            status.missingPaymentMethods.length > 0 ? `Create payment methods for ${status.missingPaymentMethods.length} bank accounts` : null
          ].filter(Boolean)
    }, 'Stripe integration status retrieved successfully');

  } catch (error) {
    logger.error('Error checking Stripe integration status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to check Stripe integration status', 500);
  }
};

/**
 * Migrate all users to Stripe integration (admin only)
 */
export const migrateAllUsersToStripeIntegration = async (req: Request & { userId?: string }, res: Response) => {
  try {
    // Only allow in development/testing environment or for admin users
    if (process.env.NODE_ENV === 'production') {
      // TODO: Add proper admin role check here
      logger.warn('Bulk Stripe integration migration attempted in production', {
        userId: req.userId,
        ip: req.ip
      });
    }

    const { limit = 50 } = req.query;

    logger.info('Starting bulk Stripe integration migration', {
      requestedBy: req.userId,
      limit: parseInt(limit as string)
    });

    const result = await BankStripeIntegrationService.migrateAllUsersToStripeIntegration(
      parseInt(limit as string)
    );

    if (result.success) {
      sendSuccess(res, {
        migration: result,
        summary: {
          usersProcessed: result.usersProcessed,
          usersSuccessful: result.usersSuccessful,
          usersFailed: result.usersFailed,
          successRate: result.usersProcessed > 0
            ? ((result.usersSuccessful / result.usersProcessed) * 100).toFixed(2) + '%'
            : '0%'
        }
      }, `Migration completed: ${result.usersSuccessful}/${result.usersProcessed} users migrated successfully`);
    } else {
      res.status(400).json({
        success: false,
        message: `Migration completed with errors: ${result.usersFailed} failed`,
        data: {
          migration: result,
          summary: {
            usersProcessed: result.usersProcessed,
            usersSuccessful: result.usersSuccessful,
            usersFailed: result.usersFailed,
            errors: result.errors
          }
        }
      });
    }

  } catch (error) {
    logger.error('Error in bulk Stripe integration migration', {
      error: error instanceof Error ? error.message : 'Unknown error',
      requestedBy: req.userId
    });
    sendError(res, 'Failed to migrate users to Stripe integration', 500);
  }
};
