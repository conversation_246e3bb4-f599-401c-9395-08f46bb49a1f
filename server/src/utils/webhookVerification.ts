import crypto from 'crypto';
import logger from './logger';
import { WEBHOOK_CONFIG } from '../config/webhookConfig';

/**
 * Enhanced webhook verification utilities with multiple security layers
 * Based on Plaid's webhook verification documentation with additional security measures
 */

/**
 * Enhanced Plaid webhook signature verification with timestamp validation
 * @param requestBody - The raw request body as string
 * @param signature - The Plaid-Verification header value
 * @param webhookSecret - The webhook secret for HMAC verification
 * @returns Verification result with detailed information
 */
export function verifyPlaidWebhookSignatureEnhanced(
  requestBody: string,
  signature: string,
  webhookSecret: string
): {
  isValid: boolean;
  reason?: string;
  timestampAge?: number;
  securityLevel: 'basic' | 'enhanced';
} {
  try {
    if (!signature || !webhookSecret) {
      logger.warn('Missing signature or webhook secret for verification');
      return {
        isValid: false,
        reason: 'Missing signature or webhook secret',
        securityLevel: 'basic'
      };
    }

    // Enhanced validation with timestamp checking
    if (WEBHOOK_CONFIG.SECURITY.ENHANCED_VALIDATION) {
      return verifyWithTimestampValidation(requestBody, signature, webhookSecret);
    }

    // Basic HMAC verification (legacy)
    return verifyBasicSignature(requestBody, signature, webhookSecret);

  } catch (error) {
    logger.error('Error in enhanced webhook signature verification', { error });
    return {
      isValid: false,
      reason: `Verification error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      securityLevel: 'basic'
    };
  }
}

/**
 * Verify webhook signature with timestamp validation (enhanced security)
 */
function verifyWithTimestampValidation(
  requestBody: string,
  signature: string,
  webhookSecret: string
): {
  isValid: boolean;
  reason?: string;
  timestampAge?: number;
  securityLevel: 'enhanced';
} {
  try {
    // Parse signature header (format: "t=timestamp,v1=signature")
    const elements = signature.split(',');
    let timestamp = '';
    let providedSignature = '';

    for (const element of elements) {
      const [key, value] = element.split('=');
      if (key === 't') {
        timestamp = value;
      } else if (key === 'v1') {
        providedSignature = value;
      }
    }

    // If no structured format, treat entire signature as the signature value
    if (!timestamp || !providedSignature) {
      providedSignature = signature;
      timestamp = Math.floor(Date.now() / 1000).toString();
      
      logger.warn('Webhook signature missing timestamp, using current time', {
        originalSignature: signature
      });
    }

    // Validate timestamp
    const currentTime = Date.now();
    const webhookTime = parseInt(timestamp) * 1000; // Convert to milliseconds
    const timestampAge = currentTime - webhookTime;

    // Check if timestamp is too old (replay attack prevention)
    const maxAge = WEBHOOK_CONFIG.SECURITY.MAX_TIMESTAMP_AGE * 1000; // Convert to milliseconds
    if (timestampAge > maxAge) {
      return {
        isValid: false,
        reason: 'Timestamp too old - possible replay attack',
        timestampAge,
        securityLevel: 'enhanced'
      };
    }

    // Check if timestamp is too far in the future (clock skew protection)
    const minAge = WEBHOOK_CONFIG.SECURITY.MIN_TIMESTAMP_AGE * 1000; // Convert to milliseconds
    if (timestampAge < minAge) {
      return {
        isValid: false,
        reason: 'Timestamp too far in future - clock skew detected',
        timestampAge,
        securityLevel: 'enhanced'
      };
    }

    // Compute expected signature
    const signedPayload = timestamp + '.' + requestBody;
    const expectedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(signedPayload, 'utf8')
      .digest('hex');

    // Compare signatures using timing-safe comparison
    const isValid = crypto.timingSafeEqual(
      Buffer.from(providedSignature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );

    return {
      isValid,
      reason: isValid ? undefined : 'Signature mismatch',
      timestampAge,
      securityLevel: 'enhanced'
    };

  } catch (error) {
    logger.error('Error in timestamp-based signature verification', { error });
    return {
      isValid: false,
      reason: `Enhanced verification error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      securityLevel: 'enhanced'
    };
  }
}

/**
 * Basic HMAC signature verification (legacy support)
 */
function verifyBasicSignature(
  requestBody: string,
  signature: string,
  webhookSecret: string
): {
  isValid: boolean;
  reason?: string;
  securityLevel: 'basic';
} {
  try {
    // Create HMAC SHA256 hash
    const hmac = crypto.createHmac('sha256', webhookSecret);
    hmac.update(requestBody, 'utf8');
    const expectedSignature = hmac.digest('hex');

    // Compare signatures
    const isValid = crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );

    if (!isValid) {
      logger.warn('Basic webhook signature verification failed', {
        expectedSignature,
        receivedSignature: signature
      });
    }

    return {
      isValid,
      reason: isValid ? undefined : 'Basic signature mismatch',
      securityLevel: 'basic'
    };
  } catch (error) {
    logger.error('Error in basic signature verification', { error });
    return {
      isValid: false,
      reason: `Basic verification error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      securityLevel: 'basic'
    };
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use verifyPlaidWebhookSignatureEnhanced instead
 */
export function verifyPlaidWebhookSignature(
  requestBody: string,
  signature: string,
  webhookSecret: string
): boolean {
  const result = verifyPlaidWebhookSignatureEnhanced(requestBody, signature, webhookSecret);
  return result.isValid;
}

/**
 * Extract signature from request headers with enhanced header checking
 */
export function extractWebhookSignature(req: any): {
  signature: string | null;
  headerSource: string;
  allHeaders: string[];
} {
  const possibleHeaders = [
    'plaid-verification',
    'x-plaid-signature', 
    'plaid-signature'
  ];
  
  const foundHeaders: string[] = [];
  let signature: string | null = null;
  let headerSource = '';
  
  for (const header of possibleHeaders) {
    if (req.headers[header]) {
      foundHeaders.push(header);
      if (!signature) {
        signature = req.headers[header];
        headerSource = header;
      }
    }
  }
  
  return {
    signature,
    headerSource,
    allHeaders: foundHeaders
  };
}

/**
 * Get request body as string for signature verification with validation
 */
export function getRequestBody(req: any): {
  body: string;
  contentType: string;
  bodySize: number;
} {
  let body: string;
  const contentType = req.headers['content-type'] || 'unknown';
  
  if (typeof req.body === 'string') {
    body = req.body;
  } else {
    // If body is already parsed as JSON, stringify it
    body = JSON.stringify(req.body);
  }
  
  return {
    body,
    contentType,
    bodySize: Buffer.byteLength(body, 'utf8')
  };
}

/**
 * Validate webhook request structure and security headers
 */
export function validateWebhookRequest(req: any): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  securityScore: number;
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  let securityScore = 0;
  
  // Check required headers
  const requiredHeaders = WEBHOOK_CONFIG.SECURITY.REQUIRED_HEADERS;
  for (const header of requiredHeaders) {
    if (!req.headers[header]) {
      errors.push(`Missing required header: ${header}`);
    } else {
      securityScore += 10;
    }
  }
  
  // Check content type
  const contentType = req.headers['content-type'];
  if (contentType && contentType.includes('application/json')) {
    securityScore += 10;
  } else {
    warnings.push('Content-Type is not application/json');
  }
  
  // Check user agent
  const userAgent = req.headers['user-agent'];
  if (userAgent) {
    securityScore += 10;
    if (userAgent.toLowerCase().includes('plaid')) {
      securityScore += 10;
    }
  } else {
    warnings.push('Missing User-Agent header');
  }
  
  // Check for suspicious headers
  const suspiciousHeaders = ['x-forwarded-for', 'x-real-ip'];
  for (const header of suspiciousHeaders) {
    if (req.headers[header]) {
      warnings.push(`Potentially suspicious header present: ${header}`);
    }
  }
  
  // Validate request method
  if (req.method !== 'POST') {
    errors.push(`Invalid HTTP method: ${req.method}. Expected POST.`);
  } else {
    securityScore += 10;
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    securityScore: Math.min(securityScore, 100) // Cap at 100
  };
}

/**
 * Generate security report for webhook request
 */
export function generateSecurityReport(req: any, verificationResult: any): {
  timestamp: string;
  clientIp: string;
  userAgent: string;
  securityLevel: string;
  verificationPassed: boolean;
  securityScore: number;
  threats: string[];
  recommendations: string[];
} {
  const validation = validateWebhookRequest(req);
  const clientIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.ip || 'unknown';
  const userAgent = req.headers['user-agent'] || 'unknown';
  
  const threats: string[] = [];
  const recommendations: string[] = [];
  
  // Analyze potential threats
  if (!verificationResult.isValid) {
    threats.push('Signature verification failed');
    recommendations.push('Verify webhook secret configuration');
  }
  
  if (validation.securityScore < 50) {
    threats.push('Low security score');
    recommendations.push('Ensure all required headers are present');
  }
  
  if (validation.errors.length > 0) {
    threats.push('Request validation errors');
    recommendations.push('Fix request format issues');
  }
  
  return {
    timestamp: new Date().toISOString(),
    clientIp,
    userAgent,
    securityLevel: verificationResult.securityLevel || 'unknown',
    verificationPassed: verificationResult.isValid,
    securityScore: validation.securityScore,
    threats,
    recommendations
  };
} 