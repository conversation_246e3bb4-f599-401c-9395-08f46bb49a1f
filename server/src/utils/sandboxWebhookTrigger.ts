/**
 * Sandbox Webhook Trigger Utility
 * Simple utility functions for triggering Plaid sandbox webhooks
 */

import { plaidClient } from '../config/plaidConfig';
import logger from './logger';

export interface SandboxTransferOptions {
  amount?: string;
  description?: string;
  type?: 'debit' | 'credit';
  network?: 'ach' | 'rtp' | 'wire';
  achClass?: 'web' | 'ppd' | 'ccd';
}

export type WebhookEventType = 
  | 'TRANSFER_EVENTS_UPDATE'
  | 'TRANSFER_POSTED' 
  | 'TRANSFER_SETTLED'
  | 'TRANSFER_FAILED'
  | 'TRANSFER_CANCELLED'
  | 'TRANSFER_RETURNED';

/**
 * Create a test transfer in sandbox environment
 */
export async function createSandboxTransfer(options: SandboxTransferOptions = {}): Promise<string> {
  const {
    amount = '10.00',
    description = 'Sandbox test transfer',
    type = 'debit',
    network = 'ach',
    achClass = 'web'
  } = options;

  try {
    logger.info('Creating sandbox test transfer', { amount, description, type });

    const transferResponse = await plaidClient.transferCreate({
      access_token: process.env.PLAID_BUSINESS_ACCESS_TOKEN!,
      account_id: process.env.PLAID_BUSINESS_ACCOUNT_ID!,
      type,
      network,
      amount,
      description,
      ach_class: achClass,
      user: {
        legal_name: 'Sandbox Test User',
      },
    });

    const transferId = transferResponse.data.transfer.id;
    logger.info('Sandbox transfer created successfully', { transferId, amount });
    
    return transferId;

  } catch (error) {
    logger.error('Failed to create sandbox transfer', { error, options });
    throw new Error(`Failed to create sandbox transfer: ${error.message}`);
  }
}

/**
 * Trigger a webhook event for a specific transfer
 */
export async function triggerWebhookEvent(
  transferId: string,
  webhookCode: WebhookEventType
): Promise<boolean> {
  try {
    logger.info('Triggering sandbox webhook', { transferId, webhookCode });

    const response = await plaidClient.sandboxTransferFireWebhook({
      transfer_id: transferId,
      webhook_code: webhookCode,
    });

    logger.info('Sandbox webhook triggered successfully', { 
      transferId, 
      webhookCode,
      response: response.data 
    });

    return true;

  } catch (error) {
    logger.error('Failed to trigger sandbox webhook', { 
      transferId, 
      webhookCode, 
      error: error.response?.data || error.message 
    });
    return false;
  }
}

/**
 * Create a transfer and immediately trigger a webhook event
 */
export async function createTransferAndTriggerWebhook(
  webhookCode: WebhookEventType = 'TRANSFER_EVENTS_UPDATE',
  transferOptions: SandboxTransferOptions = {}
): Promise<{ transferId: string; webhookTriggered: boolean }> {
  try {
    // Create the transfer
    const transferId = await createSandboxTransfer(transferOptions);
    
    // Wait a moment for the transfer to be processed
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Trigger the webhook
    const webhookTriggered = await triggerWebhookEvent(transferId, webhookCode);
    
    return { transferId, webhookTriggered };

  } catch (error) {
    logger.error('Failed to create transfer and trigger webhook', { 
      webhookCode, 
      transferOptions, 
      error 
    });
    throw error;
  }
}

/**
 * Simulate a complete transfer lifecycle with multiple webhook events
 */
export async function simulateTransferLifecycle(
  transferId?: string,
  events: WebhookEventType[] = ['TRANSFER_EVENTS_UPDATE', 'TRANSFER_POSTED', 'TRANSFER_SETTLED'],
  delayBetweenEvents: number = 2000
): Promise<{ transferId: string; results: Array<{ event: WebhookEventType; success: boolean }> }> {
  try {
    // Create transfer if not provided
    const finalTransferId = transferId || await createSandboxTransfer();
    
    logger.info('Starting transfer lifecycle simulation', { 
      transferId: finalTransferId, 
      events 
    });

    const results: Array<{ event: WebhookEventType; success: boolean }> = [];

    // Trigger each event in sequence
    for (let i = 0; i < events.length; i++) {
      const event = events[i];
      const success = await triggerWebhookEvent(finalTransferId, event);
      results.push({ event, success });

      // Wait between events (except for the last one)
      if (i < events.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delayBetweenEvents));
      }
    }

    logger.info('Transfer lifecycle simulation completed', { 
      transferId: finalTransferId, 
      results 
    });

    return { transferId: finalTransferId, results };

  } catch (error) {
    logger.error('Transfer lifecycle simulation failed', { error });
    throw error;
  }
}

/**
 * Batch trigger multiple webhook events for testing
 */
export async function batchTriggerWebhooks(
  scenarios: Array<{
    transferOptions?: SandboxTransferOptions;
    webhookEvents: WebhookEventType[];
    description?: string;
  }>
): Promise<Array<{
  description: string;
  transferId: string;
  results: Array<{ event: WebhookEventType; success: boolean }>;
}>> {
  const batchResults = [];

  for (const scenario of scenarios) {
    try {
      logger.info('Starting webhook scenario', { description: scenario.description });

      const result = await simulateTransferLifecycle(
        undefined, // Create new transfer for each scenario
        scenario.webhookEvents,
        1500 // Shorter delay for batch processing
      );

      batchResults.push({
        description: scenario.description || 'Unnamed scenario',
        transferId: result.transferId,
        results: result.results
      });

      // Wait between scenarios
      await new Promise(resolve => setTimeout(resolve, 3000));

    } catch (error) {
      logger.error('Webhook scenario failed', { 
        description: scenario.description, 
        error 
      });
      
      batchResults.push({
        description: scenario.description || 'Unnamed scenario',
        transferId: 'failed',
        results: scenario.webhookEvents.map(event => ({ event, success: false }))
      });
    }
  }

  return batchResults;
}

/**
 * Quick helper to trigger a single webhook event
 */
export async function quickTrigger(
  webhookCode: WebhookEventType = 'TRANSFER_EVENTS_UPDATE',
  amount: string = '5.00'
): Promise<string> {
  const { transferId } = await createTransferAndTriggerWebhook(webhookCode, { amount });
  return transferId;
}

/**
 * Predefined test scenarios for common use cases
 */
export const TEST_SCENARIOS = {
  SUCCESSFUL_TRANSFER: {
    transferOptions: { amount: '10.00', description: 'Successful transfer test' },
    webhookEvents: ['TRANSFER_EVENTS_UPDATE', 'TRANSFER_POSTED', 'TRANSFER_SETTLED'] as WebhookEventType[],
    description: 'Successful Transfer Flow'
  },
  
  FAILED_TRANSFER: {
    transferOptions: { amount: '25.00', description: 'Failed transfer test' },
    webhookEvents: ['TRANSFER_EVENTS_UPDATE', 'TRANSFER_FAILED'] as WebhookEventType[],
    description: 'Failed Transfer Flow'
  },
  
  CANCELLED_TRANSFER: {
    transferOptions: { amount: '50.00', description: 'Cancelled transfer test' },
    webhookEvents: ['TRANSFER_EVENTS_UPDATE', 'TRANSFER_CANCELLED'] as WebhookEventType[],
    description: 'Cancelled Transfer Flow'
  },
  
  RETURNED_TRANSFER: {
    transferOptions: { amount: '15.00', description: 'Returned transfer test' },
    webhookEvents: ['TRANSFER_EVENTS_UPDATE', 'TRANSFER_POSTED', 'TRANSFER_RETURNED'] as WebhookEventType[],
    description: 'Returned Transfer Flow'
  }
};

/**
 * Run all predefined test scenarios
 */
export async function runAllTestScenarios(): Promise<any> {
  const scenarios = Object.values(TEST_SCENARIOS);
  return await batchTriggerWebhooks(scenarios);
}