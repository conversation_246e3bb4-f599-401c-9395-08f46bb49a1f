import Stripe from 'stripe';
import logger from './logger';

/**
 * Custom error classes for Stripe integration
 */
export class StripeError extends Error {
  public readonly code: string;
  public readonly type: string;
  public readonly statusCode: number;
  public readonly userMessage: string;
  public readonly originalError?: Stripe.errors.StripeError;

  constructor(
    code: string,
    type: string,
    message: string,
    userMessage: string,
    statusCode: number = 500,
    originalError?: Stripe.errors.StripeError
  ) {
    super(message);
    this.name = 'StripeError';
    this.code = code;
    this.type = type;
    this.statusCode = statusCode;
    this.userMessage = userMessage;
    this.originalError = originalError;
  }
}

export class StripePaymentError extends StripeError {
  constructor(message: string, userMessage: string, originalError?: Stripe.errors.StripeError) {
    super('PAYMENT_ERROR', 'payment', message, userMessage, 400, originalError);
    this.name = 'StripePaymentError';
  }
}

export class StripeCustomerError extends StripeError {
  constructor(message: string, userMessage: string, originalError?: Stripe.errors.StripeError) {
    super('CUSTOMER_ERROR', 'customer', message, userMessage, 400, originalError);
    this.name = 'StripeCustomerError';
  }
}

export class StripePayoutError extends StripeError {
  constructor(message: string, userMessage: string, originalError?: Stripe.errors.StripeError) {
    super('PAYOUT_ERROR', 'payout', message, userMessage, 400, originalError);
    this.name = 'StripePayoutError';
  }
}

export class StripeWebhookError extends StripeError {
  constructor(message: string, userMessage: string, originalError?: Stripe.errors.StripeError) {
    super('WEBHOOK_ERROR', 'webhook', message, userMessage, 400, originalError);
    this.name = 'StripeWebhookError';
  }
}

/**
 * Stripe error handler utility class
 */
export class StripeErrorHandler {
  /**
   * Handle and transform Stripe errors into application-specific errors
   */
  static handleStripeError(error: Stripe.errors.StripeError, context?: string): StripeError {
    const contextInfo = context ? ` (${context})` : '';
    
    logger.error(`Stripe API error${contextInfo}`, {
      type: error.type,
      code: error.code,
      message: error.message,
      statusCode: error.statusCode,
      requestId: error.requestId,
      context
    });

    // Handle different Stripe error types based on the error instance
    if (error instanceof Stripe.errors.StripeCardError) {
      return this.handleCardError(error);
    } else if (error instanceof Stripe.errors.StripeInvalidRequestError) {
      return this.handleInvalidRequestError(error);
    } else if (error instanceof Stripe.errors.StripeAPIError) {
      return this.handleApiError(error);
    } else if (error instanceof Stripe.errors.StripeConnectionError) {
      return this.handleConnectionError(error);
    } else if (error instanceof Stripe.errors.StripeAuthenticationError) {
      return this.handleAuthenticationError(error);
    } else if (error instanceof Stripe.errors.StripeRateLimitError) {
      return this.handleRateLimitError(error);
    } else if (error instanceof Stripe.errors.StripeIdempotencyError) {
      return this.handleIdempotencyError(error);
    } else {
      return this.handleUnknownError(error);
    }
  }

  /**
   * Handle card-related errors (payment failures, insufficient funds, etc.)
   */
  private static handleCardError(error: Stripe.errors.StripeError): StripePaymentError {
    const code = error.code;
    let userMessage = 'Your payment could not be processed. Please try again or use a different payment method.';

    switch (code) {
      case 'insufficient_funds':
        userMessage = 'Insufficient funds in your bank account. Please ensure you have enough funds and try again.';
        break;
      case 'account_closed':
        userMessage = 'The bank account appears to be closed. Please use a different account.';
        break;
      case 'account_frozen':
        userMessage = 'The bank account is frozen. Please contact your bank or use a different account.';
        break;
      case 'invalid_account_number':
        userMessage = 'The bank account information is invalid. Please check your account details.';
        break;
      case 'debit_not_authorized':
        userMessage = 'The bank account is not authorized for ACH debits. Please contact your bank.';
        break;
      case 'bank_account_verification_failed':
        userMessage = 'Bank account verification failed. Please verify your account information.';
        break;
      case 'payment_method_unactivated':
        userMessage = 'Your payment method needs to be activated. Please contact support.';
        break;
      case 'payment_method_unavailable':
        userMessage = 'This payment method is currently unavailable. Please try again later.';
        break;
      default:
        userMessage = error.message || userMessage;
    }

    return new StripePaymentError(
      `Card error: ${error.message}`,
      userMessage,
      error
    );
  }

  /**
   * Handle invalid request errors (validation, missing parameters, etc.)
   */
  private static handleInvalidRequestError(error: Stripe.errors.StripeError): StripeError {
    const code = error.code;
    let userMessage = 'There was an issue with your request. Please try again.';

    switch (code) {
      case 'amount_too_small':
        userMessage = 'The amount is too small. Please enter a larger amount.';
        break;
      case 'amount_too_large':
        userMessage = 'The amount is too large. Please enter a smaller amount.';
        break;
      case 'balance_insufficient':
        userMessage = 'Insufficient balance for this transaction.';
        break;
      case 'customer_not_found':
        userMessage = 'Customer account not found. Please contact support.';
        break;
      case 'payment_method_not_available':
        userMessage = 'The selected payment method is not available.';
        break;
      case 'currency_not_supported':
        userMessage = 'The currency is not supported for this transaction.';
        break;
      default:
        // Don't expose internal validation errors to users
        userMessage = 'There was an issue processing your request. Please try again.';
    }

    return new StripeError(
      'INVALID_REQUEST',
      'validation',
      `Invalid request: ${error.message}`,
      userMessage,
      400,
      error
    );
  }

  /**
   * Handle API errors (Stripe service issues)
   */
  private static handleApiError(error: Stripe.errors.StripeError): StripeError {
    return new StripeError(
      'API_ERROR',
      'service',
      `Stripe API error: ${error.message}`,
      'Payment service is temporarily unavailable. Please try again in a few minutes.',
      503,
      error
    );
  }

  /**
   * Handle connection errors (network issues)
   */
  private static handleConnectionError(error: Stripe.errors.StripeError): StripeError {
    return new StripeError(
      'CONNECTION_ERROR',
      'network',
      `Connection error: ${error.message}`,
      'Network error occurred. Please check your connection and try again.',
      503,
      error
    );
  }

  /**
   * Handle authentication errors (API key issues)
   */
  private static handleAuthenticationError(error: Stripe.errors.StripeError): StripeError {
    return new StripeError(
      'AUTHENTICATION_ERROR',
      'auth',
      `Authentication error: ${error.message}`,
      'Payment service authentication failed. Please contact support.',
      401,
      error
    );
  }

  /**
   * Handle rate limit errors
   */
  private static handleRateLimitError(error: Stripe.errors.StripeError): StripeError {
    return new StripeError(
      'RATE_LIMIT_ERROR',
      'rate_limit',
      `Rate limit exceeded: ${error.message}`,
      'Too many requests. Please wait a moment and try again.',
      429,
      error
    );
  }

  /**
   * Handle idempotency errors (duplicate requests)
   */
  private static handleIdempotencyError(error: Stripe.errors.StripeError): StripeError {
    return new StripeError(
      'IDEMPOTENCY_ERROR',
      'duplicate',
      `Idempotency error: ${error.message}`,
      'This request has already been processed. Please check your transaction history.',
      409,
      error
    );
  }

  /**
   * Handle unknown errors
   */
  private static handleUnknownError(error: Stripe.errors.StripeError): StripeError {
    return new StripeError(
      'UNKNOWN_ERROR',
      'unknown',
      `Unknown Stripe error: ${error.message}`,
      'An unexpected error occurred. Please try again or contact support.',
      500,
      error
    );
  }

  /**
   * Retry logic for transient errors
   */
  static shouldRetry(error: StripeError): boolean {
    const retryableTypes = ['service', 'network', 'rate_limit'];
    const retryableCodes = ['API_ERROR', 'CONNECTION_ERROR', 'RATE_LIMIT_ERROR'];
    
    return retryableTypes.includes(error.type) || retryableCodes.includes(error.code);
  }

  /**
   * Get retry delay based on error type
   */
  static getRetryDelay(attempt: number, error: StripeError): number {
    const baseDelay = 1000; // 1 second
    const maxDelay = 30000; // 30 seconds
    
    let delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff
    
    // Add jitter to prevent thundering herd
    delay += Math.random() * 1000;
    
    // Special handling for rate limit errors
    if (error.code === 'RATE_LIMIT_ERROR') {
      delay = Math.max(delay, 5000); // Minimum 5 seconds for rate limits
    }
    
    return Math.min(delay, maxDelay);
  }

  /**
   * Log error with appropriate level based on error type
   */
  static logError(error: StripeError, context?: string): void {
    const logData = {
      code: error.code,
      type: error.type,
      message: error.message,
      userMessage: error.userMessage,
      statusCode: error.statusCode,
      context,
      requestId: error.originalError?.requestId
    };

    // Log at different levels based on error severity
    switch (error.type) {
      case 'payment':
      case 'validation':
        logger.warn('Stripe error (user-related)', logData);
        break;
      case 'service':
      case 'network':
      case 'auth':
        logger.error('Stripe error (service-related)', logData);
        break;
      case 'rate_limit':
        logger.warn('Stripe rate limit exceeded', logData);
        break;
      default:
        logger.error('Stripe error (unknown)', logData);
    }
  }
}

/**
 * Utility function to safely execute Stripe operations with error handling
 */
export async function executeStripeOperation<T>(
  operation: () => Promise<T>,
  context: string,
  maxRetries: number = 3
): Promise<T> {
  let lastError: StripeError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (error instanceof Stripe.errors.StripeError) {
        lastError = StripeErrorHandler.handleStripeError(error, context);
        StripeErrorHandler.logError(lastError, context);
        
        // Don't retry if it's not a retryable error or if it's the last attempt
        if (!StripeErrorHandler.shouldRetry(lastError) || attempt === maxRetries) {
          throw lastError;
        }
        
        // Wait before retrying
        const delay = StripeErrorHandler.getRetryDelay(attempt, lastError);
        logger.info(`Retrying Stripe operation after ${delay}ms`, {
          context,
          attempt,
          maxRetries,
          errorCode: lastError.code
        });
        
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        // Re-throw non-Stripe errors
        throw error;
      }
    }
  }
  
  throw lastError!;
}

/**
 * Webhook signature verification with error handling
 */
export function verifyStripeWebhookSignature(
  payload: string,
  signature: string,
  secret: string
): Stripe.Event {
  try {
    return Stripe.webhooks.constructEvent(payload, signature, secret);
  } catch (error) {
    if (error instanceof Error) {
      throw new StripeWebhookError(
        `Webhook signature verification failed: ${error.message}`,
        'Invalid webhook signature',
        error as Stripe.errors.StripeError
      );
    }
    throw error;
  }
}