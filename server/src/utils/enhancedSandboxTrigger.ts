/**
 * Enhanced Sandbox Transaction Webhook Trigger
 * 
 * This utility provides type-safe functions for triggering Plaid sandbox webhooks
 * specifically for transaction and transfer events. It fixes TypeScript issues
 * and provides a clean interface for testing webhook functionality.
 */

// Note: Using string literals instead of Plaid enums due to type compatibility issues
import { plaidClient } from '../config/plaidConfig';
import { WEBHOOK_CONFIG } from '../config/webhookConfig';
import logger from './logger';

// Type-safe webhook event types
export type TransferWebhookEvent = 
  | 'TRANSFER_EVENTS_UPDATE'
  | 'TRANSFER_POSTED' 
  | 'TRANSFER_SETTLED'
  | 'TRANSFER_FAILED'
  | 'TRANSFER_CANCELLED'
  | 'TRANSFER_RETURNED';

// Transfer creation options with string literal types
export interface SandboxTransferOptions {
  amount?: string;
  description?: string;
  type?: 'debit' | 'credit';
  network?: 'ach' | 'rtp' | 'wire';
  achClass?: 'web' | 'ppd' | 'ccd';
  userLegalName?: string;
}

// Webhook trigger result
export interface WebhookTriggerResult {
  success: boolean;
  transferId?: string;
  webhookCode?: TransferWebhookEvent;
  error?: string;
  timestamp: Date;
}

// Transaction scenario definition
export interface TransactionScenario {
  name: string;
  transferOptions: SandboxTransferOptions;
  webhookEvents: TransferWebhookEvent[];
  description: string;
  delayBetweenEvents?: number;
}

/**
 * Create a test transfer in sandbox environment with proper type safety
 * This follows the Plaid pattern of creating an authorization first, then the transfer
 */
export async function createSandboxTransfer(
  options: SandboxTransferOptions = {}
): Promise<string> {
  const {
    amount = '10.00',
    description = 'Sandbox test transfer',
    type = 'debit',
    network = 'ach',
    achClass = 'web',
    userLegalName = 'Sandbox Test User'
  } = options;

  try {
    logger.info('Creating sandbox test transfer', { 
      amount, 
      description, 
      type, 
      network,
      achClass 
    });

    // Validate required environment variables
    if (!process.env.PLAID_BUSINESS_ACCESS_TOKEN) {
      throw new Error('PLAID_BUSINESS_ACCESS_TOKEN is required');
    }
    if (!process.env.PLAID_BUSINESS_ACCOUNT_ID) {
      throw new Error('PLAID_BUSINESS_ACCOUNT_ID is required');
    }

    // Step 1: Create transfer authorization first
    logger.info('Creating transfer authorization');
    const authResponse = await plaidClient.transferAuthorizationCreate({
      access_token: process.env.PLAID_BUSINESS_ACCESS_TOKEN,
      account_id: process.env.PLAID_BUSINESS_ACCOUNT_ID,
      type: type as any,
      network: network as any,
      amount,
      ach_class: achClass as any,
      user: {
        legal_name: userLegalName,
      },
    } as any);

    const authorizationId = authResponse.data.authorization.id;
    const decision = authResponse.data.authorization.decision;

    logger.info('Transfer authorization created', { 
      authorizationId, 
      decision 
    });

    if (decision !== 'approved') {
      throw new Error(`Transfer authorization was ${decision}: ${authResponse.data.authorization.decision_rationale?.description || 'No reason provided'}`);
    }

    // Step 2: Create the actual transfer using the authorization
    logger.info('Creating transfer with authorization', { authorizationId });
    const transferResponse = await plaidClient.transferCreate({
      access_token: process.env.PLAID_BUSINESS_ACCESS_TOKEN,
      account_id: process.env.PLAID_BUSINESS_ACCOUNT_ID,
      authorization_id: authorizationId,
      description,
    } as any);

    const transferId = transferResponse.data.transfer.id;
    logger.info('Sandbox transfer created successfully', { 
      transferId, 
      authorizationId,
      amount, 
      type,
      network 
    });
    
    return transferId;

  } catch (error: any) {
    logger.error('Failed to create sandbox transfer', { 
      error: error.message || error,
      plaidError: error.response?.data,
      options 
    });
    throw new Error(`Failed to create sandbox transfer: ${error.response?.data?.error_message || error.message || error}`);
  }
}

/**
 * Trigger a webhook event for a specific transfer with proper error handling
 */
export async function triggerTransferWebhook(
  transferId: string,
  webhookCode: TransferWebhookEvent
): Promise<WebhookTriggerResult> {
  const result: WebhookTriggerResult = {
    success: false,
    transferId,
    webhookCode,
    timestamp: new Date()
  };

  try {
    logger.info('Triggering sandbox webhook', { transferId, webhookCode });

    // Map our webhook codes to Plaid's enum values
    const plaidWebhookCode = webhookCode as any; // Plaid's enum should match our string literals

    const response = await plaidClient.sandboxTransferFireWebhook({
      transfer_id: transferId,
      webhook_code: plaidWebhookCode,
    } as any);

    logger.info('Sandbox webhook triggered successfully', { 
      transferId, 
      webhookCode,
      response: response.data 
    });

    result.success = true;
    return result;

  } catch (error: any) {
    const errorMessage = error.response?.data?.error_message || error.message || 'Unknown error';
    
    logger.error('Failed to trigger sandbox webhook', { 
      transferId, 
      webhookCode, 
      error: errorMessage,
      errorCode: error.response?.data?.error_code
    });

    result.error = errorMessage;
    return result;
  }
}

/**
 * Create a transfer and immediately trigger a webhook event
 */
export async function createTransferAndTriggerWebhook(
  webhookCode: TransferWebhookEvent = 'TRANSFER_EVENTS_UPDATE',
  transferOptions: SandboxTransferOptions = {}
): Promise<{ transferId: string; webhookResult: WebhookTriggerResult }> {
  try {
    // Create the transfer
    const transferId = await createSandboxTransfer(transferOptions);
    
    // Wait a moment for the transfer to be processed
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Trigger the webhook
    const webhookResult = await triggerTransferWebhook(transferId, webhookCode);
    
    return { transferId, webhookResult };

  } catch (error: any) {
    logger.error('Failed to create transfer and trigger webhook', { 
      webhookCode, 
      transferOptions, 
      error: error.message || error
    });
    throw error;
  }
}

/**
 * Simulate a complete transfer lifecycle with multiple webhook events
 */
export async function simulateTransferLifecycle(
  transferId?: string,
  events: TransferWebhookEvent[] = ['TRANSFER_EVENTS_UPDATE', 'TRANSFER_POSTED', 'TRANSFER_SETTLED'],
  delayBetweenEvents: number = 2000,
  transferOptions: SandboxTransferOptions = {}
): Promise<{
  transferId: string;
  results: WebhookTriggerResult[];
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}> {
  try {
    // Create transfer if not provided
    const finalTransferId = transferId || await createSandboxTransfer(transferOptions);
    
    logger.info('Starting transfer lifecycle simulation', { 
      transferId: finalTransferId, 
      events,
      delayBetweenEvents
    });

    const results: WebhookTriggerResult[] = [];

    // Trigger each event in sequence
    for (let i = 0; i < events.length; i++) {
      const event = events[i];
      const result = await triggerTransferWebhook(finalTransferId, event);
      results.push(result);

      // Wait between events (except for the last one)
      if (i < events.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delayBetweenEvents));
      }
    }

    const summary = {
      total: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length
    };

    logger.info('Transfer lifecycle simulation completed', { 
      transferId: finalTransferId, 
      summary
    });

    return { transferId: finalTransferId, results, summary };

  } catch (error: any) {
    logger.error('Transfer lifecycle simulation failed', { 
      error: error.message || error 
    });
    throw error;
  }
}

/**
 * Predefined transaction scenarios for comprehensive testing
 */
export const TRANSACTION_SCENARIOS: TransactionScenario[] = [
  {
    name: 'successful_small_transfer',
    description: 'Small successful transfer with complete lifecycle',
    transferOptions: { 
      amount: '5.00', 
      description: 'Small success', // Max 15 chars
      type: 'debit'
    },
    webhookEvents: ['TRANSFER_EVENTS_UPDATE', 'TRANSFER_POSTED', 'TRANSFER_SETTLED'],
    delayBetweenEvents: 1500
  },
  {
    name: 'failed_medium_transfer',
    description: 'Medium transfer that fails during processing',
    transferOptions: { 
      amount: '25.00', 
      description: 'Medium fail', // Max 15 chars
      type: 'debit'
    },
    webhookEvents: ['TRANSFER_EVENTS_UPDATE', 'TRANSFER_FAILED'],
    delayBetweenEvents: 2000
  },
  {
    name: 'cancelled_large_transfer',
    description: 'Large transfer that gets cancelled',
    transferOptions: { 
      amount: '100.00', 
      description: 'Large cancel', // Max 15 chars
      type: 'debit'
    },
    webhookEvents: ['TRANSFER_EVENTS_UPDATE', 'TRANSFER_CANCELLED'],
    delayBetweenEvents: 1000
  },
  {
    name: 'returned_transfer',
    description: 'Transfer that gets returned after posting',
    transferOptions: { 
      amount: '15.00', 
      description: 'Return test', // Max 15 chars
      type: 'debit'
    },
    webhookEvents: ['TRANSFER_EVENTS_UPDATE', 'TRANSFER_POSTED', 'TRANSFER_RETURNED'],
    delayBetweenEvents: 2500
  },
  {
    name: 'credit_transfer',
    description: 'Credit transfer (money coming in)',
    transferOptions: { 
      amount: '20.00', 
      description: 'Credit test', // Max 15 chars
      type: 'credit'
    },
    webhookEvents: ['TRANSFER_EVENTS_UPDATE', 'TRANSFER_POSTED', 'TRANSFER_SETTLED'],
    delayBetweenEvents: 1800
  }
];

/**
 * Run multiple transaction scenarios for comprehensive testing
 */
export async function runTransactionScenarios(
  scenarios: TransactionScenario[] = TRANSACTION_SCENARIOS,
  delayBetweenScenarios: number = 5000
): Promise<Array<{
  scenario: TransactionScenario;
  transferId: string;
  results: WebhookTriggerResult[];
  summary: { total: number; successful: number; failed: number };
}>> {
  const scenarioResults = [];

  logger.info('Starting transaction scenarios', { 
    totalScenarios: scenarios.length,
    webhookUrl: WEBHOOK_CONFIG.FULL_URL
  });

  for (let i = 0; i < scenarios.length; i++) {
    const scenario = scenarios[i];
    
    try {
      logger.info(`Running scenario ${i + 1}/${scenarios.length}: ${scenario.name}`, {
        description: scenario.description
      });

      const result = await simulateTransferLifecycle(
        undefined, // Create new transfer for each scenario
        scenario.webhookEvents,
        scenario.delayBetweenEvents || 2000,
        scenario.transferOptions
      );

      scenarioResults.push({
        scenario,
        transferId: result.transferId,
        results: result.results,
        summary: result.summary
      });

      // Wait between scenarios (except for the last one)
      if (i < scenarios.length - 1) {
        logger.info(`Waiting ${delayBetweenScenarios}ms before next scenario...`);
        await new Promise(resolve => setTimeout(resolve, delayBetweenScenarios));
      }

    } catch (error: any) {
      logger.error(`Scenario ${scenario.name} failed`, { 
        error: error.message || error
      });
      
      scenarioResults.push({
        scenario,
        transferId: 'failed',
        results: [],
        summary: { total: 0, successful: 0, failed: scenario.webhookEvents.length }
      });
    }
  }

  // Log overall summary
  const overallSummary = scenarioResults.reduce(
    (acc, result) => ({
      totalScenarios: acc.totalScenarios + 1,
      successfulScenarios: acc.successfulScenarios + (result.summary.failed === 0 ? 1 : 0),
      totalWebhooks: acc.totalWebhooks + result.summary.total,
      successfulWebhooks: acc.successfulWebhooks + result.summary.successful
    }),
    { totalScenarios: 0, successfulScenarios: 0, totalWebhooks: 0, successfulWebhooks: 0 }
  );

  logger.info('Transaction scenarios completed', { overallSummary });

  return scenarioResults;
}

/**
 * Quick helper to trigger a single webhook event with minimal setup
 */
export async function quickTriggerWebhook(
  webhookCode: TransferWebhookEvent = 'TRANSFER_EVENTS_UPDATE',
  amount: string = '5.00',
  description?: string
): Promise<{ transferId: string; success: boolean; error?: string }> {
  try {
    // Ensure description is max 15 characters for Plaid API
    const shortDescription = description || `Test ${amount}`;
    const finalDescription = shortDescription.length > 15 
      ? shortDescription.substring(0, 15) 
      : shortDescription;

    const { transferId, webhookResult } = await createTransferAndTriggerWebhook(
      webhookCode,
      { 
        amount, 
        description: finalDescription
      }
    );

    return {
      transferId,
      success: webhookResult.success,
      error: webhookResult.error
    };

  } catch (error: any) {
    return {
      transferId: 'failed',
      success: false,
      error: error.message || error
    };
  }
}

/**
 * Validate webhook configuration before running tests
 */
export function validateWebhookConfiguration(): {
  valid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check required environment variables
  if (!process.env.PLAID_BUSINESS_ACCESS_TOKEN) {
    errors.push('PLAID_BUSINESS_ACCESS_TOKEN is not set');
  }
  if (!process.env.PLAID_BUSINESS_ACCOUNT_ID) {
    errors.push('PLAID_BUSINESS_ACCOUNT_ID is not set');
  }
  if (!process.env.PLAID_CLIENT_ID) {
    errors.push('PLAID_CLIENT_ID is not set');
  }
  if (!process.env.PLAID_SECRET) {
    errors.push('PLAID_SECRET is not set');
  }

  // Check webhook configuration
  if (!WEBHOOK_CONFIG.FULL_URL) {
    errors.push('Webhook URL is not configured');
  } else if (!WEBHOOK_CONFIG.FULL_URL.includes('ngrok')) {
    warnings.push('Webhook URL does not appear to be an ngrok URL - make sure it\'s accessible from Plaid');
  }

  // Check environment
  if (process.env.PLAID_ENV !== 'sandbox') {
    warnings.push('PLAID_ENV is not set to sandbox - webhook triggers only work in sandbox');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Display configuration status and help information
 */
export function displayConfigurationStatus(): void {
  const config = validateWebhookConfiguration();
  
  console.log('🔧 Enhanced Sandbox Transaction Webhook Trigger');
  console.log('===============================================');
  console.log('');
  
  console.log('📋 Configuration Status:');
  console.log(`   Webhook URL: ${WEBHOOK_CONFIG.FULL_URL || 'NOT SET'}`);
  console.log(`   Plaid Environment: ${process.env.PLAID_ENV || 'NOT SET'}`);
  console.log(`   Access Token: ${process.env.PLAID_BUSINESS_ACCESS_TOKEN ? 'SET' : 'NOT SET'}`);
  console.log(`   Account ID: ${process.env.PLAID_BUSINESS_ACCOUNT_ID ? 'SET' : 'NOT SET'}`);
  console.log('');

  if (config.errors.length > 0) {
    console.log('❌ Configuration Errors:');
    config.errors.forEach(error => console.log(`   • ${error}`));
    console.log('');
  }

  if (config.warnings.length > 0) {
    console.log('⚠️  Configuration Warnings:');
    config.warnings.forEach(warning => console.log(`   • ${warning}`));
    console.log('');
  }

  if (config.valid) {
    console.log('✅ Configuration is valid - ready to trigger webhooks!');
  } else {
    console.log('❌ Configuration has errors - please fix before running webhook tests');
  }
  
  console.log('');
  console.log('💡 Available Functions:');
  console.log('   • quickTriggerWebhook() - Trigger a single webhook');
  console.log('   • simulateTransferLifecycle() - Run complete transfer flow');
  console.log('   • runTransactionScenarios() - Run all predefined scenarios');
  console.log('   • createSandboxTransfer() - Create a test transfer');
  console.log('   • triggerTransferWebhook() - Trigger webhook for existing transfer');
}