import cron from 'node-cron';
import { syncAllPendingTransfers, getSyncStatistics } from '../services/plaidStatusSyncService';
import logger from '../utils/logger';

/**
 * Configuration for cron job behavior
 * MIGRATION COMPLETE: Cron jobs are DISABLED by default in favor of real-time webhook processing
 * This represents the completed migration from polling-based to event-driven architecture
 * 
 * WEBHOOK-ONLY PROCESSING is now the default and recommended mode
 * Cron jobs serve only as emergency fallback or manual testing tools
 */
export const CRON_CONFIG = {
  // Enable/disable cron jobs - DISABLED by default for webhook-only processing
  // Only enable for debugging, testing, or emergency fallback scenarios
  // MIGRATION: Default is now false to complete webhook-only transition
  ENABLED: process.env.PLAID_CRON_ENABLED === 'true',
  
  // Fallback mode - automatically enable cron jobs if webhooks are consistently failing
  // This provides a safety net for webhook failures
  // MIGRATION: Fallback is enabled by default for reliability
  FALLBACK_MODE: process.env.PLAID_CRON_FALLBACK_MODE !== 'false',
  
  // Processing mode from webhook config
  // MIGRATION: Default is now webhook-only to complete the transition
  PROCESSING_MODE: process.env.PLAID_PROCESSING_MODE || 'webhook-only',
  
  // Sync intervals (in minutes) - only used when cron jobs are enabled
  SYNC_INTERVAL: parseInt(process.env.PLAID_SYNC_INTERVAL || '5'),
  SMART_SYNC_INTERVAL: parseInt(process.env.PLAID_SMART_SYNC_INTERVAL || '10'),
  STATS_INTERVAL: parseInt(process.env.PLAID_STATS_INTERVAL || '60'),
  
  // Webhook health check settings for fallback activation
  WEBHOOK_FAILURE_THRESHOLD: parseInt(process.env.WEBHOOK_FAILURE_THRESHOLD || '5'),
  WEBHOOK_TIMEOUT_MINUTES: parseInt(process.env.WEBHOOK_TIMEOUT_MINUTES || '15'),
  
  // Get human-readable processing mode description
  get PROCESSING_MODE_DESCRIPTION(): string {
    switch (this.PROCESSING_MODE) {
      case 'webhook-only':
        return 'Real-time webhook processing only (RECOMMENDED)';
      case 'hybrid':
        return 'Webhooks with cron job fallback';
      case 'cron-only':
        return 'Legacy cron job processing (DEPRECATED)';
      default:
        return 'Unknown processing mode';
    }
  }
};

// Track cron job instances for management
let cronJobs: any[] = [];
let isCronActive = false;

/**
 * Set up cron jobs for Plaid transfer synchronization
 * MIGRATION: Now configurable and DISABLED by default in favor of real-time webhook processing
 * This function represents the transition from polling-based to event-driven architecture
 */
export function setupPlaidSyncCron() {
  // Log comprehensive configuration on startup
  logger.info('=== PLAID PROCESSING SYSTEM CONFIGURATION ===');
  logger.info('Processing Mode:', {
    mode: CRON_CONFIG.PROCESSING_MODE,
    description: CRON_CONFIG.PROCESSING_MODE_DESCRIPTION
  });
  
  logger.info('Cron Job Configuration:', {
    enabled: CRON_CONFIG.ENABLED,
    fallbackMode: CRON_CONFIG.FALLBACK_MODE,
    syncInterval: CRON_CONFIG.SYNC_INTERVAL,
    smartSyncInterval: CRON_CONFIG.SMART_SYNC_INTERVAL,
    statsInterval: CRON_CONFIG.STATS_INTERVAL
  });

  // Handle different processing modes
  if (!CRON_CONFIG.ENABLED) {
    if (CRON_CONFIG.PROCESSING_MODE === 'webhook-only') {
      logger.info('✅ WEBHOOK-ONLY PROCESSING MODE ACTIVE');
      logger.info('🚀 All Plaid transfer status updates will be processed via real-time webhooks');
      logger.info('⚡ This eliminates the 5-10 minute delays from the previous cron job system');
      logger.info('🔧 Cron jobs are DISABLED and will only activate as emergency fallback');
      
      if (CRON_CONFIG.FALLBACK_MODE) {
        logger.info('🛡️  Fallback mode is ENABLED - cron jobs will auto-activate if webhooks fail');
        logger.info(`📊 Fallback triggers: ${CRON_CONFIG.WEBHOOK_FAILURE_THRESHOLD} failures or ${CRON_CONFIG.WEBHOOK_TIMEOUT_MINUTES}min timeout`);
      } else {
        logger.warn('⚠️  Fallback mode is DISABLED - no automatic cron job activation');
      }
    } else {
      logger.info('Plaid sync cron jobs are DISABLED');
      logger.info('Current processing mode:', CRON_CONFIG.PROCESSING_MODE);
    }
    
    logger.info('💡 To manually enable cron jobs, set PLAID_CRON_ENABLED=true in environment variables');
    logger.info('💡 To test manual sync, use the manualSync() function');
    return;
  }

  // Cron jobs are enabled - log warning and start
  logger.warn('⚠️  CRON JOBS ARE ENABLED - This may indicate fallback mode or debugging');
  logger.warn('🔄 Starting cron job scheduling...');
  startCronJobs();
}

/**
 * Start cron jobs (internal function)
 */
function startCronJobs() {
  if (isCronActive) {
    logger.warn('Cron jobs are already active');
    return;
  }

  // Sync pending transfers
  const syncJob = cron.schedule(`*/${CRON_CONFIG.SYNC_INTERVAL} * * * *`, async () => {
    try {
      logger.info('Starting scheduled Plaid transfer sync');
      const result = await syncAllPendingTransfers();
      
      logger.info('Scheduled sync completed', {
        synced: result.synced,
        confirmed: result.confirmed,
        failed: result.failed,
        errors: result.errors,
        totalProcessed: result.totalProcessed
      });
    } catch (error) {
      logger.error('Error in scheduled Plaid sync', { error });
    }
  }, {
    timezone: 'UTC'
  });

  // Smart sync for API vs Dashboard mismatches
  const smartSyncJob = cron.schedule(`*/${CRON_CONFIG.SMART_SYNC_INTERVAL} * * * *`, async () => {
    try {
      logger.info('Starting scheduled smart sync');
      const { smartSyncTransfers } = await import('../services/enhancedPlaidSyncService');
      const result = await smartSyncTransfers();
      
      logger.info('Smart sync completed', {
        processed: result.processed,
        updated: result.updated,
        mismatches: result.mismatches.length,
        errors: result.errors.length
      });
    } catch (error) {
      logger.error('Error in scheduled smart sync', { error });
    }
  }, {
    timezone: 'UTC'
  });

  // Statistics collection
  const statsJob = cron.schedule(`0 */${Math.floor(CRON_CONFIG.STATS_INTERVAL / 60)} * * *`, async () => {
    try {
      const stats = await getSyncStatistics();
      logger.info('Plaid sync statistics', stats);
    } catch (error) {
      logger.error('Error getting sync statistics', { error });
    }
  }, {
    timezone: 'UTC'
  });

  // Store job references
  cronJobs = [syncJob, smartSyncJob, statsJob];

  // Start all jobs
  cronJobs.forEach(job => job.start());
  isCronActive = true;

  logger.info('Plaid sync cron jobs started', {
    syncInterval: `${CRON_CONFIG.SYNC_INTERVAL} minutes`,
    smartSyncInterval: `${CRON_CONFIG.SMART_SYNC_INTERVAL} minutes`,
    statsInterval: `${CRON_CONFIG.STATS_INTERVAL} minutes`
  });
}

/**
 * Stop all cron jobs
 */
export function stopCronJobs() {
  if (!isCronActive) {
    logger.info('Cron jobs are not active');
    return;
  }

  cronJobs.forEach(job => {
    job.stop();
    job.destroy();
  });
  
  cronJobs = [];
  isCronActive = false;
  
  logger.info('Plaid sync cron jobs stopped');
}

/**
 * Enable cron jobs as fallback when webhooks are failing
 */
export function enableFallbackMode(reason: string) {
  if (!CRON_CONFIG.FALLBACK_MODE) {
    logger.warn('Fallback mode is disabled in configuration');
    return;
  }

  if (isCronActive) {
    logger.info('Cron jobs already active');
    return;
  }

  logger.warn('Enabling cron job fallback mode', { reason });
  startCronJobs();
}

/**
 * Disable fallback mode and return to webhook-only processing
 */
export function disableFallbackMode(reason: string) {
  if (!isCronActive) {
    logger.info('Cron jobs are not active');
    return;
  }

  logger.info('Disabling cron job fallback mode', { reason });
  stopCronJobs();
}

/**
 * Get current cron job status
 */
export function getCronStatus() {
  return {
    enabled: CRON_CONFIG.ENABLED,
    active: isCronActive,
    fallbackMode: CRON_CONFIG.FALLBACK_MODE,
    jobCount: cronJobs.length,
    config: CRON_CONFIG
  };
}

/**
 * Manual sync function for testing and emergency scenarios
 */
export async function manualSync() {
  try {
    logger.info('Starting manual Plaid transfer sync');
    const result = await syncAllPendingTransfers();
    
    logger.info('Manual sync completed', result);
    return result;
  } catch (error) {
    logger.error('Error in manual sync', { error });
    throw error;
  }
}

/**
 * Check if the system is running in webhook-only mode
 */
export function isWebhookOnlyMode(): boolean {
  return CRON_CONFIG.PROCESSING_MODE === 'webhook-only' && !CRON_CONFIG.ENABLED;
}

/**
 * Get processing mode summary for monitoring and debugging
 */
export function getProcessingModeSummary() {
  return {
    mode: CRON_CONFIG.PROCESSING_MODE,
    description: CRON_CONFIG.PROCESSING_MODE_DESCRIPTION,
    webhookOnlyMode: isWebhookOnlyMode(),
    cronEnabled: CRON_CONFIG.ENABLED,
    cronActive: isCronActive,
    fallbackMode: CRON_CONFIG.FALLBACK_MODE,
    migrationComplete: CRON_CONFIG.PROCESSING_MODE === 'webhook-only' && !CRON_CONFIG.ENABLED,
    recommendations: getProcessingRecommendations()
  };
}

/**
 * Get recommendations based on current configuration
 */
function getProcessingRecommendations(): string[] {
  const recommendations: string[] = [];
  
  if (CRON_CONFIG.PROCESSING_MODE === 'cron-only') {
    recommendations.push('Consider migrating to webhook-only mode for real-time processing');
    recommendations.push('Cron-only mode introduces 5-10 minute delays in status updates');
  }
  
  if (CRON_CONFIG.PROCESSING_MODE === 'hybrid') {
    recommendations.push('Hybrid mode should only be used during transition periods');
    recommendations.push('Consider switching to webhook-only mode once webhooks are stable');
  }
  
  if (CRON_CONFIG.ENABLED && CRON_CONFIG.PROCESSING_MODE === 'webhook-only') {
    recommendations.push('Cron jobs are enabled in webhook-only mode - this may indicate fallback activation');
    recommendations.push('Check webhook health and consider disabling cron jobs if webhooks are working');
  }
  
  if (!CRON_CONFIG.FALLBACK_MODE && CRON_CONFIG.PROCESSING_MODE === 'webhook-only') {
    recommendations.push('Consider enabling fallback mode for better reliability');
  }
  
  return recommendations;
}

/**
 * Validate current processing configuration
 */
export function validateProcessingConfig(): { isValid: boolean; warnings: string[]; errors: string[] } {
  const warnings: string[] = [];
  const errors: string[] = [];
  
  // Check for conflicting configurations
  if (CRON_CONFIG.ENABLED && CRON_CONFIG.PROCESSING_MODE === 'webhook-only' && !isCronActive) {
    warnings.push('Cron jobs are enabled but not active in webhook-only mode');
  }
  
  // Check for deprecated configurations
  if (CRON_CONFIG.PROCESSING_MODE === 'cron-only') {
    warnings.push('Using deprecated cron-only processing mode');
  }
  
  // Check for missing fallback
  if (CRON_CONFIG.PROCESSING_MODE === 'webhook-only' && !CRON_CONFIG.FALLBACK_MODE) {
    warnings.push('Webhook-only mode without fallback may be risky');
  }
  
  // Validate intervals
  if (CRON_CONFIG.SYNC_INTERVAL < 1 || CRON_CONFIG.SYNC_INTERVAL > 60) {
    errors.push('Invalid sync interval - must be between 1 and 60 minutes');
  }
  
  return {
    isValid: errors.length === 0,
    warnings,
    errors
  };
} 