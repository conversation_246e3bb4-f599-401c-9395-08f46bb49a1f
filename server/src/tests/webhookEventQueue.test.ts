import { WebhookEventQueue, WebhookEventData } from '../services/webhookEventQueue';
import { executeUpdate, executeQuery } from '../utils/database';
import logger from '../utils/logger';

/**
 * Test the webhook event queue system
 */
async function testWebhookEventQueue() {
  logger.info('Starting webhook event queue tests...');

  try {
    // Create a test queue instance
    const testQueue = new WebhookEventQueue({
      maxAttempts: 3,
      initialDelay: 100, // Shorter delay for testing
      maxDelay: 1000,
      backoffMultiplier: 2
    });

    // Test 1: Enqueue a webhook event
    logger.info('Test 1: Enqueuing webhook event...');
    
    const testEvent: WebhookEventData = {
      webhook_type: 'TRANSFER',
      webhook_code: 'TRANSFER_EVENTS_UPDATE',
      event_id: `test_event_${Date.now()}`,
      transfer_id: `test_transfer_${Date.now()}`,
      status: 'posted',
      timestamp: new Date().toISOString(),
      environment: 'test'
    };

    await testQueue.enqueue(testEvent, 1);
    logger.info('✅ Test 1 passed: Event enqueued successfully');

    // Test 2: Check queue stats
    logger.info('Test 2: Checking queue statistics...');
    
    const stats = await testQueue.getQueueStats();
    logger.info('Queue stats:', stats);
    
    if (stats.totalEvents >= 0) {
      logger.info('✅ Test 2 passed: Queue stats retrieved successfully');
    } else {
      logger.error('❌ Test 2 failed: Invalid queue stats');
    }

    // Test 3: Process the queue
    logger.info('Test 3: Processing queue...');
    
    await testQueue.processQueue();
    logger.info('✅ Test 3 passed: Queue processed successfully');

    // Test 4: Test retry mechanism with a failing event
    logger.info('Test 4: Testing retry mechanism...');
    
    const failingEvent: WebhookEventData = {
      webhook_type: 'TRANSFER',
      webhook_code: 'TRANSFER_FAILED',
      event_id: `test_failing_event_${Date.now()}`,
      transfer_id: 'non_existent_transfer',
      status: 'failed',
      timestamp: new Date().toISOString(),
      environment: 'test'
    };

    await testQueue.enqueue(failingEvent, 0);
    
    // Process and let it fail
    await testQueue.processQueue();
    
    // Check if it was scheduled for retry
    await new Promise(resolve => setTimeout(resolve, 200)); // Wait for retry delay
    await testQueue.retryFailedEvents();
    
    logger.info('✅ Test 4 passed: Retry mechanism working');

    // Test 5: Test concurrent processing
    logger.info('Test 5: Testing concurrent processing...');
    
    const concurrentEvents: WebhookEventData[] = [];
    for (let i = 0; i < 5; i++) {
      concurrentEvents.push({
        webhook_type: 'TRANSFER',
        webhook_code: 'TRANSFER_POSTED',
        event_id: `concurrent_event_${i}_${Date.now()}`,
        transfer_id: `concurrent_transfer_${i}_${Date.now()}`,
        status: 'posted',
        timestamp: new Date().toISOString(),
        environment: 'test'
      });
    }

    // Enqueue all events
    for (const event of concurrentEvents) {
      await testQueue.enqueue(event, Math.floor(Math.random() * 3));
    }

    // Process them
    await testQueue.processQueue();
    logger.info('✅ Test 5 passed: Concurrent processing working');

    // Test 6: Check final stats
    logger.info('Test 6: Checking final statistics...');
    
    const finalStats = await testQueue.getQueueStats();
    logger.info('Final queue stats:', finalStats);
    
    logger.info('✅ All webhook event queue tests completed successfully!');

    // Stop the queue processor
    testQueue.stopQueueProcessor();

    return {
      success: true,
      message: 'All tests passed',
      stats: finalStats
    };

  } catch (error) {
    logger.error('❌ Webhook event queue test failed:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });

    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Test webhook event deduplication
 */
async function testWebhookDeduplication() {
  logger.info('Testing webhook event deduplication...');

  try {
    const testQueue = new WebhookEventQueue();
    
    const duplicateEvent: WebhookEventData = {
      webhook_type: 'TRANSFER',
      webhook_code: 'TRANSFER_POSTED',
      event_id: 'duplicate_test_event',
      transfer_id: 'duplicate_test_transfer',
      status: 'posted',
      timestamp: new Date().toISOString(),
      environment: 'test'
    };

    // Enqueue the same event twice
    await testQueue.enqueue(duplicateEvent, 0);
    
    try {
      await testQueue.enqueue(duplicateEvent, 0);
      logger.warn('⚠️  Duplicate event was allowed - this might be expected behavior');
    } catch (error) {
      logger.info('✅ Duplicate event was rejected as expected');
    }

    testQueue.stopQueueProcessor();

    return {
      success: true,
      message: 'Deduplication test completed'
    };

  } catch (error) {
    logger.error('❌ Deduplication test failed:', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Test dead letter queue functionality
 */
async function testDeadLetterQueue() {
  logger.info('Testing dead letter queue functionality...');

  try {
    const testQueue = new WebhookEventQueue({
      maxAttempts: 2, // Low max attempts for testing
      initialDelay: 50,
      maxDelay: 200,
      backoffMultiplier: 2
    });

    const deadLetterEvent: WebhookEventData = {
      webhook_type: 'TRANSFER',
      webhook_code: 'TRANSFER_FAILED',
      event_id: `dead_letter_test_${Date.now()}`,
      transfer_id: 'definitely_non_existent_transfer',
      status: 'failed',
      timestamp: new Date().toISOString(),
      environment: 'test'
    };

    await testQueue.enqueue(deadLetterEvent, 0);

    // Process multiple times to exhaust retries
    for (let i = 0; i < 3; i++) {
      await testQueue.processQueue();
      await new Promise(resolve => setTimeout(resolve, 100));
      await testQueue.retryFailedEvents();
    }

    const stats = await testQueue.getQueueStats();
    logger.info('Dead letter queue stats:', stats);

    if (stats.deadLetterEvents > 0) {
      logger.info('✅ Dead letter queue test passed: Event moved to dead letter queue');
    } else {
      logger.warn('⚠️  Dead letter queue test: No events in dead letter queue');
    }

    testQueue.stopQueueProcessor();

    return {
      success: true,
      message: 'Dead letter queue test completed',
      deadLetterEvents: stats.deadLetterEvents
    };

  } catch (error) {
    logger.error('❌ Dead letter queue test failed:', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Run all webhook event queue tests
 */
export async function runWebhookEventQueueTests() {
  logger.info('🚀 Starting comprehensive webhook event queue tests...');

  const results = {
    basicTests: await testWebhookEventQueue(),
    deduplicationTests: await testWebhookDeduplication(),
    deadLetterTests: await testDeadLetterQueue()
  };

  const allPassed = Object.values(results).every(result => result.success);

  if (allPassed) {
    logger.info('🎉 All webhook event queue tests passed successfully!');
  } else {
    logger.error('❌ Some webhook event queue tests failed');
  }

  return {
    success: allPassed,
    results
  };
}

// Run tests if this file is executed directly
if (require.main === module) {
  runWebhookEventQueueTests()
    .then((results) => {
      console.log('Test Results:', JSON.stringify(results, null, 2));
      process.exit(results.success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}