import dotenv from 'dotenv';

// Load environment variables FIRST before any other imports
dotenv.config();

import express, { Request, Response, RequestHandler } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import paymentRoutes from './routes/paymentRoutes';
import mobileApisRoutes from './routes/mobileApisRoutes';
import plaidRoutes from './routes/plaidRoutes';
import plaidTransferRoutes from './routes/plaidTransferRoutes';
import plaidStatusRoutes from './routes/plaidStatusRoutes';
import { errorHandler } from './middlewares/errorHandler';
import { authenticateTokenAndSession } from './middlewares/sessionAuth';
import userRoutes from './routes/userRoutes';
import securityEventRoutes from './routes/securityEventRoutes';
import walletRoutes from './routes/walletRoutes';
import staffRoutes from './routes/staffRoutes';
import paymentMethodRoutes from './routes/paymentMethodRoutes';
import duesRoutes from './routes/duesRoutes';
import stripeWebhookRoutes from './routes/stripeWebhookRoutes';
import { getConnection } from './config/db';
import { setupPlaidSyncCron, getCronStatus, getProcessingModeSummary, validateProcessingConfig, isWebhookOnlyMode } from './cron/plaidSyncCron';
import { WEBHOOK_CONFIG } from './config/webhookConfig';
import logger from './utils/logger';

const app = express();

// Initialize database connection
getConnection();

// Validate webhook configuration and determine processing mode
WEBHOOK_CONFIG.validateWebhookConfig();

/**
 * Initialize the processing system based on configuration
 * MIGRATION COMPLETE: Webhook processing is now the PRIMARY method, replacing cron job polling
 * This represents the completed transition from polling-based to real-time event-driven architecture
 */
function initializeProcessingSystem() {
  logger.info('=== INITIALIZING PLAID PROCESSING SYSTEM ===');
  logger.info('🔧 System Configuration:', {
    processingMode: WEBHOOK_CONFIG.PROCESSING_MODE,
    webhookOnlyMode: WEBHOOK_CONFIG.WEBHOOK_ONLY_MODE,
    webhookPriority: WEBHOOK_CONFIG.WEBHOOK_PRIORITY,
    webhookUrl: WEBHOOK_CONFIG.FULL_URL
  });

  // Validate processing configuration before initialization
  const configValidation = validateProcessingConfig();
  if (!configValidation.isValid) {
    logger.error('❌ PROCESSING CONFIGURATION ERRORS:', configValidation.errors);
  }
  if (configValidation.warnings.length > 0) {
    logger.warn('⚠️  PROCESSING CONFIGURATION WARNINGS:', configValidation.warnings);
  }

  switch (WEBHOOK_CONFIG.PROCESSING_MODE) {
    case 'webhook-only':
      logger.info('🚀 WEBHOOK-ONLY PROCESSING MODE SELECTED (RECOMMENDED)');
      logger.info('✅ Real-time webhook processing is ACTIVE');
      logger.info('🚫 Cron job polling is DISABLED (migration complete)');
      logger.info('⚡ Transfer status updates will be processed immediately upon receipt');
      logger.info('📈 This eliminates 5-10 minute delays from the previous polling system');
      logger.info('🎯 MIGRATION STATUS: COMPLETE - System fully transitioned to webhook-only processing');

      // Initialize cron system (will be disabled by default)
      setupPlaidSyncCron();
      break;

    case 'hybrid':
      logger.info('🔄 HYBRID PROCESSING MODE SELECTED');
      logger.info('🥇 Primary: Real-time webhook processing');
      logger.info('🥈 Fallback: Cron job polling for missed events');
      logger.warn('⚠️  This mode should only be used during transition periods');
      logger.warn('💡 Consider migrating to webhook-only mode for optimal performance');

      // Enable cron jobs as fallback in hybrid mode
      process.env.PLAID_CRON_ENABLED = 'true';
      setupPlaidSyncCron();
      break;

    case 'cron-only':
      logger.warn('⚠️  CRON-ONLY PROCESSING MODE SELECTED (DEPRECATED)');
      logger.warn('🔄 Using LEGACY cron job polling - NOT RECOMMENDED');
      logger.warn('⏰ This mode introduces 5-10 minute delays in status updates');
      logger.warn('🚨 This mode should ONLY be used for debugging or emergency scenarios');
      logger.warn('💡 STRONGLY RECOMMEND switching to webhook-only mode for better performance');
      logger.warn('🔧 To migrate: Set PLAID_PROCESSING_MODE=webhook-only in environment variables');

      // Force enable cron jobs
      process.env.PLAID_CRON_ENABLED = 'true';
      setupPlaidSyncCron();
      break;

    default:
      logger.error('❌ INVALID PROCESSING MODE DETECTED');
      logger.error('🔧 Invalid mode:', WEBHOOK_CONFIG.PROCESSING_MODE);
      logger.error('🔄 Defaulting to webhook-only processing mode');
      logger.info('💡 Valid modes: webhook-only (recommended), hybrid, cron-only (deprecated)');

      setupPlaidSyncCron();
      break;
  }

  // Get comprehensive processing mode summary
  const processingModeSummary = getProcessingModeSummary();

  // Log final system status with enhanced details
  const cronStatus = getCronStatus();
  logger.info('=== PROCESSING SYSTEM INITIALIZATION COMPLETE ===');
  logger.info('📊 Final System Status:', {
    processingMode: WEBHOOK_CONFIG.PROCESSING_MODE,
    webhookProcessing: WEBHOOK_CONFIG.WEBHOOK_ONLY_MODE ? 'ENABLED' : 'DISABLED',
    cronEnabled: cronStatus.enabled,
    cronActive: cronStatus.active,
    fallbackMode: cronStatus.fallbackMode,
    webhookPriority: WEBHOOK_CONFIG.WEBHOOK_PRIORITY,
    migrationComplete: processingModeSummary.migrationComplete,
    webhookOnlyMode: processingModeSummary.webhookOnlyMode
  });

  // Log processing method summary with migration status
  if (WEBHOOK_CONFIG.PROCESSING_MODE === 'webhook-only') {
    logger.info('🎯 PROCESSING METHOD: Real-time webhooks only');
    logger.info('⚡ STATUS UPDATES: Immediate (no polling delays)');
    logger.info('🔧 CRON JOBS: Disabled (emergency fallback available)');
    logger.info('✅ MIGRATION STATUS: Complete - System fully transitioned to webhook-only processing');

    if (isWebhookOnlyMode()) {
      logger.info('🏆 OPTIMAL CONFIGURATION: System is running in pure webhook-only mode');
    }
  } else if (WEBHOOK_CONFIG.PROCESSING_MODE === 'hybrid') {
    logger.info('🎯 PROCESSING METHOD: Webhooks + Cron fallback');
    logger.info('⚡ STATUS UPDATES: Immediate via webhooks, polling backup');
    logger.warn('🔄 MIGRATION STATUS: In progress - Consider completing migration to webhook-only');
  } else {
    logger.info('🎯 PROCESSING METHOD: Legacy cron job polling');
    logger.info('⏰ STATUS UPDATES: Every 5-10 minutes (delayed)');
    logger.warn('🚨 MIGRATION STATUS: Not started - Strongly recommend migrating to webhook-only');
  }

  // Log recommendations if any
  if (processingModeSummary.recommendations.length > 0) {
    logger.info('💡 SYSTEM RECOMMENDATIONS:');
    processingModeSummary.recommendations.forEach((recommendation, index) => {
      logger.info(`   ${index + 1}. ${recommendation}`);
    });
  }
}

// Initialize processing system based on configuration
// Webhook processing is prioritized over cron jobs
initializeProcessingSystem();

// Security
app.use(helmet());
app.use(cors({
  origin: '*', // Allows all origins
  credentials: false // Credentials can't be used with '*'
}));

app.use(morgan('dev'));
app.use(express.json());

// Routes
app.use('/api/payments', paymentRoutes);
app.use('/api/plaid', plaidRoutes);
app.use('/api/plaid-transfers', plaidTransferRoutes); // New Plaid transfer routes
app.use('/api/plaid-status', plaidStatusRoutes); // Enhanced Plaid status routes
app.use('/api/users', userRoutes);
app.use('/api/wallet', walletRoutes);
app.use('/api/staff', staffRoutes);
app.use('/api/security-events', securityEventRoutes);
app.use('/api/dues', duesRoutes); // Use dedicated dues routes
app.use('/api', paymentMethodRoutes);
app.use('/api/app', mobileApisRoutes);
app.use('/api/stripe', stripeWebhookRoutes); // Stripe webhook routes

// Health check
const healthCheckHandler: RequestHandler = (_req: Request, res: Response): void => {
  res.json({ status: 'ok' });
};

app.get('/api/health', healthCheckHandler);

// Error handler
app.use(errorHandler);

export default app;
