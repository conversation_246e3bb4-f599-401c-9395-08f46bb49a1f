{"name": "giu-pay-connect", "version": "1.0.0", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "start": "node dist/server.js", "build": "tsc", "test:email": "ts-node src/scripts/testEmail.ts", "test:welcome-flow": "ts-node src/scripts/testWelcomeEmailFlow.ts", "test:single-session": "ts-node src/scripts/testSingleSessionPolicy.ts", "test:user-messages": "node test-user-friendly-messages.js", "test:ach": "ts-node src/scripts/testACHTransfer.ts", "test:add-money": "ts-node src/scripts/testAddMoney.ts", "payments:mark-paid": "node scripts/markAllPendingPaymentsPaid.js", "payments:mark-paid:dry-run": "node scripts/markAllPendingPaymentsPaid.js --dry-run", "payments:mark-paid:wallet": "node scripts/markAllPendingPaymentsPaid.js --table=wallet_transactions", "payments:mark-paid:players": "node scripts/markAllPendingPaymentsPaid.js --table=player_payments", "payments:mark-paid:holds": "node scripts/markAllPendingPaymentsPaid.js --table=pending_holds", "payments:mark-paid:teams": "node scripts/markAllPendingPaymentsPaid.js --table=team_members", "payments:verify": "node scripts/verifyPaymentStatus.js", "payments:process:dry-run": "node scripts/processAllPendingPayments.js --dry-run", "payments:process": "node scripts/processAllPendingPayments.js", "payments:process:user": "node scripts/processAllPendingPayments.js --user-id=", "payments:process:transfer": "node scripts/processAllPendingPayments.js --transfer-id=", "payments:process:force": "node scripts/processAllPendingPayments.js --force-complete", "payments:verify-real": "node scripts/verifyRealPaymentStatus.js", "webhook:trigger": "ts-node src/scripts/enhancedTransactionTrigger.ts", "webhook:trigger:quick": "ts-node src/scripts/enhancedTransactionTrigger.ts quick", "webhook:trigger:lifecycle": "ts-node src/scripts/enhancedTransactionTrigger.ts lifecycle", "webhook:trigger:scenarios": "ts-node src/scripts/enhancedTransactionTrigger.ts scenarios", "webhook:config": "ts-node src/scripts/enhancedTransactionTrigger.ts config", "webhook:register": "ts-node src/scripts/registerWebhookUrl.ts", "webhook:debug": "ts-node src/scripts/debugWebhookIssue.ts", "test:user-id-extraction": "ts-node src/scripts/testRealTimeUserIdExtraction.ts", "test:stripe-config": "ts-node src/scripts/testStripeConfig.ts", "test:stripe-config-basic": "ts-node src/scripts/testStripeConfigBasic.ts"}, "dependencies": {"@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/stripe": "^8.0.416", "axios": "^1.11.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.14.1", "node-cron": "^4.2.1", "nodemailer": "^7.0.4", "plaid": "^36.0.0", "stripe": "^18.4.0", "winston": "^3.17.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.0.8", "@types/winston": "^2.4.4", "bcrypt": "^6.0.0", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}}