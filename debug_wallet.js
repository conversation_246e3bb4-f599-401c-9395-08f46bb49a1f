const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
  host: '*************',
  user: 'giupayconnect_db_user',
  password: 'giup@yconnect202103',
  database: 'giupayconnect_master_db',
  port: 3307,
};

async function debugWallet() {
  let connection;
  try {
    console.log('Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('Connected successfully!');

    // Check recent wallet transactions
    console.log('\n=== Recent Wallet Transactions ===');
    const [transactions] = await connection.execute(`
      SELECT 
        id, user_id, type, amount, description, status_id, 
        reference_id, created_at, meta_data
      FROM tbl_wallet_transactions 
      WHERE type IN ('debit', 'credit', 'wallet_transfer') 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    console.log('Recent transactions:');
    transactions.forEach(tx => {
      console.log(`ID: ${tx.id}, User: ${tx.user_id}, Type: ${tx.type}, Amount: ${tx.amount}, Status: ${tx.status_id}, Desc: ${tx.description}, Date: ${tx.created_at}`);
    });

    // Check wallet balances for users who made recent transactions
    if (transactions.length > 0) {
      const userIds = [...new Set(transactions.map(tx => tx.user_id))];
      console.log('\n=== Wallet Balances ===');
      
      for (const userId of userIds) {
        const [wallets] = await connection.execute(`
          SELECT user_id, balance, last_updated 
          FROM tbl_masterwallet 
          WHERE user_id = ?
        `, [userId]);
        
        if (wallets.length > 0) {
          const wallet = wallets[0];
          console.log(`User ${userId}: Balance = ${wallet.balance}, Last Updated: ${wallet.last_updated}`);
        }
      }
    }

    // Check for any failed transactions
    console.log('\n=== Failed Transactions ===');
    const [failedTx] = await connection.execute(`
      SELECT 
        id, user_id, type, amount, description, status_id, 
        reference_id, created_at
      FROM tbl_wallet_transactions 
      WHERE status_id = 3 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    if (failedTx.length > 0) {
      console.log('Failed transactions:');
      failedTx.forEach(tx => {
        console.log(`ID: ${tx.id}, User: ${tx.user_id}, Type: ${tx.type}, Amount: ${tx.amount}, Desc: ${tx.description}, Date: ${tx.created_at}`);
      });
    } else {
      console.log('No failed transactions found.');
    }

    // Check ledger entries
    console.log('\n=== Recent Ledger Entries ===');
    const [ledger] = await connection.execute(`
      SELECT 
        id, transaction_id, entry_type, balance_before, balance_after, created_at
      FROM tbl_wallet_ledger 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    if (ledger.length > 0) {
      console.log('Recent ledger entries:');
      ledger.forEach(entry => {
        console.log(`ID: ${entry.id}, TxID: ${entry.transaction_id}, Type: ${entry.entry_type}, Before: ${entry.balance_before}, After: ${entry.balance_after}, Date: ${entry.created_at}`);
      });
    } else {
      console.log('No ledger entries found.');
    }

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\nDatabase connection closed.');
    }
  }
}

debugWallet();
