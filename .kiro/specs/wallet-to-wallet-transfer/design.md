# Design Document

## Overview

The wallet-to-wallet transfer system enables secure money transfers between user wallets with PIN verification and atomic transaction processing. The system integrates with the existing wallet infrastructure and maintains transaction integrity through database transactions.

## Architecture

### System Components

1. **Transfer Controller** - Handles HTTP requests and response formatting
2. **Transfer Service** - Core business logic for wallet transfers
3. **Wallet Service** - Existing wallet operations (balance updates, PIN verification)
4. **User Search Service** - Finding and validating transfer recipients
5. **Transaction Service** - Creating transaction records and audit logs
6. **Database Layer** - Atomic transaction management

### Data Flow

```
Frontend Request → Transfer Controller → Transfer Service → Database Transaction
                                     ↓
                    Wallet Service ← PIN Verification
                                     ↓
                    Balance Updates ← Sender & Recipient Wallets
                                     ↓
                    Transaction Records ← Audit Trail Creation
```

## Components and Interfaces

### Transfer Controller

**Endpoint:** `POST /wallet/transfer`

**Request Interface:**
```typescript
interface TransferRequest {
  recipientUserId: number;
  amount: number;
  pin: string;
}
```

**Response Interface:**
```typescript
interface TransferResponse {
  success: boolean;
  newBalance?: number;
  message?: string;
  transactionId?: number;
}
```

### Transfer Service

**Core Function:**
```typescript
async function transferWalletToWallet(
  senderUserId: number,
  recipientUserId: number,
  amount: number,
  pin: string
): Promise<TransferResult>
```

**Validation Steps:**
1. Validate transfer amount (min: $1.00, max: $10,000.00)
2. Verify sender has sufficient balance
3. Verify recipient has active wallet
4. Verify sender's PIN
5. Execute atomic transfer

### User Search Service

**Endpoint:** `GET /wallet/search-users?search={term}`

**Search Function:**
```typescript
async function searchTransferRecipients(
  searchTerm: string,
  excludeUserId: number
): Promise<TransferRecipient[]>
```

**Search Criteria:**
- Active users with wallets
- Name or email matching search term
- Exclude current user from results

## Data Models

### Transfer Transaction Record

```sql
-- Sender transaction (negative amount)
INSERT INTO tbl_wallet_transactions (
  user_id,
  type,
  amount,
  reference_id,
  payment_provider,
  description,
  status_id,
  meta_data
) VALUES (
  sender_id,
  'wallet_transfer_out',
  -amount,
  transfer_reference,
  'internal',
  'Transfer to {recipient_name}',
  1, -- completed
  JSON_OBJECT('recipient_id', recipient_id, 'recipient_name', recipient_name)
);

-- Recipient transaction (positive amount)
INSERT INTO tbl_wallet_transactions (
  user_id,
  type,
  amount,
  reference_id,
  payment_provider,
  description,
  status_id,
  meta_data
) VALUES (
  recipient_id,
  'wallet_transfer_in',
  amount,
  transfer_reference,
  'internal',
  'Transfer from {sender_name}',
  1, -- completed
  JSON_OBJECT('sender_id', sender_id, 'sender_name', sender_name)
);
```

### Wallet Balance Updates

```sql
-- Update sender balance
UPDATE tbl_masterwallet 
SET balance = balance - amount, last_updated = NOW() 
WHERE user_id = sender_id;

-- Update recipient balance
UPDATE tbl_masterwallet 
SET balance = balance + amount, last_updated = NOW() 
WHERE user_id = recipient_id;
```

## Error Handling

### Validation Errors
- **Invalid Amount**: Amount must be between $1.00 and $10,000.00
- **Insufficient Balance**: Sender doesn't have enough funds
- **Invalid PIN**: Wallet PIN verification failed
- **Invalid Recipient**: Recipient doesn't exist or has no wallet
- **Self Transfer**: Cannot transfer to own wallet

### System Errors
- **Database Transaction Failure**: Rollback all changes
- **Wallet Not Found**: Sender or recipient wallet missing
- **Concurrent Modification**: Handle race conditions with proper locking

### Error Response Format
```typescript
{
  success: false,
  message: "User-friendly error message",
  error?: "Technical error details" // Development only
}
```

## Testing Strategy

### Unit Tests
1. **Transfer Service Tests**
   - Valid transfer scenarios
   - Validation error cases
   - PIN verification failures
   - Insufficient balance scenarios

2. **Controller Tests**
   - Request/response handling
   - Authentication requirements
   - Error response formatting

### Integration Tests
1. **Database Transaction Tests**
   - Atomic transfer operations
   - Rollback on failure scenarios
   - Concurrent transfer handling

2. **End-to-End Tests**
   - Complete transfer flow
   - Frontend integration
   - Error handling flows

### Test Data Requirements
- Test users with wallets and various balances
- Test scenarios for edge cases
- Mock PIN verification for automated tests

## Security Considerations

### PIN Security
- PIN verification before any transfer
- Rate limiting on failed PIN attempts
- Secure PIN comparison using existing hash functions

### Transaction Security
- Database transactions for atomicity
- Audit logging for all transfer attempts
- Input validation and sanitization

### Access Control
- Authentication required for all endpoints
- Users can only transfer from their own wallets
- Recipient validation to prevent invalid transfers

## Performance Considerations

### Database Optimization
- Use database transactions for consistency
- Index on user_id fields for fast lookups
- Batch operations where possible

### Caching Strategy
- Cache user search results temporarily
- Cache wallet status for recipient validation
- Invalidate cache on wallet updates

### Scalability
- Design for concurrent transfers
- Use proper database locking
- Consider queue-based processing for high volume