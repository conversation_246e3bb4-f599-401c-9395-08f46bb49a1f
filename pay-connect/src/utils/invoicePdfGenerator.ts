import { Invoice } from '../services/invoices';
import logoImage from '../assets/images/giu_pay_connect.png';

/**
 * Generate and download a professional invoice PDF
 */
export const generateInvoicePDF = async (invoice: Invoice): Promise<void> => {
    try {
        // Create a new window for the invoice
        const printWindow = window.open('', '_blank');
        if (!printWindow) {
            throw new Error('Unable to open print window. Please allow popups for this site.');
        }

        // Calculate totals
        const subtotal = invoice.items.reduce((sum, item) => sum + item.total, 0);
        const discountAmount = subtotal * (invoice.discountPercent / 100);
        const taxableAmount = subtotal - discountAmount;
        const taxAmount = taxableAmount * (invoice.taxPercent / 100);
        const totalAmount = taxableAmount + taxAmount;

        // Generate the HTML content for the invoice
        const invoiceHTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice ${invoice.invoiceNumber}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8fafc;
            padding: 20px;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .invoice-header {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            color: white;
            padding: 40px;
            position: relative;
        }
        
        .invoice-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            position: relative;
            z-index: 1;
        }
        
        .company-info {
            flex: 1;
        }
        
        .company-logo {
            width: 120px;
            height: auto;
            margin-bottom: 20px;
            background: white;
            padding: 10px;
            border-radius: 8px;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .company-tagline {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .company-details {
            font-size: 14px;
            opacity: 0.8;
            line-height: 1.8;
        }
        
        .invoice-meta {
            text-align: right;
            flex: 1;
            max-width: 300px;
        }
        
        .invoice-title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .invoice-number {
            font-size: 18px;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-paid { background: rgba(34, 197, 94, 0.2); color: #15803d; }
        .status-sent { background: rgba(59, 130, 246, 0.2); color: #1d4ed8; }
        .status-overdue { background: rgba(239, 68, 68, 0.2); color: #dc2626; }
        .status-cancelled { background: rgba(107, 114, 128, 0.2); color: #374151; }
        
        .invoice-body {
            padding: 40px;
        }
        
        .billing-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
            gap: 40px;
        }
        
        .billing-info {
            flex: 1;
        }
        
        .billing-title {
            font-size: 16px;
            font-weight: 600;
            color: #f97316;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .billing-details {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #f97316;
        }
        
        .client-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #1f2937;
        }
        
        .client-details {
            color: #6b7280;
            line-height: 1.6;
        }
        
        .invoice-dates {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .date-item {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .date-label {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .date-value {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }
        
        .items-section {
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .items-table th {
            background: #f97316;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 14px;
        }
        
        .items-table th:first-child { border-radius: 8px 0 0 0; }
        .items-table th:last-child { border-radius: 0 8px 0 0; }
        
        .items-table td {
            padding: 15px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .items-table tr:nth-child(even) {
            background: #f8fafc;
        }
        
        .items-table tr:hover {
            background: #f1f5f9;
        }
        
        .item-description {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }
        
        .item-details {
            font-size: 14px;
            color: #6b7280;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .totals-section {
            background: #f8fafc;
            padding: 30px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }
        
        .totals-table {
            width: 100%;
            max-width: 400px;
            margin-left: auto;
        }
        
        .totals-table td {
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .totals-table .total-label {
            font-weight: 500;
            color: #6b7280;
        }
        
        .totals-table .total-value {
            text-align: right;
            font-weight: 600;
            color: #1f2937;
        }
        
        .totals-table .discount-row {
            color: #059669;
        }
        
        .totals-table .final-total {
            border-top: 2px solid #f97316;
            border-bottom: none;
            font-size: 18px;
            font-weight: bold;
            color: #f97316;
            padding-top: 15px;
        }
        
        .notes-section {
            margin-top: 40px;
            padding: 20px;
            background: #fffbeb;
            border-radius: 8px;
            border-left: 4px solid #f59e0b;
        }
        
        .notes-title {
            font-weight: 600;
            color: #92400e;
            margin-bottom: 10px;
        }
        
        .notes-content {
            color: #78350f;
            line-height: 1.6;
        }
        
        .footer {
            background: #1f2937;
            color: white;
            padding: 30px 40px;
            text-align: center;
        }
        
        .footer-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .footer-text {
            opacity: 0.8;
        }
        
        .footer-contact {
            font-size: 14px;
        }
        
        /* Action Bar Styles */
        .action-bar {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            padding: 15px;
            display: flex;
            gap: 10px;
            z-index: 1000;
            border: 1px solid #e5e7eb;
        }
        
        .action-btn {
            padding: 10px 16px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }
        
        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .btn-primary {
            background: #f97316;
            color: white;
        }
        
        .btn-primary:hover {
            background: #ea580c;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        /* Icons */
        .icon {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }
        
        @media print {
            .action-bar {
                display: none !important;
            }
            body { 
                background: white; 
                padding: 0; 
            }
            .invoice-container { 
                box-shadow: none; 
                border-radius: 0; 
            }
        }
        
        @media (max-width: 768px) {
            .header-content,
            .billing-section,
            .footer-content {
                flex-direction: column;
                text-align: center;
            }
            
            .invoice-meta {
                max-width: none;
                text-align: center;
            }
            
            .invoice-dates {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Floating Action Bar -->
    <div class="action-bar">
        <button onclick="printInvoice()" class="action-btn btn-primary" title="Print Invoice">
            <svg class="icon" viewBox="0 0 24 24">
                <path d="M19 8H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zm-3 11H8v-5h8v5zm3-7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm-1-9H6v4h12V3z"/>
            </svg>
            Print
        </button>
        <button onclick="downloadPDF()" class="action-btn btn-secondary" title="Download PDF">
            <svg class="icon" viewBox="0 0 24 24">
                <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
            </svg>
            Download
        </button>
        <button onclick="closeWindow()" class="action-btn btn-danger" title="Close Window">
            <svg class="icon" viewBox="0 0 24 24">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
            Close
        </button>
    </div>

    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <div class="header-content">
                <div class="company-info">
                    <img src="${logoImage}" alt="PayConnect Logo" class="company-logo" />
                    <div class="company-name">PayConnect</div>
                    <div class="company-tagline">Sports Payment Management System</div>
                    <div class="company-details">
                        Email: <EMAIL><br>
                        Phone: +****************<br>
                        Web: www.payconnect.com
                    </div>
                </div>
                <div class="invoice-meta">
                    <div class="invoice-title">INVOICE</div>
                    <div class="invoice-number">${invoice.invoiceNumber}</div>
                    <div class="status-badge status-${invoice.status.toLowerCase()}">${invoice.status}</div>
                </div>
            </div>
        </div>

        <!-- Body -->
        <div class="invoice-body">
            <!-- Billing Information -->
            <div class="billing-section">
                <div class="billing-info">
                    <div class="billing-title">Bill To</div>
                    <div class="billing-details">
                        <div class="client-name">${invoice.clientName}</div>
                        <div class="client-details">
                            ${invoice.clientEmail}<br>
                            ${invoice.clientPhone}<br>
                            ${invoice.league} - ${invoice.season}
                        </div>
                    </div>
                </div>
                <div class="billing-info">
                    <div class="billing-title">Transaction Details</div>
                    <div class="billing-details">
                        <div class="client-name">Reference</div>
                        <div class="client-details">
                            ${invoice.invoiceNumber}<br>
                            ${invoice.description}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Dates -->
            <div class="invoice-dates">
                <div class="date-item">
                    <div class="date-label">Transaction Date</div>
                    <div class="date-value">${new Date(invoice.issueDate).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        })}</div>
                </div>
                <div class="date-item">
                    <div class="date-label">Due Date</div>
                    <div class="date-value">${new Date(invoice.dueDate).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        })}</div>
                </div>
            </div>

            <!-- Items Section -->
            <div class="items-section">
                <div class="section-title">Transaction Items</div>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Description</th>
                            <th class="text-center">Qty</th>
                            <th class="text-right">Unit Price</th>
                            <th class="text-right">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoice.items.map(item => `
                            <tr>
                                <td>
                                    <div class="item-description">${item.description}</div>
                                    <div class="item-details">Item ID: ${item.id}</div>
                                </td>
                                <td class="text-center">${item.quantity}</td>
                                <td class="text-right">$${item.unitPrice.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
                                <td class="text-right">$${item.total.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <!-- Totals Section -->
            <div class="totals-section">
                <table class="totals-table">
                    <tr>
                        <td class="total-label">Subtotal:</td>
                        <td class="total-value">$${subtotal.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
                    </tr>
                    ${invoice.discountPercent > 0 ? `
                    <tr class="discount-row">
                        <td class="total-label">Discount (${invoice.discountPercent}%):</td>
                        <td class="total-value">-$${discountAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
                    </tr>
                    ` : ''}
                    ${invoice.taxPercent > 0 ? `
                    <tr>
                        <td class="total-label">Tax (${invoice.taxPercent}%):</td>
                        <td class="total-value">$${taxAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
                    </tr>
                    ` : ''}
                    <tr class="final-total">
                        <td class="total-label">Total Amount:</td>
                        <td class="total-value">$${totalAmount.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
                    </tr>
                </table>
            </div>

            ${invoice.notes ? `
            <!-- Notes Section -->
            <div class="notes-section">
                <div class="notes-title">Notes & Terms</div>
                <div class="notes-content">${invoice.notes}</div>
            </div>
            ` : ''}
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-content">
                <div class="footer-text">
                    Thank you for using PayConnect!
                </div>
                <div class="footer-contact">
                    Generated on ${new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        })}
                </div>
            </div>
        </div>
    </div>

    <script>
        // Print function
        function printInvoice() {
            window.print();
        }
        
        // Download as PDF function
        function downloadPDF() {
            window.print();
        }
        
        // Close window function
        function closeWindow() {
            window.close();
        }
        
        // Optional: Close window after printing
        window.onafterprint = function() {
            // Don't auto-close, let user decide
            // window.close();
        };
    </script>
</body>
</html>`;

        // Write the HTML content to the new window
        printWindow.document.write(invoiceHTML);
        printWindow.document.close();

    } catch (error) {
        console.error('Error generating invoice PDF:', error);
        throw new Error('Failed to generate invoice PDF. Please try again.');
    }
};

/**
 * Download invoice as PDF using browser's print functionality
 */
export const downloadInvoicePDF = async (invoice: Invoice): Promise<void> => {
    try {
        await generateInvoicePDF(invoice);
    } catch (error) {
        console.error('Error downloading invoice PDF:', error);
        alert('Failed to download invoice PDF. Please try again.');
    }
};