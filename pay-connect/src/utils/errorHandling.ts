/**
 * Error handling utilities for the invoice/transaction system
 */

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

/**
 * Handle API errors with appropriate user messages and actions
 */
export const handleApiError = (error: any): ApiError => {
  // Network or connection errors
  if (!error.response) {
    return {
      message: 'Network error. Please check your connection and try again.',
      status: 0,
      code: 'NETWORK_ERROR'
    };
  }

  const status = error.response?.status;
  const data = error.response?.data;

  switch (status) {
    case 401:
      // Handle authentication error
      localStorage.removeItem('token');
      if (!window.location.pathname.includes('/verify-otp')) {
        window.location.href = '/login';
      }
      return {
        message: 'Your session has expired. Please log in again.',
        status: 401,
        code: 'UNAUTHORIZED'
      };

    case 403:
      return {
        message: 'You do not have permission to access this data.',
        status: 403,
        code: 'FORBIDDEN'
      };

    case 404:
      return {
        message: 'The requested data was not found.',
        status: 404,
        code: 'NOT_FOUND'
      };

    case 429:
      return {
        message: 'Too many requests. Please wait a moment and try again.',
        status: 429,
        code: 'RATE_LIMITED'
      };

    case 500:
    case 502:
    case 503:
    case 504:
      return {
        message: 'Server error. Please try again later or contact support if the problem persists.',
        status: status,
        code: 'SERVER_ERROR'
      };

    default:
      return {
        message: data?.message || 'An unexpected error occurred. Please try again.',
        status: status,
        code: 'UNKNOWN_ERROR'
      };
  }
};

/**
 * Get user-friendly error message for transaction-related errors
 */
export const getTransactionErrorMessage = (error: any): string => {
  const apiError = handleApiError(error);
  
  // Customize messages for transaction context
  switch (apiError.code) {
    case 'NETWORK_ERROR':
      return 'Unable to load transactions. Please check your connection and try again.';
    case 'NOT_FOUND':
      return 'Transaction data not found. It may have been removed or you may not have access.';
    case 'UNAUTHORIZED':
      return 'Please log in to view your transaction history.';
    case 'FORBIDDEN':
      return 'You do not have permission to view these transactions.';
    case 'SERVER_ERROR':
      return 'Unable to load transaction data. Please try again later.';
    default:
      return apiError.message;
  }
};

/**
 * Retry mechanism for failed API calls
 */
export const retryApiCall = async <T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;
      
      // Don't retry on authentication or client errors
      if (error.response?.status && error.response.status < 500) {
        throw error;
      }

      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError;
};

/**
 * Check if error is retryable
 */
export const isRetryableError = (error: any): boolean => {
  // Network errors are retryable
  if (!error.response) {
    return true;
  }

  const status = error.response?.status;
  
  // Server errors (5xx) are retryable
  return status >= 500;
};

/**
 * Log error for debugging purposes
 */
export const logError = (error: any, context: string) => {
  console.error(`[${context}] Error:`, {
    message: error.message,
    status: error.response?.status,
    data: error.response?.data,
    stack: error.stack
  });
};