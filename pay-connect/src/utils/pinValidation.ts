/**
 * PIN validation utilities
 */

export interface PinValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Validate PIN format and requirements
 */
export const validatePin = (pin: string): PinValidationResult => {
  const errors: string[] = [];

  // Check if PIN is provided
  if (!pin || pin.trim() === '') {
    errors.push('PIN is required');
    return { isValid: false, errors };
  }

  // Check if PIN contains only digits
  if (!/^\d+$/.test(pin)) {
    errors.push('PIN must contain only numbers');
  }

  // Check PIN length
  if (pin.length < 4) {
    errors.push('PIN must be at least 4 digits');
  } else if (pin.length > 6) {
    errors.push('PIN must be no more than 6 digits');
  }

  // Check for weak patterns
  if (pin.length >= 4) {
    // Check for repeated digits (e.g., 1111, 2222)
    if (/^(\d)\1+$/.test(pin)) {
      errors.push('PIN cannot be all the same digit');
    }

    // Check for sequential patterns (e.g., 1234, 4321)
    const isAscending = pin.split('').every((digit, index) => 
      index === 0 || parseInt(digit) === parseInt(pin[index - 1]) + 1
    );
    const isDescending = pin.split('').every((digit, index) => 
      index === 0 || parseInt(digit) === parseInt(pin[index - 1]) - 1
    );

    if (isAscending || isDescending) {
      errors.push('PIN cannot be a sequential pattern');
    }

    // Check for common weak PINs
    const weakPins = ['1234', '4321', '0000', '1111', '2222', '3333', '4444', '5555', '6666', '7777', '8888', '9999'];
    if (weakPins.includes(pin)) {
      errors.push('PIN is too common, please choose a more secure PIN');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate PIN confirmation
 */
export const validatePinConfirmation = (pin: string, confirmPin: string): PinValidationResult => {
  const errors: string[] = [];

  if (!confirmPin || confirmPin.trim() === '') {
    errors.push('Please confirm your PIN');
    return { isValid: false, errors };
  }

  if (pin !== confirmPin) {
    errors.push('PINs do not match');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate OTP format
 */
export const validateOtp = (otp: string): PinValidationResult => {
  const errors: string[] = [];

  if (!otp || otp.trim() === '') {
    errors.push('OTP is required');
    return { isValid: false, errors };
  }

  if (!/^\d+$/.test(otp)) {
    errors.push('OTP must contain only numbers');
  }

  if (otp.length < 4 || otp.length > 6) {
    errors.push('OTP must be 4-6 digits');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Get PIN strength indicator
 */
export const getPinStrength = (pin: string): 'weak' | 'medium' | 'strong' => {
  if (pin.length < 4) return 'weak';
  
  const validation = validatePin(pin);
  if (!validation.isValid) return 'weak';
  
  // Check for additional strength criteria
  const hasVariedDigits = new Set(pin.split('')).size >= 3;
  const isNotCommon = !['1234', '4321', '0000', '1111', '2222', '3333', '4444', '5555', '6666', '7777', '8888', '9999'].includes(pin);
  
  if (pin.length >= 6 && hasVariedDigits && isNotCommon) {
    return 'strong';
  } else if (pin.length >= 5 && hasVariedDigits) {
    return 'medium';
  }
  
  return 'weak';
};

/**
 * Rate limiting for PIN attempts
 */
export class PinAttemptTracker {
  private static readonly MAX_ATTEMPTS = 5;
  private static readonly LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes
  private static readonly STORAGE_KEY = 'pin_attempts';

  static getAttemptData(): { count: number; lastAttempt: number; isLocked: boolean } {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      if (!data) {
        return { count: 0, lastAttempt: 0, isLocked: false };
      }

      const parsed = JSON.parse(data);
      const now = Date.now();
      const timeSinceLastAttempt = now - parsed.lastAttempt;

      // Reset if lockout period has passed
      if (parsed.isLocked && timeSinceLastAttempt > this.LOCKOUT_DURATION) {
        this.resetAttempts();
        return { count: 0, lastAttempt: 0, isLocked: false };
      }

      return {
        count: parsed.count || 0,
        lastAttempt: parsed.lastAttempt || 0,
        isLocked: parsed.isLocked || false
      };
    } catch {
      return { count: 0, lastAttempt: 0, isLocked: false };
    }
  }

  static recordFailedAttempt(): { isLocked: boolean; remainingAttempts: number; lockoutTimeRemaining?: number } {
    const currentData = this.getAttemptData();
    const newCount = currentData.count + 1;
    const now = Date.now();

    const isLocked = newCount >= this.MAX_ATTEMPTS;
    
    const newData = {
      count: newCount,
      lastAttempt: now,
      isLocked
    };

    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(newData));

    return {
      isLocked,
      remainingAttempts: Math.max(0, this.MAX_ATTEMPTS - newCount),
      lockoutTimeRemaining: isLocked ? this.LOCKOUT_DURATION : undefined
    };
  }

  static recordSuccessfulAttempt(): void {
    this.resetAttempts();
  }

  static resetAttempts(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }

  static getRemainingLockoutTime(): number {
    const data = this.getAttemptData();
    if (!data.isLocked) return 0;

    const now = Date.now();
    const timeSinceLastAttempt = now - data.lastAttempt;
    const remaining = this.LOCKOUT_DURATION - timeSinceLastAttempt;

    return Math.max(0, remaining);
  }

  static formatLockoutTime(milliseconds: number): string {
    const minutes = Math.ceil(milliseconds / (60 * 1000));
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
}