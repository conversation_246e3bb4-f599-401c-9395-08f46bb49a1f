import { Invoice, InvoiceItem } from '../services/invoices';

// Backend transaction interface based on the existing structure
export interface WalletTransaction {
  id: number;
  user_id: number;
  amount: number;
  reference_id: string;
  payment_provider: string;
  description: string;
  status_id: number;
  created_at: string;
  meta_data?: any;
  plaid_transfer_id?: string;
  type?: string;
}

// Status mapping from backend status_id to invoice status
export const statusMapping: Record<number, Invoice['status']> = {
  1: 'Paid',      // Completed transactions
  2: 'Sent',      // Pending transactions  
  3: 'Cancelled'  // Failed transactions
};

/**
 * Get transaction status including overdue logic
 */
export const getTransactionStatus = (transaction: WalletTransaction): Invoice['status'] => {
  // Check for overdue status on pending transactions
  if (transaction.status_id === 2 && transaction.meta_data?.dueDate) {
    const dueDate = new Date(transaction.meta_data.dueDate);
    const now = new Date();
    if (now > dueDate) {
      return 'Overdue';
    }
  }
  
  return statusMapping[transaction.status_id] || 'Sent';
};

/**
 * Generate invoice number from transaction data
 */
export const generateInvoiceNumber = (transaction: WalletTransaction): string => {
  // Always use the actual reference_id from the transaction
  if (transaction.reference_id) {
    return transaction.reference_id;
  }
  
  // Fallback: Generate invoice number from transaction ID and date only if no reference_id
  const date = new Date(transaction.created_at);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  
  return `TXN-${year}${month}-${String(transaction.id).padStart(4, '0')}`;
};

/**
 * Extract client information from transaction metadata
 */
export const extractClientInfo = (transaction: WalletTransaction) => {
  const metaData = transaction.meta_data || {};
  
  return {
    clientName: metaData.clientName || metaData.bankName || 'System Transaction',
    clientEmail: metaData.clientEmail || '<EMAIL>',
    clientPhone: metaData.clientPhone || '+****************',
    league: metaData.league || metaData.game_title || 'General',
    season: metaData.season || metaData.season_name || 'Current',
    avatar: metaData.avatar || 'https://ui-avatars.com/api/?name=System&background=6366f1&color=fff'
  };
};

/**
 * Create invoice items from transaction data
 */
export const createInvoiceItems = (transaction: WalletTransaction): InvoiceItem[] => {
  const metaData = transaction.meta_data || {};
  const amount = Math.abs(transaction.amount);
  
  // If items exist in metadata, use them
  if (metaData.items && Array.isArray(metaData.items)) {
    return metaData.items.map((item: any, index: number) => ({
      id: String(index + 1),
      description: item.description || 'Transaction Item',
      quantity: item.quantity || 1,
      unitPrice: item.unitPrice || amount,
      total: item.total || (item.quantity || 1) * (item.unitPrice || amount)
    }));
  }
  
  // Create single item from transaction
  return [{
    id: '1',
    description: transaction.description || 'Transaction',
    quantity: 1,
    unitPrice: amount,
    total: amount
  }];
};

/**
 * Format date to ISO string for invoice dates
 */
export const formatTransactionDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toISOString().split('T')[0]; // YYYY-MM-DD format
};

/**
 * Calculate due date from transaction (30 days from creation if not specified)
 */
export const calculateDueDate = (transaction: WalletTransaction): string => {
  const metaData = transaction.meta_data || {};
  
  if (metaData.dueDate) {
    return formatTransactionDate(metaData.dueDate);
  }
  
  // Default to 30 days from creation date
  const createdDate = new Date(transaction.created_at);
  const dueDate = new Date(createdDate);
  dueDate.setDate(dueDate.getDate() + 30);
  
  return formatTransactionDate(dueDate.toISOString());
};

/**
 * Main transformation function: Convert WalletTransaction to Invoice
 */
export const transformTransactionToInvoice = (transaction: WalletTransaction): Invoice => {
  const clientInfo = extractClientInfo(transaction);
  const items = createInvoiceItems(transaction);
  const metaData = transaction.meta_data || {};
  
  return {
    id: String(transaction.id),
    invoiceNumber: generateInvoiceNumber(transaction),
    clientName: clientInfo.clientName,
    clientEmail: clientInfo.clientEmail,
    clientPhone: clientInfo.clientPhone,
    league: clientInfo.league,
    season: clientInfo.season,
    amount: Math.abs(transaction.amount),
    issueDate: formatTransactionDate(transaction.created_at),
    dueDate: calculateDueDate(transaction),
    status: getTransactionStatus(transaction),
    description: transaction.description || 'Transaction',
    avatar: clientInfo.avatar,
    items: items,
    taxPercent: metaData.taxPercent || 0,
    discountPercent: metaData.discountPercent || 0,
    notes: metaData.notes || undefined
  };
};

/**
 * Transform array of transactions to invoices
 */
export const transformTransactionsToInvoices = (transactions: WalletTransaction[]): Invoice[] => {
  return transactions.map(transformTransactionToInvoice);
};

/**
 * Filter transactions by search term
 */
export const filterTransactionsBySearch = (transactions: WalletTransaction[], searchTerm: string): WalletTransaction[] => {
  if (!searchTerm.trim()) return transactions;
  
  const term = searchTerm.toLowerCase();
  
  return transactions.filter(transaction => {
    const metaData = transaction.meta_data || {};
    
    return (
      transaction.description?.toLowerCase().includes(term) ||
      transaction.reference_id?.toLowerCase().includes(term) ||
      metaData.clientName?.toLowerCase().includes(term) ||
      metaData.bankName?.toLowerCase().includes(term) ||
      metaData.league?.toLowerCase().includes(term) ||
      metaData.game_title?.toLowerCase().includes(term)
    );
  });
};

/**
 * Filter transactions by status
 */
export const filterTransactionsByStatus = (transactions: WalletTransaction[], status: string): WalletTransaction[] => {
  if (!status || status === 'All') return transactions;
  
  return transactions.filter(transaction => {
    const transactionStatus = getTransactionStatus(transaction);
    return transactionStatus === status;
  });
};