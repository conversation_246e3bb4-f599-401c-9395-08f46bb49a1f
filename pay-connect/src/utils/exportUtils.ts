import { Invoice } from '../services/invoices';

/**
 * Export transactions to CSV format
 */
export const exportToCSV = (invoices: Invoice[], filename: string = 'transactions'): void => {
  try {
    // Define CSV headers
    const headers = [
      'Transaction ID',
      'Date',
      'Client Name',
      'Email',
      'Phone',
      'League',
      'Season',
      'Amount',
      'Status',
      'Description',
      'Due Date'
    ];

    // Convert invoices to CSV rows
    const csvRows = invoices.map(invoice => [
      invoice.invoiceNumber,
      new Date(invoice.issueDate).toLocaleDateString(),
      invoice.clientName,
      invoice.clientEmail,
      invoice.clientPhone,
      invoice.league,
      invoice.season,
      `$${invoice.amount.toFixed(2)}`,
      invoice.status,
      invoice.description,
      new Date(invoice.dueDate).toLocaleDateString()
    ]);

    // Combine headers and rows
    const csvContent = [headers, ...csvRows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    console.log(`Exported ${invoices.length} transactions to CSV`);
  } catch (error) {
    console.error('Error exporting to CSV:', error);
    throw new Error('Failed to export transactions to CSV');
  }
};

/**
 * Export transactions to Excel format (using CSV with .xlsx extension)
 */
export const exportToExcel = (invoices: Invoice[], filename: string = 'transactions'): void => {
  try {
    // Create Excel-compatible CSV content
    const headers = [
      'Transaction ID',
      'Date',
      'Client Name',
      'Email',
      'Phone',
      'League',
      'Season',
      'Amount',
      'Status',
      'Description',
      'Due Date',
      'Items Count',
      'Tax %',
      'Discount %'
    ];

    const csvRows = invoices.map(invoice => [
      invoice.invoiceNumber,
      new Date(invoice.issueDate).toLocaleDateString(),
      invoice.clientName,
      invoice.clientEmail,
      invoice.clientPhone,
      invoice.league,
      invoice.season,
      invoice.amount.toFixed(2),
      invoice.status,
      invoice.description,
      new Date(invoice.dueDate).toLocaleDateString(),
      invoice.items.length.toString(),
      invoice.taxPercent.toString(),
      invoice.discountPercent.toString()
    ]);

    // Add BOM for Excel compatibility
    const csvContent = '\uFEFF' + [headers, ...csvRows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'application/vnd.ms-excel;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.xlsx`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    console.log(`Exported ${invoices.length} transactions to Excel`);
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    throw new Error('Failed to export transactions to Excel');
  }
};

/**
 * Export transactions to JSON format
 */
export const exportToJSON = (invoices: Invoice[], filename: string = 'transactions'): void => {
  try {
    const jsonContent = JSON.stringify(invoices, null, 2);
    
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.json`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    console.log(`Exported ${invoices.length} transactions to JSON`);
  } catch (error) {
    console.error('Error exporting to JSON:', error);
    throw new Error('Failed to export transactions to JSON');
  }
};

/**
 * Export filtered transactions with format selection
 */
export const exportTransactions = async (
  format: 'csv' | 'excel' | 'json',
  filters?: {
    status?: string;
    search?: string;
  }
): Promise<void> => {
  try {
    // Import the getInvoices function dynamically to avoid circular imports
    const { getInvoices } = await import('../services/invoices');
    
    // Get all transactions with current filters
    const result = await getInvoices({
      ...filters,
      limit: 1000 // Get a large number for export
    });
    
    const invoices = result.data;
    
    if (invoices.length === 0) {
      throw new Error('No transactions found to export');
    }
    
    // Generate filename based on filters
    let filename = 'transactions';
    if (filters?.status && filters.status !== 'All') {
      filename += `_${filters.status.toLowerCase()}`;
    }
    if (filters?.search) {
      filename += `_filtered`;
    }
    
    // Export based on format
    switch (format) {
      case 'csv':
        exportToCSV(invoices, filename);
        break;
      case 'excel':
        exportToExcel(invoices, filename);
        break;
      case 'json':
        exportToJSON(invoices, filename);
        break;
      default:
        throw new Error('Unsupported export format');
    }
    
  } catch (error) {
    console.error('Error exporting transactions:', error);
    throw error;
  }
};

/**
 * Generate summary report for export
 */
export const generateSummaryReport = (invoices: Invoice[]): string => {
  const totalTransactions = invoices.length;
  const totalAmount = invoices.reduce((sum, inv) => sum + inv.amount, 0);
  
  const statusCounts = invoices.reduce((acc, inv) => {
    acc[inv.status] = (acc[inv.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const statusAmounts = invoices.reduce((acc, inv) => {
    acc[inv.status] = (acc[inv.status] || 0) + inv.amount;
    return acc;
  }, {} as Record<string, number>);
  
  return `
TRANSACTION SUMMARY REPORT
Generated: ${new Date().toLocaleString()}

OVERVIEW:
- Total Transactions: ${totalTransactions}
- Total Amount: $${totalAmount.toFixed(2)}

STATUS BREAKDOWN:
${Object.entries(statusCounts).map(([status, count]) => 
  `- ${status}: ${count} transactions ($${(statusAmounts[status] || 0).toFixed(2)})`
).join('\n')}

AVERAGE TRANSACTION: $${totalTransactions > 0 ? (totalAmount / totalTransactions).toFixed(2) : '0.00'}
  `;
};