import React, { useState, useEffect } from 'react';
import SummaryCard from '../components/Invoices/SummaryCard';
import SearchBar from '../components/Invoices/SearchBar';
import FilterAndExportControls from '../components/Invoices/FilterAndExportControls';
import InvoicesTable from '../components/Invoices/InvoicesTable';
import Offcanvas from '../components/common/Offcanvas';
import InvoiceDetailOffcanvas from '../components/Invoices/InvoiceDetailOffcanvas';
import ExportModal from '../components/Invoices/ExportModal';
import { getInvoiceSummary, getInvoices } from '../services/invoices';
import PaymentLoader from '../components/common/PaymentLoader';
import Notification from '../components/common/Notification';
import { FileText, CheckCircle, Clock, AlertCircle } from 'lucide-react';

const Invoices: React.FC = () => {
  const [selectedCard, setSelectedCard] = useState('totalInvoices');
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [summary, setSummary] = useState({
    totalInvoices: 0,
    totalAmount: 0,
    paidInvoices: 0,
    paidAmount: 0,
    pendingInvoices: 0,
    pendingAmount: 0,
    overdueInvoices: 0,
    overdueAmount: 0,
    draftInvoices: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOffcanvasOpen, setIsOffcanvasOpen] = useState(false);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [transactionCount, setTransactionCount] = useState(0);
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'info';
    message: string;
  } | null>(null);

  useEffect(() => {
    fetchSummary();
    fetchTransactionCount();
  }, []);

  useEffect(() => {
    fetchTransactionCount();
  }, [search, statusFilter]);

  const fetchSummary = async () => {
    try {
      setLoading(true);
      const data = await getInvoiceSummary();
      setSummary(data);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch transaction summary';
      setError(errorMessage);
      console.error('Error fetching summary:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchTransactionCount = async () => {
    try {
      const result = await getInvoices({
        search: search || undefined,
        status: statusFilter !== 'All' ? statusFilter : undefined,
        limit: 1000 // Get all for count
      });
      setTransactionCount(result.data.length);
    } catch (err) {
      console.error('Error fetching transaction count:', err);
      setTransactionCount(0);
    }
  };

  const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleCardClick = (card: string, status?: string) => {
    setSelectedCard(card);
    if (status) {
      setStatusFilter(status);
    }
  };

  const handleViewDetails = (id: string) => {
    setSelectedInvoiceId(id);
    setIsOffcanvasOpen(true);
  };

  const handleCloseOffcanvas = () => {
    setIsOffcanvasOpen(false);
    setSelectedInvoiceId(null);
  };

  // Removed create invoice functionality - this is transaction history only

  const handleExport = () => {
    setIsExportModalOpen(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center [min-height:765px]">
        <PaymentLoader
          type="processing"
          message="Loading transaction management..."
          size="large"
          showQuotes={true}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <button
              onClick={fetchSummary}
              className="ml-4 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="">
      <h2 className="text-2xl font-bold text-gray-900 mb-5">Transaction History</h2>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <SummaryCard
          title="Total Transactions"
          value={summary.totalAmount}
          count={summary.totalInvoices}
          icon={<FileText />}
          // className="bg-blue-100"
          onClick={() => handleCardClick('totalInvoices')}
          selected={selectedCard === 'totalInvoices'}
        />
        <SummaryCard
          title="Completed Transactions"
          value={summary.paidAmount}
          count={summary.paidInvoices}
          icon={<CheckCircle />}
          // className="bg-green-100"
          onClick={() => handleCardClick('paidInvoices', 'Paid')}
          selected={selectedCard === 'paidInvoices'}
        />
        <SummaryCard
          title="Pending Transactions"
          value={summary.pendingAmount}
          count={summary.pendingInvoices}
          icon={<Clock />}
          // className="bg-yellow-100"
          onClick={() => handleCardClick('pendingInvoices', 'Sent')}
          selected={selectedCard === 'pendingInvoices'}
        />
        <SummaryCard
          title="Overdue Transactions"
          value={summary.overdueAmount}
          count={summary.overdueInvoices}
          icon={<AlertCircle />}
          // className="bg-red-100"
          onClick={() => handleCardClick('overdueInvoices', 'Overdue')}
          selected={selectedCard === 'overdueInvoices'}
        />
      </div>


      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 md:space-x-4 mb-8">
        <SearchBar className="w-full md:w-1/2" onSearch={setSearch} />
        <FilterAndExportControls
          className="w-full md:w-1/2 lg:w-1/3"
          onFilterChange={(status) => {
            setStatusFilter(status);
            setSelectedCard(''); // Deselect card when filter is changed manually
          }}
          onExport={handleExport}
          currentFilter={statusFilter}
        />
      </div>

      {/* Invoices Table */}
      <InvoicesTable
        className="bg-white rounded-xl shadow-sm border"
        search={search}
        statusFilter={statusFilter}
        onViewDetails={handleViewDetails}
        onDataChange={fetchSummary}
        onNotification={showNotification}
      />

      {/* Offcanvas for details */}
      <Offcanvas
        isOpen={isOffcanvasOpen}
        onClose={handleCloseOffcanvas}
        title="Invoice Details"
        width="w-97"
      >
        <InvoiceDetailOffcanvas
          invoiceId={selectedInvoiceId}
          onClose={handleCloseOffcanvas}
          onNotification={showNotification}
        />
      </Offcanvas>

      {/* Export Modal */}
      <ExportModal
        isOpen={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        currentFilters={{
          status: statusFilter !== 'All' ? statusFilter : undefined,
          search: search || undefined
        }}
        transactionCount={transactionCount}
        onSuccess={(message) => showNotification('success', message)}
        onError={(message) => showNotification('error', message)}
      />

      {/* Notification */}
      {notification && (
        <Notification
          type={notification.type}
          message={notification.message}
          onClose={() => setNotification(null)}
        />
      )}
    </div>
  );
};

export default Invoices;