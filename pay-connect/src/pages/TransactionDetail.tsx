import React, { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { getTransactionById, type Transaction } from '../services/transactions';
import Breadcrumb from '../components/common/Breadcrumb';
import { Download } from 'lucide-react';
import html2pdf from 'html2pdf.js';
import PaymentLoader from '../components/common/PaymentLoader';

const TransactionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);

  // 👇 Ref to the invoice section
  const invoiceRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (id) {
      fetchTransactionDetails(id);
    }
  }, [id]);

  const fetchTransactionDetails = async (transactionId: string) => {
    try {
      setLoading(true);
      const data = await getTransactionById(transactionId);
      setTransaction(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch transaction details');
      console.error('Transaction detail fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = () => {
    if (!invoiceRef.current) return;
    setIsDownloading(true);
    const opt = {
      margin: 0.5,
      filename: `Invoice_${transaction?.transactionId}.pdf`,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: { scale: 2 },
      jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' },
    };
    (html2pdf() as any).set(opt).from(invoiceRef.current).save();
    setTimeout(() => setIsDownloading(false), 2000); // Show icon again after 2s
  };

  const getStatusColor = (status: Transaction['status']) => {
    switch (status) {
      case 'Received':
      case 'Paid':
        return 'text-green-600 bg-green-100';
      case 'Pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'Refund':
        return 'text-blue-600 bg-blue-100';
      case 'Failed':
        return 'text-red-600 bg-red-100';
      case 'Withdrawal':
        return 'text-orange-600 bg-orange-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center  [min-height:765px]">

        <PaymentLoader
          type="transaction"
          size="medium"
          showQuotes={true}
        />

      </div>

    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!transaction) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-gray-600">Transaction not found.</div>
      </div>
    );
  }

  const breadcrumbItems = [
    { label: 'Dashboard', path: '/dashboard' },
    { label: 'Transactions', path: '/transactions' },
    { label: transaction.transactionId },
  ];

  return (
    <div className="space-y-6">
      <Breadcrumb items={breadcrumbItems} />

      {/* 👇 Invoice Content to be downloaded */}
      <div ref={invoiceRef} className="bg-white rounded-xl shadow-sm border">
        <div className="bg-[#2F94ED] rounded-t-xl">
          <div className="flex items-center justify-between p-6">
            <h2 className="text-2xl font-bold text-white">
              Transaction Details: {transaction.transactionId}
            </h2>
            {!isDownloading && (
              <button onClick={handleDownload} className="text-white hover:text-gray-200">
                <Download className="w-6 h-6" />
              </button>
            )}
          </div>
        </div>

        <div className="my-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4 p-6 gap-x-6 text-gray-700">
            <div>
              <p className="text-sm font-medium text-gray-500">Transaction ID</p>
              <p className="text-lg font-semibold border-b border-dashed border-gray-300">
                {transaction.reference}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Date</p>
              <p className="text-lg font-semibold border-b border-dashed border-gray-300">
                {new Date(transaction.date).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Paid By / Received From</p>
              <p className="text-lg font-semibold border-b border-dashed border-gray-300">
                {transaction.paidByReceivedFrom}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Paid To</p>
              <p className="text-lg font-semibold border-b border-dashed border-gray-300">
                {transaction.paidTo}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Type</p>
              <p className="text-lg font-semibold border-b border-dashed border-gray-300">
                {transaction.type}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Amount</p>
              <p className="text-lg font-semibold border-b border-dashed border-gray-300">
                ${transaction.amount.toFixed(2)}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <p className="text-sm font-medium text-gray-500">Status</p>
              <span
                className={`px-3 py-1 rounded-full text-sm font-semibold ${getStatusColor(
                  transaction.status
                )}`}
              >
                {transaction.status}
              </span>
            </div>
            
            {/* Payment Method Information */}
            {transaction.paymentMethod && (
              <div>
                <p className="text-sm font-medium text-gray-500">Payment Method</p>
                <div className="flex flex-col">
                  <p className="text-lg font-semibold border-b border-dashed border-gray-300">
                    {transaction.paymentMethod.name}
                  </p>
                  <div className="mt-1 flex items-center text-xs text-gray-500">
                    <span className="mr-2">{transaction.paymentMethod.processingTime}</span>
                    {transaction.paymentMethod.fee !== null && transaction.paymentMethod.fee > 0 && (
                      <>
                        <span className="mx-1">•</span>
                        <span>Fee: ${transaction.paymentMethod.fee.toFixed(2)}</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            )}
            
            {/* Bank Account Information - Show for bank transfers */}
            {transaction.metadata && (transaction.metadata.bank_name || transaction.metadata.bankName) && (
              <div>
                <p className="text-sm font-medium text-gray-500">Bank Account</p>
                <div className="flex flex-col">
                  <p className="text-lg font-semibold border-b border-dashed border-gray-300">
                    {transaction.metadata.bank_name || transaction.metadata.bankName}
                  </p>
                  <div className="mt-1 flex items-center text-xs text-gray-500">
                    <span className="mr-2">
                      Account: ****{(transaction.metadata.account_number || transaction.metadata.accountNumber || '').toString().slice(-4)}
                    </span>
                    {(transaction.metadata.account_type || transaction.metadata.accountType) && (
                      <>
                        <span className="mx-1">•</span>
                        <span className="capitalize">{transaction.metadata.account_type || transaction.metadata.accountType}</span>
                      </>
                    )}
                    {transaction.metadata.transaction_date && (
                      <>
                        <span className="mx-1">•</span>
                        <span>
                          {new Date(transaction.metadata.transaction_date as string).toLocaleDateString()}
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionDetail;
