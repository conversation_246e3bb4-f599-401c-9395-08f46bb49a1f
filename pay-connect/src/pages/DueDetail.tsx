import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { getStaffDetails, getStaffTransactions, type StaffDetail } from '../services/staffDetails';
import { getStaffBankAccountsById, type StaffBankAccount } from '../services/staff';
import ProfileCard from '../components/StaffDetails/ProfileCard';
import PaymentSummaryCard from '../components/StaffDetails/PaymentSummaryCard';
import Tabs from '../components/StaffDetails/Tabs';
import StaffTransactionsTable from '../components/StaffDetails/StaffTransactionsTable';
import SearchBar from '../components/Transactions/SearchBar'; // Reusing existing search bar
import FilterAndExportControls from '../components/Transactions/FilterAndExportControls'; // Reusing filter
import Breadcrumb from '../components/common/Breadcrumb';
import PaymentLoader from '../components/common/PaymentLoader';
import StaffBankAccounts from '../components/Staff/StaffBankAccounts';
import { Plus, Building2, Star, CreditCard, User, Hash, Calendar } from 'lucide-react';

const DueDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [staff, setStaff] = useState<StaffDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'gameActivity' | 'transactions' | 'bankAccounts'>('transactions');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [transactionCount, setTransactionCount] = useState(0);
  const [showAddBankModal, setShowAddBankModal] = useState(false);
  const [bankAccountsCount, setBankAccountsCount] = useState(0);

  useEffect(() => {
    console.log('StaffDetail: id from useParams:', id);
    if (id && id !== 'undefined') {
      fetchStaffDetails(id);
      fetchTransactionCount(id);
    } else {
      console.error('StaffDetail: Invalid or missing id:', id);
      setError('Invalid staff ID');
      setLoading(false);
    }
  }, [id]);

  const fetchStaffDetails = async (staffId: string) => {
    try {
      setLoading(true);
      const data = await getStaffDetails(staffId);
      setStaff(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch staff details');
    } finally {
      setLoading(false);
    }
  };

  const fetchTransactionCount = async (staffId: string) => {
    try {
      // Fetch just the first page to get the total count
      const response = await getStaffTransactions(staffId, { limit: 1, offset: 0 });
      setTransactionCount(response.pagination.total);
    } catch (err) {
      console.error('Failed to fetch transaction count:', err);
      setTransactionCount(0);
    }
  };

  const handleExport = () => {
    alert('Export functionality will be implemented here.');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center [min-height:765px] ">
        <PaymentLoader
          type="setup"
          message="Loading staff management dashboard..."
          size="large"
          showQuotes={true}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!staff) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-gray-600">Staff member not found.</div>
      </div>
    );
  }

  const breadcrumbItems = [
    { label: 'Dashboard', path: '/dashboard' },
    { label: 'Staff', path: '/staff' },
    { label: staff.full_name },
  ];

  console.log(staff.upcomingPayment, 'staff')

  return (
    <div className="space-y-6">
      <Breadcrumb items={breadcrumbItems} />

      {/* Profile and Payment Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ProfileCard staff={staff} />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <PaymentSummaryCard
            title="Total Payment Paid"
            value={staff?.totalPaymentPaid}
            dateLabel="Last Paid"
            date={staff.lastPaidDate}
          />
        </div>

      </div>

      {/* Tabs and Controls */}
      <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 md:space-x-4">
        <Tabs
          activeTab={activeTab}
          gameActivityCount={staff.gameActivityCount}
          transactionsCount={transactionCount}
          bankAccountsCount={bankAccountsCount}
          onTabChange={setActiveTab}
        />
        <div className="flex items-center space-x-4 w-full md:w-auto">
          <SearchBar className="flex-grow" onSearch={setSearchTerm} />
          <FilterAndExportControls
            className="w-auto"
            onFilterChange={setStatusFilter}
            onExport={handleExport}
            currentFilter={statusFilter}
          />
        </div>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'gameActivity' && (
        <div className="bg-white rounded-xl shadow-sm border p-6 text-center py-12">
          <p className="text-gray-500">Game Activity details will be displayed here.</p>
        </div>
      )}

      {activeTab === 'transactions' && (
        <StaffTransactionsTable
          staffId={id!}
          searchTerm={searchTerm}
          statusFilter={statusFilter}
          className="bg-white rounded-xl shadow-sm border"
        />
      )}

      {activeTab === 'bankAccounts' && (
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Bank Accounts</h3>
            <button
              onClick={() => setShowAddBankModal(true)}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
              title={bankAccountsCount > 0 ? "Update existing bank account" : "Add new bank account"}
            >
              <Plus size={16} className="mr-2" />
              {bankAccountsCount > 0 ? "Update Bank Account" : "Add Bank Account"}
            </button>
          </div>
          <StaffBankAccounts
            staffId={id!}
            staffName={staff?.full_name}
            showAddButton={false}
            showAddBankModal={showAddBankModal}
            setShowAddBankModal={setShowAddBankModal}
            onAccountsCountChange={setBankAccountsCount}
          />
        </div>
      )}
    </div>
  );
};

export default DueDetail; 
