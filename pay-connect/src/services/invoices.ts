import api from './api';
import {
  transformTransactionsToInvoices,
  filterTransactionsBySearch,
  filterTransactionsByStatus,
  WalletTransaction
} from '../utils/transactionTransform';
import { retryApiCall, getTransactionErrorMessage, logError } from '../utils/errorHandling';

export interface Invoice {
  id: string;
  invoiceNumber: string;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  league: string;
  season: string;
  amount: number;
  issueDate: string;
  dueDate: string;
  status: 'Draft' | 'Sent' | 'Paid' | 'Overdue' | 'Cancelled';
  description: string;
  avatar: string;
  items: InvoiceItem[];
  taxPercent: number;
  discountPercent: number;
  notes?: string;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}



export const getInvoices = async (filters?: {
  status?: string;
  search?: string;
  page?: number;
  limit?: number;
}): Promise<{ data: Invoice[]; totalPages: number }> => {
  try {
    // Use retry mechanism for API call
    const response = await retryApiCall(async () => {
      const params = {
        limit: filters?.limit || 50,
        offset: ((filters?.page || 1) - 1) * (filters?.limit || 50)
      };

      return await api.get('/wallet/transactions', { params });
    });

    // The API returns formatted transactions, we need to convert them back to the expected format
    // Handle different possible response structures
    let apiTransactions = [];

    if (response.data.data?.transactions) {
      apiTransactions = response.data.data.transactions;
    } else if (response.data.transactions) {
      apiTransactions = response.data.transactions;
    } else if (Array.isArray(response.data)) {
      apiTransactions = response.data;
    }

    console.log('Full API Response:', response.data);
    console.log('Extracted API Transactions:', apiTransactions);
    console.log('Transaction count:', apiTransactions.length);

    // Convert API format to WalletTransaction format for our transformation utility
    let transactions: WalletTransaction[] = apiTransactions.map((tx: any) => ({
      id: tx.id,
      user_id: 0, // Not provided by API
      amount: tx.type === 'credit' ? tx.amount : -tx.amount,
      reference_id: tx.reference,
      payment_provider: tx.provider,
      description: tx.description,
      status_id: tx.status === 'completed' ? 1 : 2,
      created_at: tx.date,
      meta_data: tx.metadata,
      type: tx.type
    }));

    // Apply client-side filtering for search and status
    if (filters?.search) {
      transactions = filterTransactionsBySearch(transactions, filters.search);
    }

    if (filters?.status && filters.status !== 'All') {
      transactions = filterTransactionsByStatus(transactions, filters.status);
    }

    // Transform transactions to invoices
    const invoices = transformTransactionsToInvoices(transactions);

    console.log('Transformed Invoices:', invoices);
    console.log('Applied Filters:', filters);

    const totalPages = Math.ceil(invoices.length / (filters?.limit || 10));

    return {
      data: invoices,
      totalPages
    };
  } catch (error) {
    logError(error, 'getInvoices');
    throw new Error(getTransactionErrorMessage(error));
  }
};

export const getInvoiceById = async (id: string): Promise<Invoice> => {
  try {
    // Get specific transaction by ID with retry mechanism
    const response = await retryApiCall(async () => {
      return await api.get('/wallet/transactions');
    });

    // Handle different possible response structures
    let apiTransactions = [];

    if (response.data.data?.transactions) {
      apiTransactions = response.data.data.transactions;
    } else if (response.data.transactions) {
      apiTransactions = response.data.transactions;
    } else if (Array.isArray(response.data)) {
      apiTransactions = response.data;
    }

    // Convert API format to WalletTransaction format
    const transactions: WalletTransaction[] = apiTransactions.map((tx: any) => ({
      id: tx.id,
      user_id: 0, // Not provided by API
      amount: tx.type === 'credit' ? tx.amount : -tx.amount,
      reference_id: tx.reference,
      payment_provider: tx.provider,
      description: tx.description,
      status_id: tx.status === 'completed' ? 1 : 2,
      created_at: tx.date,
      meta_data: tx.metadata,
      type: tx.type
    }));

    // Find the transaction with matching ID
    const transaction = transactions.find(t => t.id.toString() === id);
    if (!transaction) {
      const error = new Error('Transaction not found');
      (error as any).status = 404;
      throw error;
    }

    // Transform to invoice format
    const invoices = transformTransactionsToInvoices([transaction]);
    return invoices[0];
  } catch (error) {
    logError(error, 'getInvoiceById');
    throw new Error(getTransactionErrorMessage(error));
  }
};

export const updateInvoiceStatus = async (id: string, status: Invoice['status']): Promise<Invoice> => {
  try {
    // For transaction history, we can't actually update the status
    // Instead, we'll return the current transaction with updated status for UI purposes
    const currentInvoice = await getInvoiceById(id);

    // In a real implementation, this might update a separate invoice status table
    // For now, we'll just return the invoice with the new status
    return {
      ...currentInvoice,
      status
    };
  } catch (error) {
    logError(error, 'updateInvoiceStatus');
    throw new Error('Failed to update invoice status');
  }
};

export const createInvoice = async (invoiceData: Omit<Invoice, 'id' | 'invoiceNumber'>): Promise<Invoice> => {
  // Add artificial delay to show the engaging loader
  await new Promise(resolve => setTimeout(resolve, 1200));
  // For development, return mock data with generated ID and invoice number
  const newInvoice: Invoice = {
    ...invoiceData,
    id: Date.now().toString(),
    invoiceNumber: `INV-2025-${String(Date.now()).slice(-3)}`
  };

  return Promise.resolve(newInvoice);

  // When API is ready, uncomment this:
  // const response = await api.post('/invoices', invoiceData);
  // return response.data;
};

export const getInvoiceSummary = async () => {
  try {
    const invoices = await getInvoices();

    return {
      totalInvoices: invoices.data.length,
      totalAmount: invoices.data.reduce((sum, i) => sum + i.amount, 0),
      paidInvoices: invoices.data.filter(i => i.status === 'Paid').length,
      paidAmount: invoices.data
        .filter(i => i.status === 'Paid')
        .reduce((sum, i) => sum + i.amount, 0),
      pendingInvoices: invoices.data.filter(i => i.status === 'Sent').length,
      pendingAmount: invoices.data
        .filter(i => i.status === 'Sent')
        .reduce((sum, i) => sum + i.amount, 0),
      overdueInvoices: invoices.data.filter(i => i.status === 'Overdue').length,
      overdueAmount: invoices.data
        .filter(i => i.status === 'Overdue')
        .reduce((sum, i) => sum + i.amount, 0),
      draftInvoices: invoices.data.filter(i => i.status === 'Draft').length
    };
  } catch (error) {
    logError(error, 'getInvoiceSummary');
    throw new Error('Failed to fetch invoice summary');
  }
};
