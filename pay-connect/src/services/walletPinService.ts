import api from './api';

// TypeScript interfaces for request/response types
export interface PinChangeRequest {
  currentPin: string;
  newPin: string;
  confirmPin: string;
  otp: string;
}

export interface PinResetRequest {
  newPin: string;
  confirmPin: string;
  otp: string;
}

export interface PinVerificationRequest {
  pin: string;
}

export interface OtpRequest {
  // No additional fields needed for OTP requests
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

export interface PinStatusResponse {
  hasPinSetup: boolean;
  lastChanged?: string;
  requiresOtp: boolean;
}

// Backend response interface (what the API actually returns)
interface BackendPinStatusResponse {
  requiresPinSetup: boolean;
}

/**
 * Wallet PIN Service
 * Handles all wallet PIN related API operations
 */
export class WalletPinService {
  
  /**
   * Change wallet PIN with current PIN and OTP verification
   */
  static async changePin(request: PinChangeRequest): Promise<ApiResponse> {
    try {
      const response = await api.post('/wallet/change-pin', {
        currentPin: request.currentPin,
        newPin: request.newPin,
        otpCode: request.otp  // Backend expects 'otpCode', not 'otp'
        // Note: confirmPin is not needed by backend, validation happens on frontend
      });
      
      return {
        success: true,
        message: response.data.message || 'PIN changed successfully',
        data: response.data
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to change PIN',
        error: error.response?.data?.error || error.message
      };
    }
  }

  /**
   * Reset wallet PIN with OTP verification (for forgotten PIN)
   */
  static async resetPin(request: PinResetRequest): Promise<ApiResponse> {
    try {
      const response = await api.post('/wallet/reset-pin', {
        newPin: request.newPin,
        otpCode: request.otp  // Backend expects 'otpCode', not 'otp'
      });
      
      return {
        success: true,
        message: response.data.message || 'PIN reset successfully',
        data: response.data
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to reset PIN',
        error: error.response?.data?.error || error.message
      };
    }
  }

  /**
   * Send OTP for PIN change operation
   */
  static async sendChangeOtp(): Promise<ApiResponse> {
    try {
      const response = await api.post('/wallet/send-pin-change-otp');
      
      return {
        success: true,
        message: response.data.message || 'OTP sent successfully',
        data: response.data
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to send OTP',
        error: error.response?.data?.error || error.message
      };
    }
  }

  /**
   * Send OTP for PIN reset operation
   */
  static async sendResetOtp(): Promise<ApiResponse> {
    try {
      const response = await api.post('/wallet/send-pin-reset-otp');
      
      return {
        success: true,
        message: response.data.message || 'OTP sent successfully',
        data: response.data
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to send OTP',
        error: error.response?.data?.error || error.message
      };
    }
  }

  /**
   * Verify current PIN before allowing changes
   */
  static async verifyCurrentPin(request: PinVerificationRequest): Promise<ApiResponse> {
    try {
      const response = await api.post('/wallet/verify-pin', {
        pin: request.pin
      });
      
      return {
        success: true,
        message: response.data.message || 'PIN verified successfully',
        data: response.data
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Invalid PIN',
        error: error.response?.data?.error || error.message
      };
    }
  }

  /**
   * Get wallet PIN status and information
   */
  static async getPinStatus(): Promise<ApiResponse<PinStatusResponse>> {
    try {
      const response = await api.get('/wallet/pin-status');
      const backendData = response.data as BackendPinStatusResponse;
      
      // Transform backend response to frontend format
      // Backend returns requiresPinSetup (true = needs setup)
      // Frontend expects hasPinSetup (true = already has PIN)
      const transformedData: PinStatusResponse = {
        hasPinSetup: !backendData.requiresPinSetup, // Invert the boolean
        lastChanged: undefined, // Backend doesn't provide this yet
        requiresOtp: true // Always require OTP for PIN operations
      };
      
      return {
        success: true,
        message: 'PIN status retrieved successfully',
        data: transformedData
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to get PIN status',
        error: error.response?.data?.error || error.message
      };
    }
  }
}

// Export individual functions for easier importing
export const {
  changePin,
  resetPin,
  sendChangeOtp,
  sendResetOtp,
  verifyCurrentPin,
  getPinStatus
} = WalletPinService;

// Default export
export default WalletPinService;