import api from './api';

export interface StaffMember {
  id: string;
  name: string;
  email: string;
  contact: string;
  role: 'Staff' | 'Referee';
  amount: number;
  avatar: string; // URL to an avatar image
}

export interface ImportableUser {
  id: string;
 added_by:number;
  name: string;
  email: string;
  contact: string;
  department: string;
  position: string;
  avatar: string;
  isSelected?: boolean;
}

export interface StaffBankAccount {
  id: number;
  staffId: number;
  staffName: string;
  bankName: string;
  accountNumber: string;
  accountType: string;
  accountHolderName: string;
  status: 'active' | 'inactive';
  isPrimary: boolean;
  createdAt: string;
}

export interface BankAccountData {
  bankName: string;
  accountNumber: string;
  routingNumber: string;
  accountType: 'checking' | 'savings';
  accountHolderName: string;
}

export const getStaffMembers = async (filters: { role?: string, search?: string, page?: number, limit?: number }): Promise<{ data: StaffMember[], totalPages: number }> => {
  try {
    const response = await api.get(`/staff/team`);
    let filteredData = response.data.data || response.data;

    if (filters.role && filters.role !== 'All') {
      filteredData = filteredData.filter((staff: StaffMember) => staff.role === filters.role);
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredData = filteredData.filter((staff: StaffMember) =>
        staff.name.toLowerCase().includes(searchTerm) ||
        staff.email.toLowerCase().includes(searchTerm) ||
        staff.contact.toLowerCase().includes(searchTerm)
      );
    }

    const limit = filters.limit || 10;
    const totalPages = Math.ceil(filteredData.length / limit);
    
    // Apply pagination if requested
    if (filters.page && filters.limit) {
      const startIndex = (filters.page - 1) * filters.limit;
      filteredData = filteredData.slice(startIndex, startIndex + filters.limit);
    }
    
    return { data: filteredData, totalPages };
  } catch (error) {
    console.error('Error fetching staff members:', error);
    throw error;
  }
};

export const getStaffSummary = async () => {
  const { data: staffMembers } = await getStaffMembers({});
  return {
    totalStaff: staffMembers.length,
    referee: staffMembers.filter(staff => staff.role === 'Referee').length,
    Staff: staffMembers.filter(staff => staff.role === 'Staff').length || 0,
    totalAmountDue: staffMembers.reduce((sum, staff) => sum + staff.amount, 0)
  };
};



// Get importable users from external database
export const getImportableUsers = async (filters: { department?: string, search?: string }): Promise<ImportableUser[]> => {
  try {
    const params = new URLSearchParams();

    if (filters.department && filters.department !== 'All') {
      params.append('department', filters.department);
    }

    if (filters.search) {
      params.append('search', filters.search);
    }

    const response = await api.get(`/staff/staff?${params.toString()}`);

    return response.data.data || response.data;
  } catch (error) {
    console.error('Error fetching importable users:', error);
    throw error; // Re-throw the error instead of falling back to mock data
  }
};

// Import selected users as staff members
export const importUsers = async (selectedUsers: ImportableUser[], defaultRole: 'Staff' | 'Referee'): Promise<{ success: boolean; imported: number; message: string }> => {
  try {
    const payload = {
      users: selectedUsers.map(user => ({
        id: user.id,
        added_by:user.added_by,
        name: user.name,
        email: user.email,
        contact: user.contact,
        department: user.department,
        position: user.position,
        avatar: user.avatar,
        role: defaultRole
      })),
      defaultRole
    };

    const response = await api.post('/staff/import-users', payload);

    const result = response.data;

    return {
      success: result.success || true,
      imported: result.imported || selectedUsers.length,
      message: result.message || `Successfully imported ${selectedUsers.length} user${selectedUsers.length !== 1 ? 's' : ''} as staff members.`
    };
  } catch (error) {
    console.error('Error importing users:', error);

    let errorMessage = 'Failed to import users. Please try again.';

    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: { message?: string } } };
      errorMessage = axiosError.response?.data?.message || errorMessage;
    }

    return {
      success: false,
      imported: 0,
      message: errorMessage
    };
  }
};

// Get available departments for filtering
export const getAvailableDepartments = async (): Promise<string[]> => {
  try {
    const response = await api.get('/staff/departments');
    return response.data.data || response.data;
  } catch (error) {
    console.error('Error fetching departments:', error);
    // Return default departments if API fails
    return ['Administration', 'Finance', 'Operations', 'Sports Management'];
  }
};

// Check if staff member already has a bank account
export const hasStaffBankAccount = async (
  staffId: string
): Promise<{ hasAccount: boolean; account?: StaffBankAccount }> => {
  try {
    const accounts = await getStaffBankAccountsById(staffId);
    const activeAccount = accounts.find(account => account.status === 'active');
    
    return {
      hasAccount: !!activeAccount,
      account: activeAccount
    };
  } catch (error) {
    console.error('Error checking staff bank account:', error);
    return { hasAccount: false };
  }
};

// Add staff bank account
export const addStaffBankAccount = async (
  staffId: string,
  bankAccountData: BankAccountData,
  isReplacement: boolean = false
): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await api.post('/staff/bank-account', {
      staffId,
      bankAccountData,
      isReplacement
    });

    return {
      success: response.data.success,
      message: response.data.message || (isReplacement ? 'Bank account replaced successfully' : 'Bank account added successfully')
    };
  } catch (error) {
    console.error('Error adding staff bank account:', error);
    return {
      success: false,
      message: 'Failed to add bank account'
    };
  }
};

// Transfer to staff wallet (wallet-to-wallet transfer)
export const transferToStaffWallet = async (
  staffId: string,
  amount: number,
  description: string,
  pin: string
): Promise<{ success: boolean; message: string; transactionId?: number }> => {
  try {
    const response = await api.post('/staff/wallet-transfer', {
      staffId,
      amount,
      description,
      pin
    });

    return {
      success: response.data.success,
      message: response.data.message || 'Transfer completed successfully',
      transactionId: response.data.transactionId
    };
  } catch (error) {
    console.error('Error transferring to staff wallet:', error);

    let errorMessage = 'Failed to transfer funds to staff wallet';
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: { message?: string } } };
      errorMessage = axiosError.response?.data?.message || errorMessage;
    }

    return {
      success: false,
      message: errorMessage
    };
  }
};

// Get all staff bank accounts
export const getStaffBankAccounts = async (): Promise<StaffBankAccount[]> => {
  try {
    const response = await api.get('/staff/bank-accounts');
    const accounts = response.data.data?.accounts || [];
    
    // Process accounts to identify primary accounts for each staff member
    const staffAccounts = new Map<string, StaffBankAccount[]>();
    
    accounts.forEach((account: StaffBankAccount) => {
      const staffId = account.staffId;
      if (!staffAccounts.has(staffId)) {
        staffAccounts.set(staffId, []);
      }
      staffAccounts.get(staffId)?.push(account);
    });
    
    // Mark the first account for each staff as primary if no primary is set
    const enhancedAccounts: StaffBankAccount[] = [];
    staffAccounts.forEach(staffAccountList => {
      // Check if any account is already marked as primary
      const hasPrimary = staffAccountList.some(account => account.isPrimary);
      
      if (!hasPrimary && staffAccountList.length > 0) {
        staffAccountList[0].isPrimary = true;
      }
      enhancedAccounts.push(...staffAccountList);
    });
    
    return enhancedAccounts;
  } catch (error) {
    console.error('Error fetching staff bank accounts:', error);
    return [];
  }
};

// Get bank accounts for a specific staff member
export const getStaffBankAccountsById = async (staffId: string): Promise<StaffBankAccount[]> => {
  try {
    const response = await api.get(`/staff/bank-accounts/${staffId}`);
    return response.data.data.accounts || [];
  } catch (error) {
    console.error('Error fetching staff bank accounts:', error);
    throw error;
  }
};

// Set a bank account as primary
export const setPrimaryBankAccount = async (
  staffId: string,
  accountId: number
): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await api.put(`/staff/bank-accounts/${staffId}/primary`, {
      accountId
    });

    return {
      success: response.data.success || true,
      message: response.data.message || 'Primary bank account updated successfully'
    };
  } catch (error) {
    console.error('Error setting primary bank account:', error);
    
    let errorMessage = 'Failed to set primary bank account';
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: { message?: string } } };
      errorMessage = axiosError.response?.data?.message || errorMessage;
    }
    
    return {
      success: false,
      message: errorMessage
    };
  }
};
// Update bank account status (activate/deactivate)
export const updateBankAccountStatus = async (
  accountId: number,
  status: 'active' | 'inactive'
): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await api.put(`/staff/bank-accounts/${accountId}/status`, {
      status
    });

    return {
      success: response.data.success || true,
      message: response.data.message || `Bank account ${status === 'active' ? 'activated' : 'deactivated'} successfully`
    };
  } catch (error) {
    console.error('Error updating bank account status:', error);
    
    let errorMessage = `Failed to ${status === 'active' ? 'activate' : 'deactivate'} bank account`;
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: { message?: string } } };
      errorMessage = axiosError.response?.data?.message || errorMessage;
    }
    
    return {
      success: false,
      message: errorMessage
    };
  }
};
