import api from './api';

export interface PlaidLinkToken {
  link_token: string;
  expiration: string;
  request_id: string;
}

export interface PlaidAccount {
  account_id: string;
  balances: {
    available: number | null;
    current: number | null;
    iso_currency_code: string | null;
    limit: number | null;
    unofficial_currency_code: string | null;
  };
  mask: string;
  name: string;
  official_name: string | null;
  subtype: string | null;
  type: string;
}

export interface PlaidInstitution {
  institution_id: string;
  name: string;
}

export interface PlaidPublicTokenExchangeRequest {
  public_token: string;
  accounts: PlaidAccount[];
  institution: PlaidInstitution;
  link_session_id: string;
}

export interface PlaidAccessTokenResponse {
  access_token: string;
  item_id: string;
  request_id: string;
}

export interface ConnectedBankAccount {
  id: string;
  account_id: string;
  name: string;
  official_name: string | null;
  mask: string;
  type: string;
  subtype: string | null;
  institution_name: string;
  balance: {
    available: number | null;
    current: number | null;
    currency: string | null;
  };
  is_primary: boolean;
  connected_at: string;
  stripe_bank_account_id?: string | null;
  has_stripe_integration?: boolean;
}

// Real Plaid API implementation - no mock data needed

export const createLinkToken = async (): Promise<PlaidLinkToken> => {
  try {
    const response = await api.post('/plaid/link-token', {
      client_name: import.meta.env.VITE_PLAID_CLIENT_NAME || 'PayConnect',
      country_codes: (import.meta.env.VITE_PLAID_COUNTRY_CODES || 'US').split(','),
      language: 'en',
      products: (import.meta.env.VITE_PLAID_PRODUCTS || 'transactions,auth').split(','),
      user: {
        client_user_id: 'user-' + Date.now() // In production, use actual user ID
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error creating link token:', error);
    throw error;
  }
};

export const exchangePublicToken = async (data: PlaidPublicTokenExchangeRequest): Promise<PlaidAccessTokenResponse> => {
  try {
    const response = await api.post('/plaid/exchange-public-token', {
      public_token: data.public_token,
      accounts: data.accounts,
      institution: data.institution,
      link_session_id: data.link_session_id
    });
    return response.data;
  } catch (error) {
    console.error('Error exchanging public token:', error);
    throw error;
  }
};

export const getConnectedAccounts = async (): Promise<ConnectedBankAccount[]> => {
  try {
    const response = await api.get('/plaid/accounts');
    return response.data;
  } catch (error) {
    console.error('Error fetching connected accounts:', error);
    // If API is not available, return empty array instead of mock data
    return [];
  }
};

export const disconnectAccount = async (accountId: string): Promise<{ success: boolean }> => {
  try {
    const response = await api.delete(`/plaid/accounts/${accountId}`);
    return response.data;
  } catch (error) {
    console.error('Error disconnecting account:', error);
    throw error;
  }
};

export const setPrimaryAccount = async (accountId: string): Promise<{ success: boolean }> => {
  try {
    const response = await api.patch(`/plaid/accounts/${accountId}/set-primary`);
    return response.data;
  } catch (error) {
    console.error('Error setting primary account:', error);
    throw error;
  }
};
