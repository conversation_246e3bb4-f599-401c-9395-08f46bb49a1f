import React from 'react';

interface SkeletonLoaderProps {
  type?: 'table' | 'card' | 'summary' | 'detail';
  rows?: number;
  className?: string;
}

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({ 
  type = 'table', 
  rows = 5, 
  className = '' 
}) => {
  const baseSkeletonClass = "animate-pulse bg-gray-200 rounded";

  if (type === 'summary') {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
        {[...Array(4)].map((_, index) => (
          <div key={index} className="bg-white rounded-xl shadow-sm border p-6">
            <div className="flex items-center justify-between mb-4">
              <div className={`${baseSkeletonClass} h-4 w-24`}></div>
              <div className={`${baseSkeletonClass} h-8 w-8 rounded-full`}></div>
            </div>
            <div className={`${baseSkeletonClass} h-8 w-20 mb-2`}></div>
            <div className={`${baseSkeletonClass} h-4 w-16`}></div>
          </div>
        ))}
      </div>
    );
  }

  if (type === 'table') {
    return (
      <div className={`bg-white rounded-xl shadow-sm border ${className}`}>
        {/* Table Header */}
        <div className="bg-gray-50 px-6 py-3 border-b">
          <div className="grid grid-cols-8 gap-4">
            {[...Array(8)].map((_, index) => (
              <div key={index} className={`${baseSkeletonClass} h-4`}></div>
            ))}
          </div>
        </div>
        
        {/* Table Rows */}
        {[...Array(rows)].map((_, rowIndex) => (
          <div key={rowIndex} className="px-6 py-4 border-b border-gray-100 last:border-b-0">
            <div className="grid grid-cols-8 gap-4 items-center">
              <div className="col-span-2">
                <div className={`${baseSkeletonClass} h-4 w-full mb-2`}></div>
                <div className={`${baseSkeletonClass} h-3 w-3/4`}></div>
              </div>
              <div className={`${baseSkeletonClass} h-4 w-full`}></div>
              <div className={`${baseSkeletonClass} h-4 w-full`}></div>
              <div className={`${baseSkeletonClass} h-4 w-full`}></div>
              <div className={`${baseSkeletonClass} h-4 w-full`}></div>
              <div className={`${baseSkeletonClass} h-4 w-full`}></div>
              <div className="flex space-x-2">
                <div className={`${baseSkeletonClass} h-6 w-6 rounded`}></div>
                <div className={`${baseSkeletonClass} h-6 w-6 rounded`}></div>
                <div className={`${baseSkeletonClass} h-6 w-6 rounded`}></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (type === 'detail') {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* Header */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between mb-4">
            <div className={`${baseSkeletonClass} h-6 w-32`}></div>
            <div className={`${baseSkeletonClass} h-6 w-16 rounded-full`}></div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className={`${baseSkeletonClass} h-4 w-full`}></div>
            <div className={`${baseSkeletonClass} h-4 w-full`}></div>
          </div>
        </div>

        {/* Details */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className={`${baseSkeletonClass} h-5 w-24 mb-4`}></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="flex items-center space-x-4">
                <div className={`${baseSkeletonClass} h-12 w-12 rounded-full`}></div>
                <div className="flex-1 space-y-2">
                  <div className={`${baseSkeletonClass} h-4 w-3/4`}></div>
                  <div className={`${baseSkeletonClass} h-3 w-1/2`}></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Items */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className={`${baseSkeletonClass} h-5 w-32 mb-4`}></div>
          <div className="space-y-3">
            {[...Array(2)].map((_, index) => (
              <div key={index} className="flex justify-between items-center py-2">
                <div className="flex-1">
                  <div className={`${baseSkeletonClass} h-4 w-3/4 mb-2`}></div>
                  <div className={`${baseSkeletonClass} h-3 w-1/2`}></div>
                </div>
                <div className={`${baseSkeletonClass} h-4 w-16`}></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Default card type
  return (
    <div className={`bg-white rounded-xl shadow-sm border p-6 ${className}`}>
      <div className={`${baseSkeletonClass} h-6 w-3/4 mb-4`}></div>
      <div className={`${baseSkeletonClass} h-4 w-full mb-2`}></div>
      <div className={`${baseSkeletonClass} h-4 w-2/3`}></div>
    </div>
  );
};

export default SkeletonLoader;