import React, { useState, useEffect, useRef, useCallback } from 'react';
import TableRow from './TableRow';
import { AwaitingPayment, getAwaitingPayments, updatePaymentStatus } from '../../services/awaitingPayments';
import EmptyState from '../common/EmptyState';
import PaymentLoader from '../common/PaymentLoader';
import StaffWalletTransferModal from '../Staff/StaffWalletTransferModal';

interface PaymentsTableProps {
  className?: string;
  search: string;
  onViewDetails: (id: string) => void;
  selectedGame?: string | null;
  selectedSeason?: string | null;
  selectedGroup?: string | null;
}

const PaymentsTable: React.FC<PaymentsTableProps> = ({ className, search, onViewDetails, selectedGame, selectedSeason, selectedGroup }) => {
  const [payments, setPayments] = useState<AwaitingPayment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  // const [totalRecords, setTotalRecords] = useState(0);
  const pageSize = 10;
  const observer = useRef<IntersectionObserver | null>(null);
  const lastRowRef = useRef<HTMLTableRowElement | null>(null);

  // Wallet transfer modal state
  const [showWalletTransferModal, setShowWalletTransferModal] = useState(false);
  const [selectedPaymentForTransfer, setSelectedPaymentForTransfer] = useState<AwaitingPayment | null>(null);

  // Reset payments and page when search or filters change
  useEffect(() => {
    setPayments([]);
    setPage(1);
    setHasMore(true);
    // setTotalRecords(0);
  }, [search, selectedGame, selectedSeason, selectedGroup]);

  useEffect(() => {
    fetchPayments(page);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, search, selectedGame, selectedSeason, selectedGroup]);

  const fetchPayments = async (pageToFetch: number) => {
    try {
      if (pageToFetch === 1) setLoading(true);
      else setIsFetchingMore(true);
      const data = await getAwaitingPayments({
        game_id: selectedGame,
        season_id: selectedSeason,
        group_id: selectedGroup,
        search: search,
        page: pageToFetch,
        pageSize: pageSize,
      });

      console.log(data, 'data')
      const newPayments = data.data || [];
      setPayments(prev => pageToFetch === 1 ? newPayments : [...prev, ...newPayments]);
      setHasMore((pageToFetch - 1) * pageSize + newPayments.length < (data.totalRecords || 0));
      setError(null);
    } catch (err) {
      setError('Failed to fetch payments');
    } finally {
      setLoading(false);
      setIsFetchingMore(false);
    }
  };

  // Infinite scroll: observe last row
  const lastRowElementRef = useCallback((node: HTMLTableRowElement | null) => {
    if (loading || isFetchingMore || !hasMore) return;
    if (observer.current) observer.current.disconnect();
    observer.current = new window.IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        setPage(prev => prev + 1);
      }
    });
    if (node) observer.current.observe(node);
  }, [loading, isFetchingMore, hasMore]);

  const handleStatusUpdate = async (id: string, newStatus: AwaitingPayment['status']) => {
    try {
      const updatedPayment = await updatePaymentStatus(id, newStatus);
      setPayments(payments.map(p => p.id === id ? updatedPayment : p));
    } catch (err) {
      setError('Failed to update payment status');
    }
  };

  const handleWalletTransfer = (payment: AwaitingPayment) => {
    setSelectedPaymentForTransfer(payment);
    setShowWalletTransferModal(true);
  };

  const handleWalletTransferSuccess = () => {
    // Refresh the payments data after successful transfer
    setPayments([]);
    setPage(1);
    setHasMore(true);
  };

  if (loading && payments.length === 0) {
    return (
      <div className="flex items-center justify-center p-8 [min-height:491px]">
        <PaymentLoader
          type="wallet"
          message="Loading payment records..."
          size="medium"
          showQuotes={true}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        {error}
      </div>
    );
  }

  const hasSearch = search.trim() !== '';
  const showEmptyState = payments.length === 0;

  if (showEmptyState) {
    return (
      <div className={`rounded-md border border-gray-200 ${className}`}>
        <EmptyState
          type={hasSearch ? 'search' : 'payments'}
          title={hasSearch ? 'No payments found' : undefined}
          description={hasSearch ? 'Try adjusting your search criteria.' : undefined}
          onAction={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <div className={`overflow-x-auto rounded-md ${className}`}>
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-[#404040] ">
          <tr>

            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              League / Season
            </th>


            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Team Name
            </th>
            {/* <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Season
            </th> */}
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              $Expected Amount
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              $Received Amount
            </th>

            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Due Date
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Number of Playera
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Status
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Action
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {payments.map((payment, idx) => {
            const isLast = idx === payments.length - 1;
            return (
              <TableRow
                key={payment.id}
                {...payment}
                onStatusUpdate={handleStatusUpdate}
                onViewDetails={onViewDetails}
                onWalletTransfer={handleWalletTransfer}
                ref={isLast ? lastRowElementRef : undefined}
              />
            );
          })}
        </tbody>
      </table>
      {isFetchingMore && (
        <div className="flex items-center justify-center py-4">
          <PaymentLoader type="wallet" message="Loading more..." size="small" showQuotes={false} />
        </div>
      )}

      {/* Wallet Transfer Modal */}
      <StaffWalletTransferModal
        isOpen={showWalletTransferModal}
        onClose={() => setShowWalletTransferModal(false)}
        staffId={selectedPaymentForTransfer?.id || ''}
        staffName={selectedPaymentForTransfer?.payerName || ''}
        onSuccess={handleWalletTransferSuccess}
      />
    </div>
  );
};

export default PaymentsTable;
