import React, { useState, useEffect } from 'react';
import TableRow from './TableRow';
import { Invoice, getInvoices, updateInvoiceStatus, getInvoiceById } from '../../services/invoices';
import EmptyState from '../common/EmptyState';
import PaymentLoader from '../common/PaymentLoader';
import DeleteConfirmModal from './DeleteConfirmModal';
import { downloadInvoicePDF } from '../../utils/invoicePdfGenerator';

interface InvoicesTableProps {
  className?: string;
  search: string;
  statusFilter: string;
  onViewDetails: (id: string) => void;
  onDataChange?: () => void;
  onNotification?: (type: 'success' | 'error' | 'info', message: string) => void;
}

const InvoicesTable: React.FC<InvoicesTableProps> = ({ 
  className, 
  search, 
  statusFilter,
  onViewDetails,
  onDataChange,
  onNotification
}) => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchInvoices();
  }, [search, statusFilter]);

  const fetchInvoices = async () => {
    try {
      setLoading(true);
      const data = await getInvoices({
        search: search || undefined,
        status: statusFilter !== 'All' ? statusFilter : undefined
      });
      setInvoices(data.data);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch transactions';
      setError(errorMessage);
      console.error('Error fetching invoices:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (id: string, newStatus: Invoice['status']) => {
    try {
      const updatedInvoice = await updateInvoiceStatus(id, newStatus);
      setInvoices(invoices.map(invoice => 
        invoice.id === id ? updatedInvoice : invoice
      ));
      onDataChange?.(); // Refresh summary data
    } catch (err) {
      setError('Failed to update invoice status');
    }
  };

  const handleSendInvoice = (id: string) => {
    handleStatusUpdate(id, 'Sent');
  };

  const handleDownloadInvoice = async (id: string) => {
    try {
      // Get the full invoice data
      const invoice = await getInvoiceById(id);
      // Generate and download the PDF
      await downloadInvoicePDF(invoice);
      onNotification?.('success', 'Transaction receipt downloaded successfully');
    } catch (error) {
      console.error('Error downloading invoice PDF:', error);
      onNotification?.('error', 'Failed to download PDF. Please try again.');
    }
  };

  const handleEditInvoice = (id: string) => {
    // For transaction history, editing is not applicable
    onNotification?.('info', 'Transaction records cannot be edited. This is your transaction history.');
  };


  const [showModal, setShowModal] = useState(false);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);

  const handleDeleteClick = (id: string) => {
    setSelectedInvoiceId(id);
    setShowModal(true);
  };

  const handleDeleteInvoice = () => {
    if (selectedInvoiceId) {
      // For transaction history, deletion is not applicable
      onNotification?.('info', 'Transaction records cannot be deleted. This is your permanent transaction history.');
      setSelectedInvoiceId(null);
      setShowModal(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <PaymentLoader
          type="processing"
          message="Loading transactions..."
          size="medium"
          showQuotes={true}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <div className="flex items-center justify-between">
          <span>{error}</span>
          <button
            onClick={fetchInvoices}
            className="ml-4 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Check if we should show empty state
  const hasSearch = search.trim() !== '';
  const hasFilter = statusFilter !== 'All';
  const showEmptyState = invoices.length === 0;

  if (showEmptyState) {
    return (
      <div className={`rounded-md border border-gray-200 ${className}`}>
        <EmptyState
          type={hasSearch || hasFilter ? 'search' : 'invoices'}
          title={hasSearch || hasFilter ? 'No transactions found' : 'No transactions yet'}
          description={
            hasSearch || hasFilter 
              ? 'Try adjusting your search criteria or filters.' 
              : 'Your transaction history will appear here once you make payments or receive funds.'
          }
          onAction={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <div className={`overflow-x-auto rounded-md ${className}`}>
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-[#404040]">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Transaction ID
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Description
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Type
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Amount
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Date
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Status
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {invoices.map((invoice) => (
            <TableRow
          key={invoice.id}
  {...invoice}
  onStatusUpdate={handleStatusUpdate}
  onViewDetails={onViewDetails}
  onSendInvoice={handleSendInvoice}
  onDownloadInvoice={handleDownloadInvoice}
  onEditInvoice={handleEditInvoice}
  onDeleteInvoice={handleDeleteClick} // ✅ This shows the modal
            />
          ))}
        </tbody>
      </table>

      {/* Delete Modal */}
      <DeleteConfirmModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onConfirm={handleDeleteInvoice}
        invoiceId={selectedInvoiceId}
      />
    </div>
  );
};

export default InvoicesTable;
