import React, { useState } from 'react';
import { Download, FileText, Table, Code, X } from 'lucide-react';
import { exportTransactions, generateSummaryReport } from '../../utils/exportUtils';

interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentFilters: {
    status?: string;
    search?: string;
  };
  transactionCount: number;
  onSuccess?: (message: string) => void;
  onError?: (message: string) => void;
}

const ExportModal: React.FC<ExportModalProps> = ({
  isOpen,
  onClose,
  currentFilters,
  transactionCount,
  onSuccess,
  onError
}) => {
  const [selectedFormat, setSelectedFormat] = useState<'csv' | 'excel' | 'json'>('csv');
  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState<string | null>(null);

  if (!isOpen) return null;

  const handleExport = async () => {
    try {
      setIsExporting(true);
      setExportError(null);
      
      await exportTransactions(selectedFormat, currentFilters);
      
      // Show success message via callback
      const successMessage = `Successfully exported ${transactionCount} transactions as ${selectedFormat.toUpperCase()}`;
      if (onSuccess) {
        onSuccess(successMessage);
      }
      onClose();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Export failed';
      setExportError(errorMessage);
    } finally {
      setIsExporting(false);
    }
  };

  const getFilterDescription = () => {
    const parts = [];
    if (currentFilters.status && currentFilters.status !== 'All') {
      parts.push(`Status: ${currentFilters.status}`);
    }
    if (currentFilters.search) {
      parts.push(`Search: "${currentFilters.search}"`);
    }
    return parts.length > 0 ? parts.join(', ') : 'All transactions';
  };

  const formatOptions = [
    {
      value: 'csv' as const,
      label: 'CSV',
      description: 'Comma-separated values, compatible with Excel and Google Sheets',
      icon: <Table className="w-5 h-5" />,
      extension: '.csv'
    },
    {
      value: 'excel' as const,
      label: 'Excel',
      description: 'Microsoft Excel format with enhanced formatting',
      icon: <FileText className="w-5 h-5" />,
      extension: '.xlsx'
    },
    {
      value: 'json' as const,
      label: 'JSON',
      description: 'JavaScript Object Notation, for developers and data analysis',
      icon: <Code className="w-5 h-5" />,
      extension: '.json'
    }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Export Transactions</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Export Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 text-blue-800 mb-2">
              <Download className="w-4 h-4" />
              <span className="font-medium">Export Details</span>
            </div>
            <div className="text-sm text-blue-700">
              <p><strong>Transactions:</strong> {transactionCount}</p>
              <p><strong>Filters:</strong> {getFilterDescription()}</p>
            </div>
          </div>

          {/* Format Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Select Export Format
            </label>
            <div className="space-y-3">
              {formatOptions.map((option) => (
                <label
                  key={option.value}
                  className={`flex items-start space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedFormat === option.value
                      ? 'border-orange-500 bg-orange-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="format"
                    value={option.value}
                    checked={selectedFormat === option.value}
                    onChange={(e) => setSelectedFormat(e.target.value as 'csv' | 'excel' | 'json')}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      {option.icon}
                      <span className="font-medium text-gray-900">
                        {option.label} {option.extension}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      {option.description}
                    </p>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Error Message */}
          {exportError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="text-red-800 text-sm">
                <strong>Export Error:</strong> {exportError}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            disabled={isExporting}
          >
            Cancel
          </button>
          <button
            onClick={handleExport}
            disabled={isExporting || transactionCount === 0}
            className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                <span>Exporting...</span>
              </>
            ) : (
              <>
                <Download className="w-4 h-4" />
                <span>Export {transactionCount} Transactions</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExportModal;