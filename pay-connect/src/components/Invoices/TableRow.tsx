import React from 'react';
import { Invoice } from '../../services/invoices';
import { Eye, Download } from 'lucide-react';

interface TableRowProps extends Invoice {
  onStatusUpdate: (id: string, status: Invoice['status']) => void;
  onViewDetails: (id: string) => void;
  onSendInvoice?: (id: string) => void;
  onDownloadInvoice?: (id: string) => void;
  onEditInvoice?: (id: string) => void;
  onDeleteInvoice?: (id: string) => void;
}

const TableRow: React.FC<TableRowProps> = ({
  id,
  invoiceNumber,
  clientName,
  clientEmail,
  clientPhone,
  league,
  season,
  amount,
  issueDate,
  dueDate,
  status,
  avatar,
  onStatusUpdate,
  onViewDetails,
  onSendInvoice,
  onDownloadInvoice,
  onEditInvoice,
  onDeleteInvoice
}) => {
  const getStatusColor = (status: Invoice['status']) => {
    switch (status) {
      case 'Paid':
        return 'bg-green-100 text-green-800';
      case 'Sent':
        return 'bg-blue-100 text-blue-800';
      case 'Draft':
        return 'bg-gray-100 text-gray-800';
      case 'Overdue':
        return 'bg-red-100 text-red-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleStatusChange = (newStatus: Invoice['status']) => {
    onStatusUpdate(id, newStatus);
  };

  const getAvailableActions = () => {
    const actions = [];
    
    // View Details - always available for all transactions
    actions.push(
      <button
        key="view"
        onClick={() => onViewDetails(id)}
        className="text-orange-500 hover:text-orange-600 ml-3 mt-1 p-2 rounded-full bg-orange-50 hover:bg-orange-100 transition"
        title="View Transaction Details"
      >
        <Eye size={16} />
      </button>
    );

    // Download Receipt - available for completed transactions
    if (['Paid'].includes(status)) {
      actions.push(
        <button
          key="download"
          onClick={() => onDownloadInvoice?.(id)}
          className="text-purple-500 hover:text-purple-600 ml-3 mt-1 p-2 rounded-full bg-purple-50 hover:bg-purple-100 transition"
          title="Download Receipt"
        >
          <Download size={16} />
        </button>
      );
    }

    return actions;
  };

  // Determine transaction type based on amount and context
  const getTransactionType = () => {
    if (amount > 0) {
      return 'Credit'; // Money received
    } else {
      return 'Debit'; // Money paid out
    }
  };

  const getTransactionTypeColor = (type: string) => {
    return type === 'Credit' 
      ? 'bg-green-100 text-green-800' 
      : 'bg-blue-100 text-blue-800';
  };

  return (
    <tr className="hover:bg-gray-50 transition-colors">
      {/* Transaction ID */}
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm font-medium text-gray-900">{invoiceNumber}</div>
        <div className="text-sm text-gray-500">{formatDate(issueDate)}</div>
      </td>
      
      {/* Description */}
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm font-medium text-gray-900">{clientName}</div>
        <div className="text-sm text-gray-500">{league} - {season}</div>
      </td>
      
      {/* Transaction Type */}
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTransactionTypeColor(getTransactionType())}`}>
          {getTransactionType()}
        </span>
      </td>
      
      {/* Amount */}
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
        ${Math.abs(amount).toLocaleString()}
      </td>
      
      {/* Date */}
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {formatDate(issueDate)}
      </td>
      
      {/* Status */}
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(status)}`}>
          {status}
        </span>
      </td>
      
      {/* Actions */}
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <div className="flex items-center space-x-2">
          {getAvailableActions()}
        </div>
      </td>
    </tr>
  );
};

export default TableRow;
