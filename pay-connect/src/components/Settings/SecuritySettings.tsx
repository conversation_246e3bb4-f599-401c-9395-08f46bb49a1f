import React, { useState, useEffect } from 'react';
import { Shield, Lock, Wallet } from 'lucide-react';
import { SecuritySettings as SecuritySettingsType } from '../../services/settings';
import WalletPinModal from './WalletPinModal';
import { WalletPinService } from '../../services/walletPinService';

interface SecuritySettingsProps {
  settings: SecuritySettingsType;
  onUpdate: (settings: SecuritySettingsType) => void;
  isLoading?: boolean;
}

const SecuritySettings: React.FC<SecuritySettingsProps> = ({ settings, onUpdate, isLoading }) => {
  // Note: settings, onUpdate, and isLoading props are kept for compatibility
  // but only used for potential future expansion of security features
  const [showPinModal, setShowPinModal] = useState(false);
  const [pinModalMode, setPinModalMode] = useState<'change' | 'reset'>('change');
  const [pinStatus, setPinStatus] = useState<{
    hasPinSetup: boolean;
    lastChanged?: string;
    isLoading: boolean;
  }>({
    hasPinSetup: false,
    isLoading: true
  });

  // Fetch PIN status on component mount
  useEffect(() => {
    fetchPinStatus();
  }, []);

  const fetchPinStatus = async () => {
    try {
      const result = await WalletPinService.getPinStatus();
      console.log('PIN Status API Result:', result); // Debug log
      
      if (result.success && result.data) {
        console.log('Setting PIN Status:', {
          hasPinSetup: result.data.hasPinSetup,
          lastChanged: result.data.lastChanged
        }); // Debug log
        
        setPinStatus({
          hasPinSetup: result.data.hasPinSetup,
          lastChanged: result.data.lastChanged,
          isLoading: false
        });
      } else {
        console.log('PIN Status API failed or no data:', result); // Debug log
        setPinStatus(prev => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      console.error('Error fetching PIN status:', error);
      setPinStatus(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handlePinModalOpen = (mode: 'change' | 'reset') => {
    setPinModalMode(mode);
    setShowPinModal(true);
  };

  const handlePinModalClose = () => {
    setShowPinModal(false);
  };

  const handlePinModalSuccess = () => {
    // Refresh PIN status after successful change/reset
    fetchPinStatus();
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-semibold text-gray-900">Security Settings</h3>
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
              <Shield className="w-5 h-5 text-red-600" />
            </div>
          </div>
          <p className="text-sm text-gray-500">Configure security and access controls</p>
        </div>
      </div>

      <div className="space-y-6">
        {/* 
        COMMENTED OUT - Other Security Features
        
        Session Management, Authentication, Login Security, Security Recommendations, 
        and Security Profiles sections have been commented out to focus only on 
        Wallet PIN Management functionality.
        
        Uncomment these sections if you need the full security settings.
        */}

        {/* Wallet PIN Management */}
        <div className="border-t pt-6">
          <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center">
            <Wallet className="w-4 h-4 mr-2" />
            Wallet PIN Management
          </h4>
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h5 className="text-sm font-medium text-gray-900">Wallet PIN Status</h5>
                  <p className="text-sm text-gray-500">
                    {pinStatus.isLoading 
                      ? 'Loading PIN status...' 
                      : pinStatus.hasPinSetup 
                        ? 'Your wallet PIN is set up and active'
                        : 'No wallet PIN configured'
                    }
                  </p>
                </div>
                <div className="flex items-center">
                  {!pinStatus.isLoading && (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      pinStatus.hasPinSetup 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {pinStatus.hasPinSetup ? 'Active' : 'Not Set'}
                    </span>
                  )}
                </div>
              </div>

              {pinStatus.lastChanged && (
                <div className="mb-4">
                  <p className="text-xs text-gray-500">
                    Last changed: {new Date(pinStatus.lastChanged).toLocaleDateString()}
                  </p>
                </div>
              )}

              <div className="flex space-x-3">
                <button
                  onClick={() => handlePinModalOpen('change')}
                  disabled={pinStatus.isLoading || !pinStatus.hasPinSetup}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
                >
                  Change PIN
                </button>
                <button
                  onClick={() => handlePinModalOpen('reset')}
                  disabled={pinStatus.isLoading}
                  className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
                >
                  Reset PIN
                </button>
              </div>
            </div>

            {/* PIN Security Tips */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Lock className="w-5 h-5 text-blue-600 mt-0.5" />
                <div className="flex-1">
                  <h5 className="text-sm font-medium text-blue-900 mb-2">Wallet PIN Security</h5>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Your PIN protects all wallet transactions</li>
                    <li>• Use a unique PIN different from other accounts</li>
                    <li>• Change your PIN regularly for better security</li>
                    <li>• Never share your PIN with anyone</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Wallet PIN Modal */}
      <WalletPinModal
        isOpen={showPinModal}
        onClose={handlePinModalClose}
        onSuccess={handlePinModalSuccess}
        mode={pinModalMode}
      />
    </div>
  );
};

export default SecuritySettings;