import React, { useState, useEffect } from 'react';
import { X, Lock, Mail, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import PinInput from './PinInput';
import { WalletPinService } from '../../services/walletPinService';
import { validatePin, validatePinConfirmation, validateOtp, PinAttemptTracker } from '../../utils/pinValidation';

interface WalletPinModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  mode: 'change' | 'reset';
}

type Step = 'current-pin' | 'otp-verification' | 'new-pin' | 'success';

const WalletPinModal: React.FC<WalletPinModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  mode
}) => {
  const [currentStep, setCurrentStep] = useState<Step>(mode === 'change' ? 'current-pin' : 'otp-verification');
  const [currentPin, setCurrentPin] = useState('');
  const [newPin, setNewPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [otp, setOtp] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [otpSent, setOtpSent] = useState(false);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setCurrentStep(mode === 'change' ? 'current-pin' : 'otp-verification');
      setCurrentPin('');
      setNewPin('');
      setConfirmPin('');
      setOtp('');
      setError('');
      setOtpSent(false);
    }
  }, [isOpen, mode]);

  const handleClose = () => {
    setCurrentPin('');
    setNewPin('');
    setConfirmPin('');
    setOtp('');
    setError('');
    setOtpSent(false);
    onClose();
  };

  const handleCurrentPinSubmit = async () => {
    // Check rate limiting first
    const attemptData = PinAttemptTracker.getAttemptData();
    if (attemptData.isLocked) {
      const remainingTime = PinAttemptTracker.getRemainingLockoutTime();
      setError(`Too many failed attempts. Please try again in ${PinAttemptTracker.formatLockoutTime(remainingTime)}.`);
      return;
    }

    // Validate PIN format
    const validation = validatePin(currentPin);
    if (!validation.isValid) {
      setError(validation.errors[0]);
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await WalletPinService.verifyCurrentPin({ pin: currentPin });
      
      if (result.success) {
        // Record successful attempt
        PinAttemptTracker.recordSuccessfulAttempt();
        
        // Send OTP for PIN change
        const otpResult = await WalletPinService.sendChangeOtp();
        if (otpResult.success) {
          setOtpSent(true);
          setCurrentStep('otp-verification');
        } else {
          setError(otpResult.message);
        }
      } else {
        // Record failed attempt
        const attemptResult = PinAttemptTracker.recordFailedAttempt();
        
        if (attemptResult.isLocked) {
          setError(`Too many failed attempts. Account locked for ${PinAttemptTracker.formatLockoutTime(PinAttemptTracker.getRemainingLockoutTime())}.`);
        } else {
          setError(`${result.message} (${attemptResult.remainingAttempts} attempts remaining)`);
        }
      }
    } catch (error) {
      setError('Failed to verify PIN. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendOtp = async () => {
    setIsLoading(true);
    setError('');

    try {
      const result = mode === 'change' 
        ? await WalletPinService.sendChangeOtp()
        : await WalletPinService.sendResetOtp();
      
      if (result.success) {
        setOtpSent(true);
        setError('');
      } else {
        setError(result.message);
      }
    } catch (error) {
      setError('Failed to send OTP. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOtpSubmit = async () => {
    // Validate OTP format
    const validation = validateOtp(otp);
    if (!validation.isValid) {
      setError(validation.errors[0]);
      return;
    }

    setCurrentStep('new-pin');
    setError('');
  };

  const handleNewPinSubmit = async () => {
    // Validate new PIN
    const newPinValidation = validatePin(newPin);
    if (!newPinValidation.isValid) {
      setError(newPinValidation.errors[0]);
      return;
    }

    // Validate PIN confirmation
    const confirmValidation = validatePinConfirmation(newPin, confirmPin);
    if (!confirmValidation.isValid) {
      setError(confirmValidation.errors[0]);
      return;
    }

    // Check if new PIN is different from current PIN (for change mode)
    if (mode === 'change' && newPin === currentPin) {
      setError('New PIN must be different from current PIN');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = mode === 'change'
        ? await WalletPinService.changePin({
            currentPin,
            newPin,
            confirmPin,
            otp
          })
        : await WalletPinService.resetPin({
            newPin,
            confirmPin,
            otp
          });

      if (result.success) {
        // Record successful PIN change for audit
        console.log(`Wallet PIN ${mode} successful for user`);
        
        setCurrentStep('success');
        setTimeout(() => {
          handleClose();
          onSuccess();
        }, 2000);
      } else {
        setError(result.message);
      }
    } catch (error) {
      console.error(`Failed to ${mode} PIN:`, error);
      setError(`Failed to ${mode} PIN. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };

  const renderCurrentPinStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Lock className="w-8 h-8 text-blue-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Verify Current PIN
        </h3>
        <p className="text-gray-600">
          Please enter your current wallet PIN to continue
        </p>
      </div>

      <PinInput
        value={currentPin}
        onChange={setCurrentPin}
        placeholder="Current PIN"
        error={error}
        autoFocus
        label="Current PIN"
      />

      <div className="flex space-x-3">
        <button
          onClick={handleClose}
          className="flex-1 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handleCurrentPinSubmit}
          disabled={isLoading || currentPin.length < 4}
          className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
        >
          {isLoading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            'Verify PIN'
          )}
        </button>
      </div>
    </div>
  );

  const renderOtpStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Mail className="w-8 h-8 text-green-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Verify Your Identity
        </h3>
        <p className="text-gray-600">
          {otpSent 
            ? 'We\'ve sent a verification code to your registered email address'
            : 'We need to verify your identity before proceeding'
          }
        </p>
      </div>

      {!otpSent ? (
        <div className="text-center">
          <button
            onClick={handleSendOtp}
            disabled={isLoading}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors flex items-center justify-center mx-auto"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin mr-2" />
            ) : (
              <Mail className="w-4 h-4 mr-2" />
            )}
            Send Verification Code
          </button>
        </div>
      ) : (
        <>
          <PinInput
            value={otp}
            onChange={setOtp}
            placeholder="Enter OTP"
            error={error}
            autoFocus
            label="Verification Code"
            maxLength={6}
            showToggle={false}
          />

          <div className="text-center">
            <button
              onClick={handleSendOtp}
              disabled={isLoading}
              className="text-sm text-blue-600 hover:text-blue-700 disabled:opacity-50"
            >
              Didn't receive the code? Resend
            </button>
          </div>
        </>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
            <p className="text-sm text-red-600">{error}</p>
          </div>
        </div>
      )}

      <div className="flex space-x-3">
        <button
          onClick={handleClose}
          className="flex-1 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        {otpSent && (
          <button
            onClick={handleOtpSubmit}
            disabled={isLoading || otp.length < 4}
            className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Continue
          </button>
        )}
      </div>
    </div>
  );

  const renderNewPinStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Lock className="w-8 h-8 text-purple-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {mode === 'change' ? 'Set New PIN' : 'Create New PIN'}
        </h3>
        <p className="text-gray-600">
          Choose a secure PIN that you'll remember
        </p>
      </div>

      <div className="space-y-4">
        <PinInput
          value={newPin}
          onChange={setNewPin}
          placeholder="New PIN"
          autoFocus
          label="New PIN"
        />

        <PinInput
          value={confirmPin}
          onChange={setConfirmPin}
          placeholder="Confirm PIN"
          label="Confirm New PIN"
          error={confirmPin.length > 0 && newPin !== confirmPin ? 'PINs do not match' : ''}
        />
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
            <p className="text-sm text-red-600">{error}</p>
          </div>
        </div>
      )}

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <h4 className="text-sm font-medium text-blue-900 mb-2">PIN Security Tips:</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Use a unique PIN that's different from other accounts</li>
          <li>• Avoid easily guessable numbers like birthdays</li>
          <li>• Don't share your PIN with anyone</li>
        </ul>
      </div>

      <div className="flex space-x-3">
        <button
          onClick={handleClose}
          className="flex-1 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handleNewPinSubmit}
          disabled={isLoading || newPin.length < 4 || newPin !== confirmPin}
          className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
        >
          {isLoading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            `${mode === 'change' ? 'Change' : 'Set'} PIN`
          )}
        </button>
      </div>
    </div>
  );

  const renderSuccessStep = () => (
    <div className="space-y-6 text-center">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
        <CheckCircle className="w-8 h-8 text-green-600" />
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          PIN {mode === 'change' ? 'Changed' : 'Set'} Successfully!
        </h3>
        <p className="text-gray-600">
          Your wallet PIN has been {mode === 'change' ? 'updated' : 'created'} successfully.
        </p>
      </div>
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              {mode === 'change' ? 'Change Wallet PIN' : 'Reset Wallet PIN'}
            </h2>
            <button
              onClick={handleClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          {currentStep === 'current-pin' && renderCurrentPinStep()}
          {currentStep === 'otp-verification' && renderOtpStep()}
          {currentStep === 'new-pin' && renderNewPinStep()}
          {currentStep === 'success' && renderSuccessStep()}
        </div>
      </div>
    </div>
  );
};

export default WalletPinModal;