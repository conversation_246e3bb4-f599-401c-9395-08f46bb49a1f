import React, { useState, useRef, useEffect } from 'react';
import { Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { validatePin, getPinStrength } from '../../utils/pinValidation';

interface PinInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  autoFocus?: boolean;
  label?: string;
  maxLength?: number;
  showToggle?: boolean;
}

const PinInput: React.FC<PinInputProps> = ({
  value,
  onChange,
  placeholder = "Enter PIN",
  error,
  disabled = false,
  autoFocus = false,
  label,
  maxLength = 6,
  showToggle = true
}) => {
  const [showPin, setShowPin] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    
    // Only allow numeric input
    if (!/^\d*$/.test(newValue)) {
      return;
    }
    
    // Limit to maxLength
    if (newValue.length <= maxLength) {
      onChange(newValue);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow backspace, delete, tab, escape, enter
    if ([8, 9, 27, 13, 46].indexOf(e.keyCode) !== -1 ||
        // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
        (e.keyCode === 65 && e.ctrlKey === true) ||
        (e.keyCode === 67 && e.ctrlKey === true) ||
        (e.keyCode === 86 && e.ctrlKey === true) ||
        (e.keyCode === 88 && e.ctrlKey === true)) {
      return;
    }
    
    // Ensure that it is a number and stop the keypress
    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
      e.preventDefault();
    }
  };

  const toggleShowPin = () => {
    setShowPin(!showPin);
  };

  const validation = value.length > 0 ? validatePin(value) : { isValid: false, errors: [] };
  const pinStrength = value.length >= 4 ? getPinStrength(value) : 'weak';
  const isValid = validation.isValid;
  const hasError = error && error.length > 0;

  return (
    <div className="w-full">
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
        </label>
      )}
      
      <div className="relative">
        <input
          ref={inputRef}
          type={showPin ? "text" : "password"}
          value={value}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          disabled={disabled}
          maxLength={maxLength}
          className={`
            w-full px-4 py-3 pr-12 text-center text-lg font-mono tracking-widest
            border rounded-lg transition-all duration-200
            ${hasError 
              ? 'border-red-300 focus:border-red-500 focus:ring-red-500' 
              : isFocused || value
                ? 'border-blue-300 focus:border-blue-500 focus:ring-blue-500'
                : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
            }
            ${disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'}
            focus:outline-none focus:ring-2 focus:ring-opacity-50
          `}
          aria-invalid={hasError}
          aria-describedby={hasError ? `${label}-error` : undefined}
        />
        
        {showToggle && (
          <button
            type="button"
            onClick={toggleShowPin}
            disabled={disabled}
            className={`
              absolute right-3 top-1/2 transform -translate-y-1/2
              p-1 rounded-md transition-colors
              ${disabled 
                ? 'text-gray-400 cursor-not-allowed' 
                : 'text-gray-500 hover:text-gray-700 focus:outline-none focus:text-gray-700'
              }
            `}
            aria-label={showPin ? "Hide PIN" : "Show PIN"}
          >
            {showPin ? (
              <EyeOff className="w-5 h-5" />
            ) : (
              <Eye className="w-5 h-5" />
            )}
          </button>
        )}
      </div>

      {/* Validation feedback */}
      <div className="mt-2 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {/* PIN length indicator */}
          <div className="flex space-x-1">
            {Array.from({ length: maxLength }, (_, i) => (
              <div
                key={i}
                className={`
                  w-2 h-2 rounded-full transition-colors
                  ${i < value.length 
                    ? isValid 
                      ? 'bg-green-500' 
                      : 'bg-blue-500'
                    : 'bg-gray-300'
                  }
                `}
              />
            ))}
          </div>
          
          {/* Length indicator text */}
          <span className="text-xs text-gray-500">
            {value.length}/{maxLength}
          </span>
        </div>

        {/* PIN strength indicator */}
        {value.length >= 4 && (
          <div className="flex items-center space-x-2">
            <div className={`
              px-2 py-1 rounded text-xs font-medium
              ${pinStrength === 'strong' ? 'bg-green-100 text-green-800' :
                pinStrength === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'}
            `}>
              {pinStrength === 'strong' ? 'Strong' : 
               pinStrength === 'medium' ? 'Medium' : 'Weak'}
            </div>
            {isValid && (
              <CheckCircle className="w-4 h-4 text-green-600" />
            )}
          </div>
        )}
      </div>

      {/* Error message */}
      {hasError && (
        <p 
          id={`${label}-error`}
          className="mt-2 text-sm text-red-600"
          role="alert"
        >
          {error}
        </p>
      )}

      {/* Help text */}
      {!hasError && (
        <p className="mt-2 text-xs text-gray-500">
          Enter a {maxLength === 4 ? '4' : '4-6'} digit PIN using numbers only
        </p>
      )}
    </div>
  );
};

export default PinInput;