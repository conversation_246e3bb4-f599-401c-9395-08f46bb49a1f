import React, { useCallback, useEffect, useState } from 'react';
import { usePlaidLink } from 'react-plaid-link';
import { Plus, Loader2 } from 'lucide-react';
import { createLinkToken, exchangePublicToken, PlaidPublicTokenExchangeRequest } from '../../services/plaid';
import PaymentLoader from '../common/PaymentLoader';
import SpinnerLoader from '../SpinnerLoader';

interface PlaidLinkProps {
  onSuccess: () => void;
  onError?: (error: any) => void;
  className?: string;
}

const PlaidLink: React.FC<PlaidLinkProps> = ({ onSuccess, onError, className = '' }) => {
  const [linkToken, setLinkToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // const [isConnecting, setIsConnecting] = useState(true);

  // Initialize link token
  useEffect(() => {
    const initializeLinkToken = async () => {
      try {
        setIsLoading(true);
        const tokenData = await createLinkToken();
        setLinkToken(tokenData.link_token);
      } catch (err) {
        console.error('Error creating link token:', err);
        setError('Failed to initialize bank connection. Please try again.');
        onError?.(err);
      } finally {
        setIsLoading(false);
      }
    };

    initializeLinkToken();
  }, [onError]);

  const onPlaidSuccess = useCallback(async (public_token: string, metadata: any) => {
    try {
      setIsLoading(true);

      // Prepare data for token exchange
      const exchangeData: PlaidPublicTokenExchangeRequest = {
        public_token,
        accounts: metadata.accounts,
        institution: metadata.institution,
        link_session_id: metadata.link_session_id
      };

      // Exchange public token for access token
      await exchangePublicToken(exchangeData);

      // Stripe integration happens automatically in the backend
      console.log('Bank accounts linked successfully with automatic Stripe integration');

      // Call success callback
      onSuccess();
    } catch (err) {
      console.error('Error exchanging public token:', err);
      setError('Failed to connect bank account. Please try again.');
      onError?.(err);
    } finally {
      setIsLoading(false);
    }
  }, [onSuccess, onError]);

  const onPlaidExit = useCallback((err: any, metadata: any) => {
    if (err) {
      console.error('Plaid Link exit with error:', err);
      setError('Bank connection was cancelled or failed.');
      onError?.(err);
    }
  }, [onError]);

  const { open, ready } = usePlaidLink({
    token: linkToken,
    onSuccess: onPlaidSuccess,
    onExit: onPlaidExit,
    onEvent: (eventName, metadata) => {
      console.log('Plaid Link event:', eventName, metadata);
    },
    env: (import.meta.env.VITE_PLAID_ENV as any) || 'sandbox',
  });

  const handleOpenLink = () => {
    if (ready && linkToken) {
      setError(null);
      open();
    }
  };

  if (error) {
    return (
      <div className={`text-center py-4 ${className}`}>
        <div className="text-red-600 mb-4">{error}</div>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className={`text-center ${className}`}>
      <button
        onClick={handleOpenLink}
        disabled={!ready || isLoading || !linkToken}
        className="inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
      >
        {isLoading ? (
          <div className="flex items-center space-x-2">
            <SpinnerLoader size={20} /> {/* Reusable spinner */}
            <span>Connecting...</span>
          </div>
        ) : (
          <>
           
            <span>Connect Bank Account</span>
             <Plus size={20} />
          </>
        )}
      </button>


      <p className="text-sm text-gray-500 mt-2">
        Securely connect your bank account using Plaid
      </p>
    </div>
  );
};

export default PlaidLink;
