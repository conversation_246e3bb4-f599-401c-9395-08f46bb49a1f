import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getWallet, getWalletTransactions, Wallet as WalletType } from '../services/wallet';

interface WalletContextType {
  wallet: WalletType | null;
  isLoading: boolean;
  error: string | null;
  recentTransactions: any[];
  refreshWallet: () => Promise<void>;
  refreshTransactions: () => Promise<void>;
  updateBalance: (newBalance: number) => void;
  refreshAll: () => Promise<void>;
}

const WalletContext = createContext<WalletContextType | undefined>(undefined);

export const useWallet = () => {
  const context = useContext(WalletContext);
  if (context === undefined) {
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
};

interface WalletProviderProps {
  children: ReactNode;
}

export const WalletProvider: React.FC<WalletProviderProps> = ({ children }) => {
  const [wallet, setWallet] = useState<WalletType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);

  const refreshWallet = async () => {
    try {
      setError(null);
      const walletData = await getWallet();
      setWallet(walletData);
    } catch (err) {
      console.error('Error fetching wallet:', err);
      setError('Failed to load wallet information');
    }
  };

  const refreshTransactions = async () => {
    try {
      const transactions = await getWalletTransactions(10, 0);
      setRecentTransactions(transactions);
    } catch (err) {
      console.error('Error fetching transactions:', err);
      setRecentTransactions([]);
    }
  };

  const updateBalance = (newBalance: number) => {
    if (wallet) {
      setWallet({ ...wallet, balance: newBalance });
    }
  };

  const refreshAll = async () => {
    setIsLoading(true);
    try {
      await Promise.all([refreshWallet(), refreshTransactions()]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshAll();
  }, []);

  const value = {
    wallet,
    isLoading,
    error,
    recentTransactions,
    refreshWallet,
    refreshTransactions,
    updateBalance,
    refreshAll
  };

  return (
    <WalletContext.Provider value={value}>
      {children}
    </WalletContext.Provider>
  );
};
